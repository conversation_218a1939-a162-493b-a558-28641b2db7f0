import React, { useState } from "react";
import useLanguage from "@/hooks/useLanguage"; // Make sure this import exists

interface CalendarHeaderProps {
  currentDate: Date;
  onPrevMonth: () => void;
  onNextMonth: () => void;
  onYearChange: (year: number) => void;
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  currentDate,
  onPrevMonth,
  onNextMonth,
  onYearChange,
}) => {
  const { t, language } = useLanguage(); // Make sure lang or direction is available

  const isRTL = language === "ar"; // Adjust if your hook uses a different property

  const monthNames = [
    t("January"),
    t("February"),
    t("March"),
    t("April"),
    t("May"),
    t("June"),
    t("July"),
    t("August"),
    t("September"),
    t("October"),
    t("November"),
    t("December"),
  ];
  const month = currentDate.getMonth();
  const year = currentDate.getFullYear();
  const [isMonthDropdownOpen, setIsMonthDropdownOpen] = useState(false);

  const handleYearChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onYearChange(parseInt(event.target.value, 10));
  };

  const handleMonthChange = (monthIndex: number) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(monthIndex);
    const monthsDiff = monthIndex - month;
    if (monthsDiff > 0) {
      for (let i = 0; i < monthsDiff; i++) {
        onNextMonth();
      }
    } else if (monthsDiff < 0) {
      for (let i = 0; i < Math.abs(monthsDiff); i++) {
        onPrevMonth();
      }
    }
    setIsMonthDropdownOpen(false);
  };

  const years = Array.from({ length: 50 }, (_, i) => year - 25 + i);

  return (
    <div className="border-b border-gray-200 bg-white p-3 shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:p-4 lg:p-6">
      <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0">
        {/* Navigation Button - Previous */}
        <button
          onClick={isRTL ? onNextMonth : onPrevMonth}
          className="rounded-full p-2 text-gray-700 transition duration-200 ease-in-out hover:bg-gray-100 hover:text-black dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white sm:p-3"
          aria-label={t("Previous month")}
        >
          {/* Right arrow for RTL, left arrow for LTR */}
          {isRTL ? (
            // Right arrow
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 sm:h-6 sm:w-6"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4-4a1 1 0 01-1.414 0z"
                clipRule="evenodd"
              />
            </svg>
          ) : (
            // Left arrow
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 sm:h-6 sm:w-6"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </button>

        {/* Controls Container */}
        <div className="flex flex-col items-center space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0">
          {/* Month Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsMonthDropdownOpen(!isMonthDropdownOpen)}
              className="flex w-full items-center justify-between space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-800 shadow-sm transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 sm:w-auto sm:px-4 sm:text-base"
            >
              <span className="truncate">{monthNames[month]}</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {isMonthDropdownOpen && (
              <div className="absolute z-20 mt-1 w-full min-w-[160px] rounded-lg border border-gray-300 bg-white py-1 shadow-lg dark:border-gray-600 dark:bg-gray-700 sm:w-48">
                <div className="max-h-60 overflow-auto">
                  {monthNames.map((monthName, idx) => (
                    <button
                      key={idx}
                      onClick={() => handleMonthChange(idx)}
                      className={`w-full px-4 py-2 text-left text-sm transition-colors hover:bg-gray-100 dark:hover:bg-gray-600 sm:text-base ${
                        idx === month
                          ? "bg-blue-100 font-medium text-blue-700 dark:bg-blue-900 dark:text-blue-200"
                          : "text-gray-700 dark:text-gray-200"
                      }`}
                    >
                      {monthName}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Year Dropdown */}
          <div className="w-full sm:w-auto">
            <label htmlFor="calendar-year-select" className="sr-only">
              {t("Select year")}
            </label>
            <select
              id="calendar-year-select"
              value={year}
              onChange={handleYearChange}
              className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-800 shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:w-auto sm:px-4 sm:text-base"
              aria-label={t("Select year")}
            >
              {years.map((yr) => (
                <option key={yr} value={yr}>
                  {yr}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Navigation Button - Next */}
        <button
          onClick={isRTL ? onPrevMonth : onNextMonth}
          className="rounded-full p-2 text-gray-700 transition duration-200 ease-in-out hover:bg-gray-100 hover:text-black dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white sm:p-3"
          aria-label={t("Next month")}
        >
          {/* Left arrow for RTL, right arrow for LTR */}
          {isRTL ? (
            // Left arrow
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 sm:h-6 sm:w-6"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          ) : (
            // Right arrow
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 sm:h-6 sm:w-6"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4-4a1 1 0 01-1.414 0z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </button>
      </div>
    </div>
  );
};

export default CalendarHeader;

import { Permissions , PermissionCategory} from '@/lib/types/auth';

export class PermissionService {
  private permissions: Permissions;

  constructor(permissions: Permissions) {
    this.permissions = permissions;
  }

  hasCategoryAccess(category: String): boolean {
    if (!this.permissions) {
      return true;
    }
    if (!this.permissions[category as keyof Permissions]) {
      return true;
    }
    return this.permissions[category as keyof Permissions].sidebar;
  }

  hasPermission(category: keyof Permissions, permission: keyof PermissionCategory): boolean {
    if (!this.permissions[category as keyof Permissions]) {
      return true;
    }
    return !!this.permissions[category]?.[permission];
  }

  getAllPermissions(): Permissions {
    if (!this.permissions) {
      return {} as Permissions;
    }
    return this.permissions;
  }

  hasAllPermissions(requirements: Partial<Permissions>): boolean {
    if (!this.permissions) {
      return false;
    }
    return Object.entries(requirements).every(([category, perms]) => {
      const categoryPermissions = this.permissions[category as keyof Permissions];
      if (!categoryPermissions) return false;
      
      return Object.entries(perms).every(([perm, required]) => 
        required ? categoryPermissions[perm as keyof PermissionCategory] : true
      );
    });
  }
}
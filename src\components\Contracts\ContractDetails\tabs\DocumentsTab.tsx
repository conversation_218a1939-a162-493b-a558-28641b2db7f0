import React from "react";
import { Contract } from "@/lib/interfaces/contract";
import { FaFileAlt, FaFileUpload, FaDownload } from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import { formatDate } from "../utils";

interface DocumentsTabProps {
  contract: Contract;
}

const DocumentsTab: React.FC<DocumentsTabProps> = ({ contract }) => {
  const { t, language } = useLanguage();

  // Local format function with Arabic support
  const formatDateLocal = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === "ar" ? "ar-EG" : "en-US",
    );
  };

  // Safe access to document_uploaded with fallback
  const documents = contract.document_uploaded || [];

  return (
    <div>
      <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4">
        {t("Contract Documents")}
      </h3>

      <div className="mb-6">
        <button className="flex items-center gap-2 rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600">
          <FaFileUpload size={16} />
          {t("Upload New Document")}
        </button>
      </div>

      {documents.length > 0 ? (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {documents.map((doc) => (
            <div
              key={doc.id}
              className="rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
            >
              <div className="mb-3 flex items-start justify-between">
                <div className="flex items-start">
                  <div className="mr-3 rounded bg-gray-100 p-2 dark:bg-gray-700">
                    {doc.fileType === "pdf" && (
                      <FaFileAlt
                        className="text-red-500 dark:text-red-400"
                        size={20}
                      />
                    )}
                    {doc.fileType === "image" && (
                      <FaFileAlt
                        className="text-blue-500 dark:text-blue-400"
                        size={20}
                      />
                    )}
                  </div>
                  <div>
                    <h6 className="truncate font-medium text-gray-900 dark:text-white">
                      {doc.name}
                    </h6>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {t("uploaded")}: {formatDateLocal(doc.uploadDate)}
                    </p>
                  </div>
                </div>
                <span className="rounded-full bg-gray-100 px-2 py-1 text-xs font-semibold text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  {doc.type.charAt(0).toUpperCase() + doc.type.slice(1)}
                </span>
              </div>

              <div className="mt-3 flex justify-end border-t border-gray-100 pt-3 dark:border-gray-700">
                <a
                  href={doc.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 rounded bg-blue-100 px-3 py-1 text-sm text-blue-700 transition-colors hover:bg-blue-200 dark:bg-blue-800 dark:text-blue-200 dark:hover:bg-blue-700"
                >
                  <FaDownload size={12} />
                  {t("download")}
                </a>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="rounded-lg bg-gray-50 py-8 text-center text-gray-500 dark:bg-gray-700 dark:text-gray-400 md:py-12">
          <FaFileUpload className="mx-auto mb-3" size={24} />
          <p>{t("noDocumentsYet")}</p>
        </div>
      )}
    </div>
  );
};

export default DocumentsTab;

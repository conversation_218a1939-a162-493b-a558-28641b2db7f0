import React, { useState } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";
import {
  FaCalendarAlt,
  FaDollarSign,
  FaInfoCircle,
  FaUser,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaTimes,
  FaEdit,
  FaTrash,
  FaHistory,
  FaSave,
} from "react-icons/fa";
import { Button } from "@/components/ui/button";
import { Edit2, Trash2, History } from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { LoadingComp } from "@/components/common/Loading";
import DeleteConfirmationModal from "@/components/Modals/DeleteConfirmationModal";
import ExpenseServices from "@/lib/expenses";
import IncomeServices from "@/lib/income";
import { toast } from "react-hot-toast";
import { canEditIncome } from "@/types/income";
import { canEditExpense } from "@/types/expenses";

interface SelectedEventDetailsProps {
  event: EventDetails;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onHistory: () => void;
  onSave: (updatedEvent: EventDetails) => void;
}

const SelectedEventDetails: React.FC<SelectedEventDetailsProps> = ({
  event,
  onClose,
  onEdit,
  onDelete,
  onHistory,
  onSave,
}) => {
  const { t, language } = useLanguage();
  const [isEditing, setIsEditing] = useState(false);
  const [editedEvent, setEditedEvent] = useState<EventDetails>(event);
  const { hasPermission, permissionsLoaded } = usePermissions();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Get services
  const expenseService = ExpenseServices();
  const incomeService = IncomeServices();

  // Handle soft delete based on event type
  const handleSoftDelete = async () => {
    if (editedEvent && editedEvent.id) {
      setIsDeleting(true);
      setDeleteError(null);

      try {
        // Check if it's income or expense and call the appropriate function
        if (editedEvent.category === "income") {
          await incomeService.softDeleteincome(editedEvent.id);
          toast.success(
            `Income "${editedEvent.title}" has been deleted successfully`,
          );
        } else {
          await expenseService.softDeleteExpense(editedEvent.id);
          toast.success(
            `Expense "${editedEvent.title}" has been deleted successfully`,
          );
        }

        setTimeout(() => {
          // Refresh the page after deletion
          window.location.reload();
        }, 1000);
        // Close modal and trigger parent callback
        setIsDeleteModalOpen(false);
        onDelete();
      } catch (error: any) {
        console.error(`Error soft deleting ${editedEvent.category}:`, error);

        // Handle error response
        if (error.response && error.response.data) {
          const errorMessage =
            error.response.data.warning ||
            error.response.data.message ||
            `Error deleting ${editedEvent.category}`;
          setDeleteError(errorMessage);
        } else {
          setDeleteError(
            `Failed to delete ${editedEvent.category}. Please try again.`,
          );
        }
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const handleSave = () => {
    onSave(editedEvent);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedEvent(event);
    setIsEditing(false);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setEditedEvent((prevEvent) => ({ ...prevEvent, [name]: value }));
  };
  if (!permissionsLoaded) {
    return (
      <div className="fixed left-0 top-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }
  const formatCurrency = (value: number) =>
    new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  return (
    <div className="relative mb-6 rounded-lg bg-white p-6 shadow-lg dark:bg-boxdark">
      <button
        className="absolute right-4 top-4 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        onClick={onClose}
      >
        <FaTimes size={24} />
      </button>
      <div className="mb-4 text-center">
        {isEditing ? (
          <input
            type="text"
            name="title"
            value={editedEvent.title}
            onChange={handleChange}
            className="border-b border-gray-300 bg-transparent text-2xl font-bold text-gray-800 focus:border-blue-500 focus:outline-none dark:text-gray-100"
          />
        ) : (
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
            {event.title}
          </h2>
        )}
        <div className="mt-4 flex justify-center gap-4">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                className="border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700 dark:border-green-800 dark:hover:bg-green-900/20"
              >
                <FaSave className="mr-1.5 h-4 w-4" />
                {t("save")}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="border-gray-200 text-gray-600 hover:bg-gray-50 hover:text-gray-700 dark:border-gray-800 dark:hover:bg-gray-900/20"
              >
                {t("cancel")}
              </Button>
            </>
          ) : hasPermission("income", "edit") ||
            hasPermission("expenses", "edit") ? (
            <>
              {(() => {
                const canEdit =
                  event.category === "income"
                    ? canEditIncome(event)
                    : canEditExpense(event);
                const isLinked = event.reservation_id || event.contract_id;

                return (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (canEdit) {
                        onEdit();
                      } else {
                        const linkType = event.reservation_id
                          ? "reservation"
                          : "contract";
                        toast.error(t(`Cannot edit: linked to ${linkType}`));
                      }
                    }}
                    disabled={!canEdit}
                    className={`${
                      canEdit
                        ? "border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700 dark:border-blue-800 dark:hover:bg-blue-900/20"
                        : "cursor-not-allowed border-gray-200 text-gray-400 dark:border-gray-600 dark:text-gray-500"
                    }`}
                    title={
                      !canEdit
                        ? isLinked
                          ? event.reservation_id
                            ? t("Cannot edit: linked to reservation")
                            : t("Cannot edit: linked to contract")
                          : t("Cannot edit this event")
                        : t("Edit event")
                    }
                  >
                    <Edit2 className="mr-1.5 h-4 w-4" />
                    {t("edit")}
                  </Button>
                );
              })()}

              {/* Add Delete Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsDeleteModalOpen(true)}
                className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 dark:border-red-800 dark:hover:bg-red-900/20"
              >
                <Trash2 className="mr-1.5 h-4 w-4" />
                {t("delete")}
              </Button>
            </>
          ) : (
            <></>
          )}
          <DeleteConfirmationModal
            isOpen={isDeleteModalOpen}
            onClose={() => {
              setIsDeleteModalOpen(false);
              setDeleteError(null);
            }}
            onConfirm={handleSoftDelete}
            message={t(
              "Are you sure you want to delete this event? This action cannot be undone. You will not be able to delete this event if it has an upcoming reservation or contract.",
            )}
            title={t(
              editedEvent.category === "income"
                ? "deleteIncome"
                : "deleteExpense",
            )}
            itemName={editedEvent?.title || ""}
            isLoading={isDeleting}
            error={deleteError}
          />
        </div>
      </div>
      <hr className="mb-4 border-gray-300 dark:border-gray-700" />
      <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* Left Column */}
        <div className="space-y-4">
          {/* Amount */}
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("amount")}
            </label>
            {isEditing ? (
              <input
                type="number"
                name="amount"
                value={editedEvent.amount}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 p-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
              />
            ) : (
              <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                {formatCurrency(event.amount)}
              </p>
            )}
          </div>

          {/* Due Date */}
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("dueDate")}
            </label>
            {isEditing ? (
              <input
                type="date"
                name="dueDate"
                value={editedEvent.dueDate.split("T")[0]}
                onChange={(e) =>
                  setEditedEvent({
                    ...editedEvent,
                    dueDate: e.target.value,
                  })
                }
                className="w-full rounded-lg border border-gray-300 p-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
              />
            ) : (
              <p className="text-gray-900 dark:text-gray-100">
                {new Date(event.dueDate).toLocaleDateString(
                  language === "ar" ? "ar-EG" : "en-US",
                  {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  },
                )}
              </p>
            )}
          </div>

          {/* Payment Date - Only show when completed */}
          {event.status === "completed" && (
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {event.category === "income"
                  ? t("Received Date")
                  : t("Paid Date")}
              </label>
              <div className="rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-900/20">
                {event.category === "income" && event.received_date ? (
                  <span className="font-medium text-green-700 dark:text-green-300">
                    {new Date(event.received_date).toLocaleDateString(
                      language === "ar" ? "ar-EG" : "en-US",
                      {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      },
                    )}
                  </span>
                ) : event.category === "expense" && event.paid_date ? (
                  <span className="font-medium text-green-700 dark:text-green-300">
                    {new Date(event.paid_date).toLocaleDateString(
                      language === "ar" ? "ar-EG" : "en-US",
                      {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      },
                    )}
                  </span>
                ) : (
                  <span className="text-gray-500 dark:text-gray-400">
                    {t("Date not available")}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Status */}
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("status")}
            </label>
            {isEditing ? (
              <select
                name="status"
                value={editedEvent.status}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 p-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
              >
                <option value="pending">{t("pending")}</option>
                <option value="completed">{t("completed")}</option>
                <option value="overdue">{t("overdue")}</option>
                <option value="cancelled">{t("cancelled")}</option>
                <option value="upcoming">{t("upcoming")}</option>
              </select>
            ) : (
              <span
                className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${
                  event.status === "completed"
                    ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                    : event.status === "pending"
                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                      : event.status === "overdue"
                        ? "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100"
                }`}
              >
                {t(event.status)}
              </span>
            )}
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          {/* Priority */}
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("priority")}
            </label>
            {isEditing ? (
              <select
                name="priority"
                value={editedEvent.priority}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 p-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
              >
                <option value="low">{t("low")}</option>
                <option value="medium">{t("medium")}</option>
                <option value="high">{t("high")}</option>
              </select>
            ) : (
              <span
                className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${
                  event.priority === "high"
                    ? "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                    : event.priority === "medium"
                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                      : "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                }`}
              >
                {t(event.priority)}
              </span>
            )}
          </div>

          {/* Category */}
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("category")}
            </label>
            <span
              className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${
                event.category === "income"
                  ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                  : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
              }`}
            >
              {t(event.category)}
            </span>
          </div>

          {/* Event Type */}
          {event.type && (
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Event Type")}
              </label>
              <div className="rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-900/20">
                <span className="font-medium text-blue-700 dark:text-blue-300">
                  {event.type}
                </span>
              </div>
            </div>
          )}

          {/* Description */}
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("description")}
            </label>
            {isEditing ? (
              <textarea
                name="description"
                value={editedEvent.description || ""}
                onChange={handleChange}
                rows={3}
                className="w-full rounded-lg border border-gray-300 p-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
              />
            ) : (
              <p className="text-gray-900 dark:text-gray-100">
                {event.description || t("noDescription")}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Created At */}
      {editedEvent.created_at && (
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("createdAt")}
          </label>
          {isEditing ? (
            <input
              type="date"
              name="created_at"
              value={editedEvent.created_at.split("T")[0]}
              onChange={(e) =>
                setEditedEvent({
                  ...editedEvent,
                  created_at: e.target.value,
                })
              }
              className="w-full rounded-lg border border-gray-300 p-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
            />
          ) : (
            event.created_at && (
              <p className="text-gray-900 dark:text-gray-100">
                {new Date(event.created_at).toLocaleDateString(
                  language === "ar" ? "ar-EG" : "en-US",
                  {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  },
                )}
              </p>
            )
          )}
        </div>
      )}

      {/* Contact Information */}
      {event.contact && (
        <div className="mb-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
          <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200">
            {t("contactInformation")}
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("name")}
              </label>
              <p className="text-gray-900 dark:text-gray-100">
                {event.contact.name}
              </p>
            </div>
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("email")}
              </label>
              <p className="text-gray-900 dark:text-gray-100">
                {event.contact.email}
              </p>
            </div>
            {event.contact.phone && (
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("phone")}
                </label>
                <p className="text-gray-900 dark:text-gray-100">
                  {event.contact.phone}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Location Information */}
      {event.location && (
        <div className="mb-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
          <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200">
            {t("locationInformation")}
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("name")}
              </label>
              <p className="text-gray-900 dark:text-gray-100">
                {event.location.name}
              </p>
            </div>
            {event.location.address && (
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("address")}
                </label>
                <p className="text-gray-900 dark:text-gray-100">
                  {event.location.address}
                </p>
              </div>
            )}
            {event.location.city && (
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("city")}
                </label>
                <p className="text-gray-900 dark:text-gray-100">
                  {event.location.city}
                </p>
              </div>
            )}
            {event.location.country && (
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("country")}
                </label>
                <p className="text-gray-900 dark:text-gray-100">
                  {event.location.country}
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const DetailItem: React.FC<{
  icon: React.ReactNode;
  label: string;
  value: React.ReactNode;
}> = ({ icon, label, value }) => {
  const { t } = useLanguage();
  return (
    <p className="mb-2 flex items-center gap-2 text-gray-600 dark:text-gray-300">
      {icon}
      <span className="text-gray-500">{label}:</span>{" "}
      {typeof value === "string" ? t(value) : value}
    </p>
  );
};

const Section: React.FC<{ title: string; children: React.ReactNode }> = ({
  title,
  children,
}) => (
  <div className="mb-4">
    <h3 className="mb-2 text-lg font-bold text-gray-800 dark:text-gray-100">
      {title}
    </h3>
    {children}
  </div>
);

export default SelectedEventDetails;

import React, { useState, useMemo, useEffect } from "react";
import {
  Search,
  Filter,
  X,
  User<PERSON>heck,
  UserPlus,
  Building,
  MapPin,
  Mail,
  Phone,
  ListFilter,
} from "lucide-react";
import { Card, CardContent } from "@/components/cards/card";
import { Contact } from "@/lib/types/contacts";
import ContactCard from "../cards/contactCard";
import { Combobox } from "@headlessui/react";
import { Check, ChevronDown } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";
import { useContacts } from "@/hooks/useContact";

interface ContactListProps {
  onSelect: (contact: Contact) => void;
  selectedId?: string;
  canEdit?: boolean;
  canDelete?: boolean;
}

const ContactList = ({ onSelect, selectedId, canEdit = true, canDelete = true }: ContactListProps) => {
  const { t } = useLanguage();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { data, isLoading, isError } = useContacts();

  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    type: [] as string[],
    location: [] as string[],
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const contactTypes = ["client", "other", "agency"];
  const locations = useMemo(() => {
    // Extract all unique locations from contacts
    const allLocations = new Set<string>();
    contacts.forEach((contact) => {
      contact.sharedLocations?.forEach((loc) => allLocations.add(loc.name));
      // contact.ownedLocations?.forEach(loc => allLocations.add(loc.name));
    });
    return Array.from(allLocations).sort();
  }, [contacts]);

  const filteredContacts = useMemo(() => {
    return contacts.filter((contact) => {
      const matchesSearch =
        contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.company.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesTypeFilter =
        filters.type.length === 0 ||
        contact.type.some((type) => filters.type.includes(type));

      const matchesLocationFilter =
        filters.location.length === 0 ||
        filters.location.some(
          (loc) =>
            contact.sharedLocations.some((location) => location.name === loc),
          // contact.ownedLocations.some(location => location.name === loc)
        );

      return matchesSearch && matchesTypeFilter && matchesLocationFilter;
    });
  }, [contacts, searchTerm, filters]);

  const toggleFilter = (
    filterType: "type" | "location",
    values: string | string[],
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: Array.isArray(values)
        ? values
        : prev[filterType].includes(values)
          ? prev[filterType].filter((item) => item !== values)
          : [...prev[filterType], values],
    }));
  };

  const clearFilters = () => {
    setFilters({ type: [], location: [] });
  };

  useEffect(() => {
    if (data) {
      console.log("Fetched contacts:", data.contacts);

      setContacts(data.contacts);
      setLoading(false);
    } else if (isError) {
      setError(t("errorLoadingContacts"));
      setLoading(false);
    }
  }, [data, isError, t]);

  return (
    <Card className="w-full max-w-full overflow-hidden">
      <CardContent className="p-3 sm:p-4 lg:p-6">
        {/* Search and Filters Section */}
        <div className="mb-4 space-y-3 sm:mb-6 sm:space-y-4">
          <div className="relative">
            <Search
              className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400 dark:text-gray-500"
              size={18}
            />
            <input
              type="text"
              placeholder={t("searchContacts")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full rounded-lg border py-2.5 pl-10 pr-4 text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-boxdark dark:text-gray-100 sm:py-3 sm:text-base"
            />
          </div>

          {/* Filter Toggle and Active Filters */}
          <div className="flex flex-wrap items-center gap-2 sm:gap-3">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center gap-2 rounded-lg px-3 py-2 text-sm text-gray-600 transition-colors hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 sm:text-base"
            >
              <ListFilter size={16} />
              {t("filters")}
            </button>

            {/* Active Filters Display */}
            <div className="flex flex-wrap gap-1.5 sm:gap-2">
              {filters.type.map((type) => (
                <span
                  key={type}
                  className="flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                >
                  {type}
                  <X
                    size={12}
                    className="cursor-pointer hover:text-blue-600"
                    onClick={() => toggleFilter("type", type)}
                  />
                </span>
              ))}
              {filters.location.map((loc) => (
                <span
                  key={loc}
                  className="flex items-center gap-1 rounded-full bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-300"
                >
                  {loc}
                  <X
                    size={12}
                    className="cursor-pointer hover:text-green-600"
                    onClick={() => toggleFilter("location", loc)}
                  />
                </span>
              ))}
              {(filters.type.length > 0 || filters.location.length > 0) && (
                <button
                  onClick={clearFilters}
                  className="text-xs text-red-600 hover:underline dark:text-red-400"
                >
                  {t("clearFilters")}
                </button>
              )}
            </div>
          </div>

          {/* Expandable Filters */}
          {isFilterOpen && (
            <div className="mt-2 grid grid-cols-1 gap-4 sm:grid-cols-2">
              {/* Contact Type Filters */}
              <div>
                <p className="mb-2 flex items-center gap-2 text-sm font-medium">
                  <UserCheck size={16} />
                  {t("contactTypes")}
                </p>
                <div className="space-y-1">
                  {contactTypes.map((type) => (
                    <label
                      key={type}
                      className="flex items-center gap-2 text-sm"
                    >
                      <input
                        type="checkbox"
                        checked={filters.type.includes(type)}
                        onChange={() => toggleFilter("type", type)}
                        className="form-checkbox h-4 w-4 text-blue-600"
                      />
                      {type}
                    </label>
                  ))}
                </div>
              </div>

              {/* Location Filters */}
              <div>
                <p className="mb-2 flex items-center gap-2 text-sm font-medium">
                  <MapPin size={16} /> {t("locations")}
                </p>
                <Combobox
                  value={filters.location}
                  onChange={(selected) => toggleFilter("location", selected)}
                  multiple
                >
                  <div className="relative">
                    {/* Input with focus triggering dropdown */}
                    <Combobox.Input
                      className="w-full rounded-lg border py-2 pl-3 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-boxdark dark:text-gray-100"
                      onChange={(e) => setSearchTerm(e.target.value)} // Search functionality
                      placeholder={t("searchLocations")}
                    />

                    {/* Dropdown Icon */}
                    <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
                      <ChevronDown size={20} className="text-gray-400" />
                    </Combobox.Button>

                    {/* Dropdown Options */}
                    <Combobox.Options className="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      {locations
                        .filter((loc) =>
                          loc.toLowerCase().includes(searchTerm.toLowerCase()),
                        ) // Filtered options
                        .map((loc) => (
                          <Combobox.Option
                            key={loc}
                            value={loc}
                            className={({ active }) =>
                              `relative cursor-pointer select-none py-2 pl-10 pr-4 ${
                                active
                                  ? "bg-blue-600 text-white"
                                  : "text-gray-900"
                              }`
                            }
                          >
                            {({ selected, active }) => (
                              <>
                                <span
                                  className={`block truncate ${
                                    selected ? "font-medium" : "font-normal"
                                  }`}
                                >
                                  {loc}
                                </span>
                                {selected && (
                                  <span
                                    className={`absolute inset-y-0 left-0 flex items-center pl-3 ${
                                      active ? "text-white" : "text-blue-600"
                                    }`}
                                  >
                                    <Check size={16} />
                                  </span>
                                )}
                              </>
                            )}
                          </Combobox.Option>
                        ))}
                    </Combobox.Options>
                  </div>
                </Combobox>
              </div>
            </div>
          )}
        </div>

        {/* Loading state */}
        {loading && (
          <div className="flex items-center justify-center py-8 sm:py-12">
            <div className="flex flex-col items-center space-y-3">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900 dark:border-white"></div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t("Loading contacts...")}
              </p>
            </div>
          </div>
        )}

        {/* Error state */}
        {error && (
          <div className="rounded-lg bg-red-50 p-4 text-center dark:bg-red-900/20 sm:p-6">
            <div className="mx-auto max-w-md">
              <p className="text-sm text-red-600 dark:text-red-400 sm:text-base">
                {error}
              </p>
              <button
                className="mt-2 text-sm text-red-700 underline hover:text-red-800 dark:text-red-300 dark:hover:text-red-200"
                onClick={() => window.location.reload()}
              >
                {t("Try again")}
              </button>
            </div>
          </div>
        )}

        {/* Contact list */}
        {!loading && !error && (
          <div className="space-y-2 sm:space-y-3">
            {filteredContacts.length === 0 ? (
              <div className="rounded-lg bg-gray-50 p-6 text-center dark:bg-gray-800 sm:p-8">
                <div className="mx-auto max-w-md">
                  <UserPlus
                    size={40}
                    className="mx-auto mb-4 text-gray-400 sm:size-12"
                  />
                  <h3 className="mb-2 text-lg font-medium text-gray-700 dark:text-gray-300 sm:text-xl">
                    {t("noContactsFound")}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 sm:text-base">
                    {t("Try adjusting your search or filters")}
                  </p>
                </div>
              </div>
            ) : (
              filteredContacts.map((contact) => (
                <ContactCard
                  key={contact.id}
                  contact={contact}
                  onSelect={onSelect}
                  isSelected={selectedId === contact.id}
                  canEdit={canEdit}
                  canDelete={canDelete}
                />
              ))
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ContactList;

{"contracts": "Contracts", "propertyContracts": "Property Contracts", "newContract": "New Contract", "searchContracts": "Search contracts... (Title, Client, Location)", "allStatuses": "All Statuses", "active": "Active", "pending": "Pending", "expired": "Expired", "terminated": "Terminated", "draft": "Draft", "to": "to", "resetFilters": "Reset Filters", "searchAndFilters": "Search & Filters", "noContractsFound": "No contracts found", "month": "month", "months": "months", "days": "days", "day": "day", "deleteConfirmation": "Are you sure you want to delete this contract?", "viewDetails": "View Details", "contractExpired": "Contract expired", "contractExpirestoday": "Contract expires today", "expiresIn": "Expires in", "contract": "Contract", "title": "Title", "contractNumber": "Contract Number", "description": "Description", "startDate": "Start Date", "endDate": "End Date", "contractDuration": "Contract Duration", "client": "Client", "propertyLocation": "Property Location", "selectAClient": "Select a client", "selectALocation": "Select a location", "locationDetails": "Location Details", "paymentDetails": "Payment Details", "monthlyAmount": "Monthly Amount", "totalContract": "Total Contract", "totalContractValue": "Total Contract Value", "calculatedBasedOnMonthly": "Calculated based on monthly amount and duration, can be manually adjusted", "paymentDay": "Payment Day", "dayOfMonthWhenPaymentIsDue": "Day of the month when payment is due (1-31)", "renewalTerms": "Renewal Terms", "autoRenewal": "Auto-Renewal", "noManualRenewalRequired": "No - Manual Renewal Required", "yesAutoRenewAtEndOfTerm": "Yes - Auto-Renew at End of Term", "rateIncrease": "Rate Increase", "percentageIncreaseUponRenewal": "Percentage increase upon renewal", "noticePeriod": "Notice Period", "daysNoticeRequiredToCancel": "Days notice required to cancel auto-renewal", "daysNoticeRequiredForRenewal": "Days notice required for renewal request", "documents": "Documents", "uploadDocuments": "Upload Documents", "uploadContractDocuments": "Upload contract documents, invoices, or other relevant files", "documentList": "Document List", "removeFile": "Remove file", "nodocument_uploaded": "No documents uploaded yet", "notes": "Notes", "createContract": "Create Contract", "updateContract": "Update Contract", "cancel": "Cancel", "basicInformation": "Basic Information", "clientAndProperty": "Client & Property", "clientInformation": "Client Information", "clientName": "Client Name", "company": "Company", "email": "Email", "phone": "Phone", "propertyInformation": "Property Information", "property": "Property", "paymentInformation": "Payment Information", "currentStatus": "Current Status", "dueDate": "Due Date", "nextPayment": "Next Payment", "paymentSchedule": "Payment Schedule", "paymentHistoryAppear": "Payment details will appear here when implemented", "paymentHistory": "Payment History", "paymentHistoryAppearHere": "Payment history will appear here when implemented", "contractRenewal": "Contract Renewal", "autoRenewalEnabled": "Auto-Renewal Enabled", "manualRenewalRequired": "Manual Renewal Required", "thisContractWillAutoRenew": "This contract will automatically renew when it expires", "thisContractRequiresManual": "This contract requires manual renewal before expiration", "currentMonthly": "Current monthly", "newMonthly": "New monthly", "noticeDeadline": "Notice deadline", "renewalActions": "Renewal Actions", "renewContract": "Renew Contract", "cancelRenewal": "Cancel <PERSON>", "contractDocuments": "Contract Documents", "uploadNewDocument": "Upload New Document", "noDocumentsYet": "No documents have been uploaded yet", "upToDate": "Up to date", "overdue": "Overdue", "partiallyPaid": "Partially paid", "paymentDueToday": "Payment due today", "paymentDueIn": "Payment due in", "printContract": "Print Contract", "downloadAsPDF": "Download as PDF", "editContract": "Edit Contract", "deleteContract": "Delete Contract", "close": "Close", "details": "Details", "payments": "Payments", "renewal": "Renewal", "total": "Total", "contractPeriod": "Contract Period", "created": "Created", "by": "by", "lastUpdated": "Last Updated", "autoRenews": "Auto-renews", "increase": "increase", "daysNotice": "days notice", "manualRenewal": "Manual renewal", "showing": "Showing", "of": "of", "th": "th", "rateWillIncrease": "Rate will increase by this percentage upon renewal", "paymentStatus": "Payment Status", "new": "New", "actions": "Actions", "status": "Status", "startDatePlaceholder": "Start date", "endDatePlaceholder": "End date", "due": "Due", "uploaded": "Uploaded", "download": "Download", "contractInformation": "Contract Information", "noDescriptionProvided": "No description provided", "noNotes": "No notes", "noticeRequiredToCancelAutoRenewal": "Notice required to cancel auto-renewal", "noticeRequiredToRequestRenewal": "Notice required to request renewal", "addNewContract": "Add New Contract", "editContractTitle": "Edit Contract", "scrollbar-hide": "Hide scrollbar", "ofEachMonth": "of each month", "pleaseSelectClientAndLocation": "Please select a client and location", "paymentDayMustBeBetween": "Payment day must be between 1 and 31", "monthlyAmountMustBeGreaterThanZero": "Monthly amount must be greater than zero", "pleaseFillAllRequiredFields": "Please fill all required fields", "years": "years", "tryAgain": "Try again", "Confirm": "Confirm", "Paid Date": "Paid <PERSON>", "Expense Progress": "Expense Progress", "Upload Documents": "Upload Documents", "Related Incomes": "Related Incomes", "Add Related Income": "Add Related Income", "Update Contract": "Update Contract"}
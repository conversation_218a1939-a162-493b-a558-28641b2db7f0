import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import useLanguage from "@/hooks/useLanguage";
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON>sBar<PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  <PERSON>tesianGrid,
  <PERSON>ltip as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts';

interface ContactLocationsDashboardProps {
  reservationsByLocation: Array<{
    name: string;
    reservations: number;
    percentage: number;
  }>;
  COLORS: string[];
  locationData: Array<{
    name: string;
    reservations: number;
    revenue: number;
    percentage: number;
  }>;
}

const ContactLocationsDashboard: React.FC<ContactLocationsDashboardProps> = ({
  reservationsByLocation,
  COLORS,
  locationData
}) => {
  const { t } = useLanguage();

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium">{t("preferredLocations")}</h3>
          <Button variant="outline" size="sm" className="h-8">
            {t("viewMap")}
          </Button>
        </div>
        
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <ReChartsBarChart
              data={reservationsByLocation}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              layout="vertical"
            >
              <CartesianGrid strokeDasharray="3 3" horizontal={false} />
              <XAxis type="number" axisLine={false} tickLine={false} />
              <YAxis 
                type="category" 
                dataKey="name" 
                axisLine={false} 
                tickLine={false}
                width={150}
              />
              <ReChartsTooltip 
                formatter={(value: any) => [value, t("reservations")]}
                labelFormatter={(name) => `${name}`}
              />
              <Bar 
                dataKey="reservations" 
                name={t("reservations")}
                fill="#3b82f6"
                radius={[0, 4, 4, 0]}
              >
                {reservationsByLocation.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={COLORS[index % COLORS.length]} 
                  />
                ))}
              </Bar>
            </ReChartsBarChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-8">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
            {t("locationBreakdown")}
          </h4>
          
          <div className="space-y-4">
            {reservationsByLocation.map((location, index) => (
              <div key={location.name} className="flex flex-col">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">{location.name}</span>
                  <span className="text-sm font-medium">{location.percentage}%</span>
                </div>
                <div className="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full">
                  <div 
                    className="h-full rounded-full" 
                    style={{ 
                      width: `${location.percentage}%`, 
                      backgroundColor: COLORS[index % COLORS.length] 
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Location Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-medium mb-4">{t("locationPerformance")}</h3>
          
          <div className="space-y-6">
            {locationData.slice(0, 3).map(location => (
              <div key={location.name} className="border-b dark:border-gray-700 pb-4 last:border-0 last:pb-0">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{location.name}</h4>
                  <div className="text-sm font-medium text-blue-600">{location.reservations} {t("reservations")}</div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 mt-2">
                  <div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{t("revenue")}</span>
                    <p className="text-sm font-medium mt-1">${location.revenue.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{t("shareOfTotal")}</span>
                    <p className="text-sm font-medium mt-1">{location.percentage}%</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{t("avgRevenuePerReservation")}</span>
                    <p className="text-sm font-medium mt-1">${Math.round(location.revenue / location.reservations).toLocaleString()}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <Button variant="link" className="mt-4 h-auto p-0">
            {t("viewAllLocations")}
          </Button>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-medium mb-4">{t("locationTypes")}</h3>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <ReChartsPieChart>
                <Pie
                  data={[
                    { name: 'Billboard', value: 18 },
                    { name: 'Digital Display', value: 12 },
                    { name: 'Street Banner', value: 8 },
                    { name: 'Transport', value: 4 },
                    { name: 'Other', value: 2 }
                  ]}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {COLORS.map((color, index) => (
                    <Cell key={`cell-${index}`} fill={color} />
                  ))}
                </Pie>
                <ReChartsTooltip formatter={(value: any) => [value, t("reservations")]} />
              </ReChartsPieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 dark:text-gray-400">{t("mostUsedLocationType")}</span>
              <span className="text-sm font-medium mt-1">Billboard</span>
            </div>
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 dark:text-gray-400">{t("highestRevenueType")}</span>
              <span className="text-sm font-medium mt-1">Digital Display</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactLocationsDashboard;

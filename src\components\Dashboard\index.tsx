"use client";
import React, { useState, useEffect } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import UpcomingEventPopup from "../Financials/common/UpcommingEventPopup.tsx";
import AnalyticsSection from "./Sections/AnalyticsSection";
import UpcomingEventsSection from "./Sections/UpcomingEventsSection";
import IncomeVsExpenseSection from "./Sections/IncomeVsExpenseSection";
import ExpectedVsActualSection from "./Sections/ExpectedVsActualSection";
import Analytics from "../Financials/Overview/Sections/Analytic";
import getCombinedEvents from "@/lib/events";
import { LoadingComp } from "@/components/common/Loading";
import { is } from "date-fns/locale";

const Dashboard: React.FC = () => {
  const [upcomingEvents, setUpcomingEvents] = useState<EventDetails[]>([]);
  const [upcomingEventsSectionData, setUpcomingEventsSectionData] = useState<
    EventDetails[]
  >([]);
  const { t } = useLanguage();
  const [selectedEvent, setSelectedEvent] = useState<EventDetails | null>(null);
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [loading, setLoading] = useState<boolean>(true); // Loading state
  useEffect(() => {
    const today = new Date();
    const filteredEvents = upcomingEvents.filter(
      (event) => new Date(event.dueDate) >= today,
    );
    setUpcomingEventsSectionData(filteredEvents.slice(0, 10));
  }, [upcomingEvents]);

  // sort upcoming events by date
  upcomingEvents.sort(
    (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime(),
  );

  const handlePopupClose = () => setSelectedEvent(null);
  const handleSave = (updatedEvent: EventDetails) => {
    setUpcomingEvents((prevEvents) =>
      prevEvents.map((event) =>
        event.id === updatedEvent.id ? updatedEvent : event,
      ),
    );
    handlePopupClose();
  };

  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      try {
        const events = await getCombinedEvents();
        setUpcomingEvents(events);
      } catch (error) {
        console.error("Error fetching events:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  return loading ? (
    <div className="flex min-h-[400px] items-center justify-center rounded-lg bg-white dark:bg-gray-800">
      <LoadingComp />
    </div>
  ) : (
    <div className="space-y-4 sm:space-y-6 lg:space-y-8">
      <Breadcrumb pageName={t("overview")} />

      {/* Analytics Overview */}
      <div className="w-full">
        <Analytics events={upcomingEvents} />
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-3 lg:gap-8">
        {/* Analytics Section - Takes 2 columns on large screens */}
        <div className="lg:col-span-2">
          <AnalyticsSection events={upcomingEvents} />
        </div>

        {/* Upcoming Events Section - Takes 1 column on large screens */}
        <div className="lg:col-span-1">
          <UpcomingEventsSection
            onSave={handleSave}
            events={upcomingEventsSectionData}
            filterStatus={filterStatus}
            filterPriority={filterPriority}
            onFilterStatusChange={setFilterStatus}
            onFilterPriorityChange={setFilterPriority}
          />
        </div>
      </div>

      {/* Bottom Charts Section */}
      <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2 lg:gap-8">
        <div className="w-full">
          <IncomeVsExpenseSection events={upcomingEvents} />
        </div>

        <div className="w-full">
          <ExpectedVsActualSection events={upcomingEvents} />
        </div>
      </div>

      {/* Popup */}
      {selectedEvent && (
        <UpcomingEventPopup
          event={selectedEvent}
          onClose={handlePopupClose}
          onSave={handleSave}
        />
      )}
    </div>
  );
};

export default Dashboard;

import React from "react";
import useLanguage from "@/hooks/useLanguage";

export const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
};

export const getRecurringDateRange = (range: string) => {
    const currentDate = new Date();
    let start: Date;
    let end: Date;

    switch (range) {
        case "everyMonth":
            start = new Date(currentDate);
            start.setMonth(start.getMonth() - 1);
            end = new Date(currentDate);
            end.setMonth(end.getMonth() + 1);
            break;
        case "every7Days":
            start = new Date(currentDate);
            start.setDate(start.getDate() - 7);
            end = new Date(currentDate);
            end.setDate(end.getDate() + 7);
            break;
        case "every30Days":
            start = new Date(currentDate);
            start.setDate(start.getDate() - 30);
            end = new Date(currentDate);
            end.setDate(end.getDate() + 30);
            break;
        case "every90Days":
            start = new Date(currentDate);
            start.setDate(start.getDate() - 90);
            end = new Date(currentDate);
            end.setDate(end.getDate() + 90);
            break;
        case "every3Months":
            start = new Date(currentDate);
            start.setMonth(start.getMonth() - 3);
            end = new Date(currentDate);
            end.setMonth(end.getMonth() + 3);
            break;
        case "everyQuarter":
            start = new Date(currentDate);
            start.setMonth(start.getMonth() - 4);
            end = new Date(currentDate);
            end.setMonth(end.getMonth() + 4);
            break;
        case "every6Months":
            start = new Date(currentDate);
            start.setMonth(start.getMonth() - 6);
            end = new Date(currentDate);
            end.setMonth(end.getMonth() + 6);
            break;
        case "everyYear":
            start = new Date(currentDate);
            start.setFullYear(start.getFullYear() - 1);
            end = new Date(currentDate);
            end.setFullYear(end.getFullYear() + 1);
            break;
        default:
            start = new Date(currentDate);
            start.setDate(currentDate.getDate() - 7);
            end = new Date(currentDate);
            end.setDate(currentDate.getDate() + 7);
    }

    return { start: formatDate(start), end: formatDate(end) };
};

interface DateRangeSelectorProps {
    onChange: (range: string) => void;
}

export const RecurringDateRangeSelector: React.FC<DateRangeSelectorProps> = ({ onChange }) => {
    const { t } = useLanguage();

    return (
        <div className="mb-4 w-full">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t("Recurrence Schedule")} 
                </label>
            <select 
                onChange={(e) => onChange(e.target.value)} 
                className="w-full border rounded p-2 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
            >
                <option value="">{t("Select Recurrence")}</option>
                <option value="everyMonth">{t("Every Month")}</option>
                <option value="every7Days">{t("Every 7 Days")}</option>
                <option value="every90Days">{t("Every 90 Days")}</option>
                <option value="every3Months">{t("Every 3 Months")}</option>
                <option value="everyQuarter">{t("Every Quarter")}</option>
                <option value="every6Months">{t("Every 6 Months")}</option>
                <option value="everyYear">{t("Every Year")}</option>
            </select>
        </div>
    );
};

import useLanguage from "@/hooks/useLanguage";
import { useEffect, useState } from "react";

const LanguageSwitcher = () => {
  const { language, toggleLanguage } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="relative inline-block">
      <button
        onClick={toggleLanguage}
        className={`
          relative h-9 
          w-[80px] 
          overflow-hidden rounded-full border 
          border-gray-200 bg-gradient-to-r 
          from-gray-100 
          to-gray-200 shadow-inner transition-all
          duration-300
          ease-in-out
          hover:shadow-lg
          dark:border-gray-600 dark:from-gray-700 dark:to-gray-800
        `}
      >
        <div
          className={`
            absolute 
            top-1 
            ${language === "en" ? "left-1" : "right-1"}
            h-7 w-7 
            rounded-full border-2
            border-gray-100 
            bg-white shadow-md transition-all
            duration-300
            ease-in-out
            hover:shadow-lg dark:border-gray-700 dark:bg-gray-900
          `}
        />
        <div
          className={`
          flex h-full 
          items-center
          justify-between px-3
          text-xs font-semibold
          ${language === "en" ? "flex-row" : "flex-row-reverse"}
        `}
        >
          <span
            className={`transition-colors duration-300 ${language === "en" ? "text-black-400" : "text-black-100"}`}
          >
            EN
          </span>
          <span
            className={`transition-colors duration-300 ${language === "ar" ? "text-black-400" : "text-black-100"}`}
          >
            عربى
          </span>
        </div>
      </button>
    </div>
  );
};

export default LanguageSwitcher;

import { useState } from 'react';
import { EventDetails } from "@/lib/interfaces/finaces";
import { Contact } from "@/lib/types/contacts";
import { Location } from "@/lib/types/location";
import { Reservation } from "@/lib/interfaces/reservation";
import { Contract } from "@/lib/interfaces/contract";

interface PDFModalProps {
  isOpen: boolean;
  data: {
    events?: EventDetails[];
    contacts?: Contact[];
    locations?: Location[];
    reservations?: Reservation[];
    contracts?: Contract[];
  };
  title: string;
  mode: 'events' | 'contacts' | 'locations' | 'reservations' | 'contracts';
  onClose: () => void;
}

export const usePDFGenerator = () => {
  const [modalProps, setModalProps] = useState<PDFModalProps>({
    isOpen: false,
    data: {},
    title: '',
    mode: 'events',
    onClose: () => {}
  });

  const openPDFModal = (
    data: PDFModalProps['data'],
    title: string,
    mode: PDFModalProps['mode']
  ) => {
    setModalProps({
      isOpen: true,
      data,
      title,
      mode,
      onClose: closePDFModal
    });
  };

  const closePDFModal = () => {
    setModalProps(prev => ({
      ...prev,
      isOpen: false
    }));
  };

  return {
    modalProps,
    openPDFModal,
    closePDFModal
  };
};

export default usePDFGenerator;

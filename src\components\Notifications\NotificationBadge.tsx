import React from 'react';
import { Bell } from 'lucide-react';

interface NotificationBadgeProps {
  count: number;
  onClick: () => void;
  className?: string;
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({ 
  count,
  onClick,
  className = ''
}) => {
  const hasNotifications = count > 0;

  return (
    <button 
      onClick={onClick}
      className={`relative flex h-10 w-10 items-center justify-center rounded-full border border-stroke bg-gray-100 hover:bg-gray-200 dark:border-strokedark dark:bg-meta-4 dark:hover:bg-meta-3 transition-colors ${className}`}
      aria-label="Notifications"
    >
      {hasNotifications && (
        <span className="absolute -top-1 -right-1 h-4 w-4">
          <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-meta-1 opacity-75"></span>
          <span className="relative inline-flex h-4 w-4 rounded-full bg-meta-1 justify-center items-center text-[10px] text-white font-bold">
            {count > 9 ? '9+' : count}
          </span>
        </span>
      )}

      <Bell className="fill-none stroke-current h-5 w-5" />
    </button>
  );
};

export default NotificationBadge;

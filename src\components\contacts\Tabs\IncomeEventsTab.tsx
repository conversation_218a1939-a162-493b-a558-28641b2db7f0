"use client";

import React, { useState, useEffect } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import IncomeServices from "@/lib/income";
import { Loader2 } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import {
  getDateRange,
  DateRangeSelector,
} from "@/components/Financials/common/dateRanges";
import UpcomingEventsPage from "@/components/Dashboard/UpcommingEventsPage/UpcommingEvents";
import { FaFilter, FaTimes } from "react-icons/fa";
import IncomeSummary from "@/components/Financials/Income/IncomeSummary";

interface IncomeEventsTabProps {
  ContactId: string;
}

const IncomeEventsTab: React.FC<IncomeEventsTabProps> = ({ ContactId }) => {
  const { t, language } = useLanguage();
  const [incomes, setIncomes] = useState<EventDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Default Date Range
  const currentDate = new Date();
  const [startDate, setStartDate] = useState<Date>(
    new Date(currentDate.setDate(currentDate.getDate() - 15)),
  );
  const [endDate, setEndDate] = useState<Date>(
    new Date(new Date().setDate(new Date().getDate() + 15)),
  );
  const [selectedIncome, setSelectedIncome] = useState<EventDetails | null>(
    null,
  );

  useEffect(() => {
    const fetchIncomes = async () => {
      setIsLoading(true);
      try {
        // Get a fresh instance of IncomeServices inside the effect
        const { getIncomesByContact } = IncomeServices();
        const incomesData = await getIncomesByContact(ContactId);
        console.log("Fetched incomes11111111111111111:", incomesData);
        setIncomes(incomesData);
        setError(null);
      } catch (err) {
        console.error("Error fetching income events:", err);
        setError("Failed to load income data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchIncomes();
  }, [ContactId]); // Only re-run if ContactId changes

  const handleDateRangeChange = (range: string) => {
    const { start, end } = getDateRange(range);
    setStartDate(new Date(start));
    setEndDate(new Date(end));
  };

  const locale = language === "ar" ? arSA : enUS;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return <div className="py-8 text-center text-red-500">{error}</div>;
  }

  if (incomes.length === 0) {
    return (
      <div className="text-muted-foreground py-8 text-center">
        No income events found for this location.
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-wrap items-center justify-between gap-4">
        <h2 className="text-lg font-medium text-gray-900 dark:text-gray-400">
          {t("Income Events")} ({incomes.length})
        </h2>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2 text-sm text-gray-600 hover:text-primary dark:text-gray-400"
        >
          <FaFilter className="h-4 w-4" />
          {showFilters ? t("Hide Filters") : t("Show Filters")}
        </button>
      </div>

      {showFilters && (
        <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
          <h3 className="mb-3 text-sm font-medium">
            {t("Filter by Date Range")}
          </h3>

          <div className="mb-4 flex flex-col gap-4 sm:flex-row">
            <div className="flex items-center">
              <span className="mr-2">{t("From")}</span>
              <DatePicker
                selected={startDate}
                onChange={(date) => date && setStartDate(date)}
                className="rounded border p-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                dateFormat="yyyy-MM-dd"
                locale={locale}
              />
            </div>

            <div className="flex items-center">
              <span className="mr-2">{t("To")}</span>
              <DatePicker
                selected={endDate}
                onChange={(date) => date && setEndDate(date)}
                className="rounded border p-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                dateFormat="yyyy-MM-dd"
                locale={locale}
              />
            </div>
          </div>

          <DateRangeSelector onChange={handleDateRangeChange} />
        </div>
      )}

      <IncomeSummary incomes={incomes} loading={isLoading} />

      <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
        <UpcomingEventsPage
          events={incomes}
          setEvents={(newEvents) =>
            console.log("Update attempted in read-only mode")
          }
          mode="income"
        />
      </div>
    </div>
  );
};

export default IncomeEventsTab;

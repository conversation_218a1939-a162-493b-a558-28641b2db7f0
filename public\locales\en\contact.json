{"searchContacts": "Search contacts...", "generatePDF": "Generate PDF", "editContactDetails": "Edit Contact Details", "importContacts": "Import Contacts", "importContactsDescription": "Import contacts from a CSV or Excel file.", "fillContactDetails": "Fill in the contact details", "startImport": "Start Import", "filters": "Filters", "clearFilters": "Clear filters", "contactTypes": "Contact Types", "locations": "Locations", "searchLocations": "Search locations...", "noContactsFound": "No contacts found.", "type": "Type", "name": "Name", "company": "Company", "email": "Email", "phone": "Phone", "address": "Address", "cancel": "Cancel", "addContact": "Add Contact", "saveChanges": "Save Changes", "editContact": "Edit Contact", "addNewContact": "Add New Contact", "selectOneOrMoreTypes": "Select one or more types", "addCustomType": "Add custom type", "enterCustomType": "Enter custom type", "searchTypes": "Search types...", "noTypesFound": "No types found.", "loading": "Loading...", "edit": "Edit", "delete": "Delete", "financialHistory": "Financial History", "contactDetails": "Contact Details", "contactInformation": "Contact Information", "sharedLocations": "Shared Locations", "ownedLocations": "Owned Locations", "noSharedLocations": "No shared locations.", "noOwnedLocations": "No owned locations.", "viewMore": "View more", "created": "Created", "by": "by", "deleteContact": "Delete Contact", "noContactSelected": "No contact selected.", "EGP": "EGP", "errorLoadingContacts": "Error loading contacts.", "Try again": "Try again", "statistics": "Statistics", "contactStatistics": "Contact Statistics", "statisticsForContact": "Statistics for this contact", "timeframe": "Timeframe", "weekly": "Weekly", "monthly": "Monthly", "revenue": "Revenue", "reservations": "Reservations", "paymentHistory": "Payment History", "Income": "Income", "Expenses": "Expenses", "Contracts": "Contracts", "Events": "Events", "Reservations": "Reservations", "Financial History": "Financial History", "Loading financial data...": "Loading financial data...", "View all financial records for this contact": "View all financial records for this contact", "onTime": "On Time", "late": "Late", "pending": "Pending", "creditCard": "Credit Card", "bankTransfer": "Bank Transfer", "check": "Check", "selectAContactToViewDetails": "Select a contact to view details", "createNewContactDescription": "Create a new contact to manage your relationships and interactions.", "contactHistory": "Contact History", "Our Amount": "Our Amount", "Search reservations...": "Search reservations...", "Showing": "Showing", "Use the toggles to customize the view and displayed data.": "Use the toggles to customize the view and displayed data.", "financialData": "Financial Data", "allEvents": "All Events", "incomes": "Incomes", "deleteContactWarning": "Are you sure you want to delete this contact? This action cannot be undone.", "deleteContactConditionsNote": "Note: You cannot delete this contact if they have:", "deleteContactConditionsOwnership": " Ownership shares in any active location", "deleteContactConditionsIncomeEvents": " Upcoming income events (pending/upcoming/overdue with future due dates)", "deleteContactConditionsExpenseEvents": " Upcoming expense events (pending/upcoming/overdue with future due dates)", "deleteContactConditionsContractsReservations": "Any active contracts with future end dates and upcoming reservations will be automatically marked as deleted/cancelled.", "contactSavedSuccessfully": "Contact saved successfully.", "contactDeletedSuccessfully": "Contact deleted successfully.", "contactCreatedSuccessfully": "Contact created successfully.", "importContactsSuccess": "Contacts imported successfully.", "importContactsError": "Error importing contacts. Please check the file format and try again.", "noContactsToImport": "No contacts to import. Please upload a valid CSV or Excel file.", "Import Contacts": "Import Contacts", "Drag and drop one or more files (CSV or Excel)": "Drag and drop one or more files (CSV or Excel)", "cancelImport": "Cancel Import", "Map Columns for": "Map Columns for", "Apply to Empty": "Apply to Empty", "-- Select Column --": "Select Column", "Manual Entry": "Manual Entry", "Set default value (optional)": "Set default value (optional)", "-- Set Default Value --": "Set Default Value", "Confirm Import": "Confirm Import", "deleting": "Deleting...", "-- Select --": "Select", "hide": "<PERSON>de", "Reservations List": "Reservations List"}
import React, { useState } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";

interface ExpensePopupProps {
    expense: EventDetails;
    onClose: () => void;
    onSave: (updatedExpense: EventDetails) => void;
}

const ExpensePopup: React.FC<ExpensePopupProps> = ({ expense, onClose, onSave }) => {
    const [updatedExpense, setUpdatedExpense] = useState(expense);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setUpdatedExpense({ ...updatedExpense, [name]: value });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSave(updatedExpense);
    };

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white p-6 rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700">
                <h2 className="text-xl font-semibold mb-4 dark:text-white">Edit Expense</h2>
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                        <input
                            type="text"
                            name="title"
                            value={updatedExpense.title}
                            onChange={handleChange}
                            className="mt-1 p-2 border rounded w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Amount</label>
                        <input
                            type="number"
                            name="amount"
                            value={updatedExpense.amount}
                            onChange={handleChange}
                            className="mt-1 p-2 border rounded w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Due Date</label>
                        <input
                            type="date"
                            name="dueDate"
                            value={updatedExpense.dueDate}
                            onChange={handleChange}
                            className="mt-1 p-2 border rounded w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <select
                            name="status"
                            value={updatedExpense.status}
                            onChange={handleChange}
                            className="mt-1 p-2 border rounded w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                        >
                            <option value="upcoming">Upcoming</option>
                            <option value="completed">Completed</option>
                            <option value="overdue">Overdue</option>
                        </select>
                    </div>
                    <div className="flex justify-end gap-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 bg-gray-300 rounded dark:bg-gray-600 dark:text-gray-300"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 bg-blue-500 text-white rounded dark:bg-blue-600"
                        >
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default ExpensePopup;
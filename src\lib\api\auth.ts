// src/lib/api/auth.ts
import axios from "axios";
import { signOut } from "next-auth/react";
import { getSession } from "next-auth/react";
import { permission } from "process";

// const API_BASE_URL = "http://172.20.10.14:8000";
const API_BASE_URL =  "https://sarayvera-backend-production.up.railway.app/";
export const authService = {
  async login(email: string, password: string) {
    try {
      const response = await axios.post(`${API_BASE_URL}/users/api/login/`, {
        email,
        password,
      });
      
      return {
        user: {
          id: response.data.user.id,
          email: response.data.user.email,
          role: response.data.user.role,
        },
        access_exp: response.data.access_exp,
        refresh_exp: response.data.refresh_exp,
        accessToken: response.data.accessToken,
        refreshToken: response.data.refreshToken,
        permissions: response.data.permissions,
      };
    } catch (error) {
      throw new Error(
        (axios.isAxiosError(error) && error.response?.data?.message) || 
        (error instanceof Error ? error.message : "An unknown error occurred") || 
        "Login failed"
      );
    }
  },

  async refreshToken(refreshToken: string) {
    try {
      console.log("Refreshing token with refreshToken:", refreshToken);
      
      const response = await axios.post(`${API_BASE_URL}/users/api/refresh-token/`, {
        refreshToken: refreshToken,
      });
      
      if (response.status !== 200) {
        throw new Error("Failed to refresh token");
      }


      return {
        permission : response.data.permissions,
        accessToken: response.data.accessToken,
        access_exp: response.data.access_exp,
        refreshToken: response.data.refreshToken || refreshToken, // Fallback to old refresh token if new one isn't provided
      };
    } catch (error) {
      console.error("Error refreshing token:", error);
      await signOut({ callbackUrl: "/auth/signin" });
      throw new Error("Refresh token failed");
    }
  },

  async logout() {
    await signOut({ callbackUrl: "/auth/signin" });
  },
};
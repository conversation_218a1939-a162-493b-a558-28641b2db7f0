import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowUpRight, Calendar } from 'lucide-react';
import useLanguage from "@/hooks/useLanguage";
import {
  LineChart as ReChartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ReChartsTooltip,
  Legend as <PERSON><PERSON><PERSON>sLegend,
  ResponsiveContainer,
  BarChart as ReChartsBarChart,
  Bar
} from 'recharts';

interface OccupancyDashboardProps {
  occupancyData: Array<{
    period: string;
    occupancyRate: number;
    availableRate: number;
  }>;
  seasonalChartData: Array<{
    name: string;
    revenue: number;
    occupancy: number;
  }>;
  locationBreakdownData: Array<{
    area: string;
    revenue: number;
    occupancyRate: number;
    adsCount: number;
  }>;
  averageOccupancy: number;
}

const OccupancyDashboard: React.FC<OccupancyDashboardProps> = ({
  occupancyData,
  seasonalChartData,
  locationBreakdownData,
  averageOccupancy
}) => {
  const { t } = useLanguage();

  return (
    <div className="space-y-6">
      {/* Occupancy Analysis Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-6">
          <h3 className="text-lg font-medium">{t("occupancyAnalysis")}</h3>
          <Button variant="outline" size="sm" className="self-start sm:self-auto h-8">
            {t("exportData")}
          </Button>
        </div>
        
        <div className="h-80 w-full">
          <ResponsiveContainer width="99%" height="100%">
            <ReChartsLineChart
              data={occupancyData}
              margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="period" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} tickFormatter={(value) => `${value}%`} />
              <ReChartsTooltip formatter={(value: any) => [`${value}%`, ""]} />
              <ReChartsLegend />
              <Line 
                type="monotone" 
                dataKey="occupancyRate" 
                name={t("occupancyRate")} 
                stroke="#3b82f6" 
                strokeWidth={2} 
                dot={{ r: 3 }}
              />
              <Line 
                type="monotone" 
                dataKey="availableRate" 
                name={t("availableRate")} 
                stroke="#e11d48" 
                strokeWidth={2} 
                dot={{ r: 3 }}
              />
            </ReChartsLineChart>
          </ResponsiveContainer>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6">
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("averageOccupancy")}</p>
            <p className="text-xl font-bold mt-1">{averageOccupancy}%</p>
            <div className="flex items-center mt-2 text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-500 font-medium">+5%</span>
              <span className="text-gray-500 dark:text-gray-400 ml-1">{t("fromLastPeriod")}</span>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("peakOccupancy")}</p>
            <p className="text-xl font-bold mt-1">95%</p>
            <div className="flex items-center mt-2 text-sm">
              <Calendar className="h-4 w-4 text-blue-500 mr-1" />
              <span className="text-blue-500 font-medium">{t("summerMonths")}</span>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("lowestOccupancy")}</p>
            <p className="text-xl font-bold mt-1">60%</p>
            <div className="flex items-center mt-2 text-sm">
              <Calendar className="h-4 w-4 text-red-500 mr-1" />
              <span className="text-red-500 font-medium">{t("winterMonths")}</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Seasonal Occupancy Analysis */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm">
        <h3 className="text-lg font-medium mb-6">{t("seasonalOccupancyTrends")}</h3>
        
        <div className="h-80 w-full">
          <ResponsiveContainer width="99%" height="100%">
            <ReChartsBarChart
              data={seasonalChartData}
              margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="name" axisLine={false} tickLine={false} />
              <YAxis 
                axisLine={false} 
                tickLine={false} 
                yAxisId="left" 
                orientation="left" 
                tickFormatter={(value) => `${value}%`} 
              />
              <YAxis 
                axisLine={false} 
                tickLine={false} 
                yAxisId="right" 
                orientation="right" 
                tickFormatter={(value) => `$${value/1000}K`} 
              />
              <ReChartsTooltip />
              <ReChartsLegend />
              <Bar 
                dataKey="occupancy" 
                name={t("occupancyRate")} 
                yAxisId="left"
                fill="#3b82f6" 
                radius={[4, 4, 0, 0]} 
              />
              <Bar 
                dataKey="revenue" 
                name={t("revenue")} 
                yAxisId="right"
                fill="#10b981" 
                radius={[4, 4, 0, 0]} 
              />
            </ReChartsBarChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
            {t("locationTypePerformance")}
          </h4>
          
          <div className="w-full overflow-x-auto">
            <div className="min-w-[640px]">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b dark:border-gray-700">
                    <th className="text-left py-3 px-4">{t("locationType")}</th>
                    <th className="text-right py-3 px-4">{t("occupancyRate")}</th>
                    <th className="text-right py-3 px-4">{t("averageRevenue")}</th>
                    <th className="text-right py-3 px-4">{t("adCount")}</th>
                  </tr>
                </thead>
                <tbody>
                  {locationBreakdownData.map((location, idx) => (
                    <tr 
                      key={location.area} 
                      className={idx < locationBreakdownData.length - 1 ? "border-b dark:border-gray-700" : ""}
                    >
                      <td className="py-3 px-4">{location.area}</td>
                      <td className="text-right py-3 px-4">
                        <span className={`font-medium ${
                          location.occupancyRate > 85 ? 'text-green-600 dark:text-green-500' :
                          location.occupancyRate > 70 ? 'text-blue-600 dark:text-blue-500' :
                          'text-yellow-600 dark:text-yellow-500'
                        }`}>
                          {location.occupancyRate}%
                        </span>
                      </td>
                      <td className="text-right py-3 px-4 font-medium">
                        ${(location.revenue / location.adsCount).toFixed(0)}
                      </td>
                      <td className="text-right py-3 px-4">{location.adsCount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OccupancyDashboard;

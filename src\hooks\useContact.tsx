import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api/apiClient';
import { Contact } from '@/lib/types/contacts';
import createContactServices from '@/lib/contacts';
export interface ContactApiResponse {
    contacts: Contact[];
  }


  export const useContacts= () => {
    return useQuery<ContactApiResponse>({
      queryKey: ['contacts'],
      queryFn: async () => {
        const { data } = await apiClient.get('/contacts/api/getall/');
        return data;
      },
      staleTime: 10 * 60 * 1000, 
    });
  };

  export function useContactServices() {
    return createContactServices(apiClient);
  }

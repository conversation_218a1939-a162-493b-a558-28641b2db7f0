"use client";

import React, { useState } from "react";
import { MapPin, Plus, Building } from "lucide-react";
import { Card, CardContent } from "@/components/cards/card";
import LocationForm from "@/components/Locations/LocationForm";
import LocationList from "@/components/Locations/LocationList";
import LocationDetails from "@/components/Locations/LocationDetails";
import InlineEditForm from "@/components/Locations/InlineEditForm";
import DefaultLayout from "@/components/Layouts/Layout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { Location } from "@/lib/types/location";
import useLanguage from "@/hooks/useLanguage";
import { usePermissions } from "@/hooks/usePermissions";
import { LoadingComp } from "../common/Loading";

const LocationPage = () => {
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null,
  );
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const { t } = useLanguage();
  const { hasPermission, permissionsLoaded } = usePermissions();

  const handleSaveLocation = (data: Partial<Location>) => {
    console.log("Form submitted with data:", data);

    if (isEditing && selectedLocation) {
      setSelectedLocation({
        ...selectedLocation,
        ...data,
      } as Location);
    }

    setIsFormOpen(false);
    setIsEditing(false);
    setIsEditModalOpen(false);
    setEditingLocation(null);
  };

  const handleEditFromList = (location: Location) => {
    setEditingLocation(location);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setEditingLocation(null);
  };

  // Check permissions loading state
  if (!permissionsLoaded) {
    return (
      <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }

  // Check if user has view permission for locations
  if (!hasPermission("locations", "view")) {
    return (
      <>
        <Breadcrumb pageName={t("locations")} />
        <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
          <div className="flex items-center justify-center h-full">
            <Card className="w-full max-w-md">
              <CardContent className="text-center py-12">
                <Building size={48} className="mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  {t("accessDenied")}
                </h2>
                <p className="text-gray-500 dark:text-gray-400">
                  {t("noPermissionToViewLocations")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Breadcrumb pageName={t("locations")} />
      <div className="min-h-screen bg-gray-50 p-3 dark:bg-boxdark sm:p-4 lg:p-6">
        {/* Title Row */}
        <div className="mb-4 sm:mb-6">
          <div className="flex items-center gap-2 sm:gap-3">
            <Building size={20} className="text-blue-600 sm:size-6" />
            <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100 sm:text-2xl lg:text-3xl">
              {t("locationsManagement")}
            </h1>
          </div>
        </div>

        {/* Add Location Button Section */}
        {!isFormOpen && hasPermission("locations", "create") && (
          <div className="mb-6">
            <Card className="w-full border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/20">
              <CardContent className="p-4 sm:px-6 sm:py-6">
                <div className="flex flex-col items-center justify-between gap-4 sm:flex-row sm:gap-6">
                  <div className="text-center sm:text-left">
                    <h2 className="text-lg font-bold text-blue-800 dark:text-blue-300 sm:text-xl">
                      {t("addNewLocation")}
                    </h2>
                    <p className="mt-1 text-sm text-blue-600 dark:text-blue-400 sm:text-base">
                      {t("createNewLocationDescription")}
                    </p>
                  </div>
                  <button
                    onClick={() => {
                      setIsFormOpen(true);
                      setIsEditing(false);
                    }}
                    className="flex w-full items-center justify-center gap-2 rounded-lg bg-blue-600 px-6 py-3 text-base font-medium text-white shadow-md transition-colors hover:bg-blue-700 hover:shadow-lg sm:w-auto sm:gap-3 sm:px-8 sm:text-lg"
                  >
                    <Plus size={18} className="sm:size-5" />
                    {t("addLocation")}
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {isFormOpen && !isEditing && hasPermission("locations", "create") && (
          <div className="mb-6">
            <Card className="w-full border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/20">
              <CardContent className="p-4 sm:p-6">
                <div className="mb-4 sm:mb-6">
                  <h2 className="text-center text-xl font-bold text-blue-800 dark:text-blue-300 sm:text-2xl">
                    {t("addNewLocation")}
                  </h2>
                  <p className="mt-1 text-center text-sm text-blue-600 dark:text-blue-400 sm:text-base">
                    {t("fillLocationDetails")}
                  </p>
                </div>
                <div className="mx-auto max-w-4xl">
                  <LocationForm
                    isOpen={isFormOpen}
                    onClose={() => setIsFormOpen(false)}
                    onSubmit={handleSaveLocation}
                    view="sidebar"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Add Edit Modal */}
        {isEditModalOpen && editingLocation && (
          <div className="mb-4 sm:mb-6">
            <Card className="w-full border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/20">
              <CardContent className="p-4 sm:p-6">
                <div className="mb-4 sm:mb-6">
                  <h2 className="text-center text-xl font-bold text-blue-800 dark:text-blue-300 sm:text-2xl">
                    {t("editLocation")}
                  </h2>
                  <p className="mt-1 text-center text-sm text-blue-600 dark:text-blue-400 sm:text-base">
                    {t("updateLocationDetails")}
                  </p>
                </div>
                <div className="mx-auto max-w-4xl">
                  <LocationForm
                    isOpen={isEditModalOpen}
                    onClose={handleCloseEditModal}
                    location={editingLocation}
                    onSubmit={handleSaveLocation}
                    view="sidebar"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content Area */}
        <div className="mb-4 sm:mb-6">
          {selectedLocation ? (
            isEditing && hasPermission("locations", "edit") ? (
              <InlineEditForm
                location={selectedLocation}
                onSave={handleSaveLocation}
                onCancel={() => setIsEditing(false)}
              />
            ) : (
              <LocationDetails
                location={selectedLocation}
                onEdit={() => {
                  if (hasPermission("locations", "edit")) {
                    setIsEditing(true);
                  }
                }}
                onDelete={() => {
                  console.log(
                    `Deleting location with ID: ${selectedLocation?.id}`,
                  );
                  setSelectedLocation(null);
                }}
                onClose={() => setSelectedLocation(null)}
                canEdit={hasPermission("locations", "edit")}
                canDelete={hasPermission("locations", "delete")}
                canExport={hasPermission("locations", "export")}
              />
            )
          ) : (
            <Card className="flex w-full items-center justify-center dark:border-gray-700 dark:bg-gray-800">
              <CardContent className="py-8 text-center sm:py-12">
                <MapPin
                  size={40}
                  className="mx-auto mb-4 text-gray-400 sm:size-12"
                />
                <h3 className="mb-2 text-lg font-medium text-gray-700 dark:text-gray-300 sm:text-xl">
                  {t("selectALocationToViewDetails")}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 sm:text-base">
                  {t(
                    "Choose a location from the list below to see its details",
                  )}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
        {!selectedLocation && (
          <div>
            <LocationList
              onSelect={(location: Location) => setSelectedLocation(location)}
              onEdit={handleEditFromList}
              canEdit={hasPermission("locations", "edit")}
              canDelete={hasPermission("locations", "delete")}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default LocationPage;

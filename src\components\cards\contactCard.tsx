import React from "react";
import {
  Mail,
  Phone,
  MapPin,
  User<PERSON><PERSON><PERSON>,
  Users,
  Building,
  MoreVertical,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/cards/card";
import { Contact } from "@/lib/types/contacts";
import useLanguage from "@/hooks/useLanguage";

interface ContactCardProps {
  contact: Contact;
  onSelect: (contact: Contact) => void;
  isSelected?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
}

const ContactCard: React.FC<ContactCardProps> = ({
  contact,
  onSelect,
  isSelected = false,
  canEdit = true,
  canDelete = true
}) => {
  const { t } = useLanguage();

  return (
    <Card
      onClick={() => onSelect(contact)}
      className={`cursor-pointer transition-all duration-200 hover:shadow-md dark:bg-boxdark lg:translate-x-0 ${
        isSelected
          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
          : "border-gray-200 hover:border-blue-300 dark:border-gray-700 dark:hover:border-blue-600"
      }`}
    >
      <CardHeader className="p-3 pb-2 sm:p-4 sm:pb-3">
        <div className="flex w-full items-start justify-between">
          <div className="min-w-0 flex-1">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <span className="truncate">{contact.name}</span>
              {contact.type.includes("client") && (
                <UserCheck
                  size={14}
                  className="flex-shrink-0 text-green-500 sm:size-4"
                />
              )}
              {contact.type.includes("agency") && (
                <Users
                  size={14}
                  className="flex-shrink-0 text-blue-500 sm:size-4"
                />
              )}
            </CardTitle>
            {contact.company && (
              <CardDescription className="flex items-center gap-2 text-sm">
                <Building
                  size={12}
                  className="flex-shrink-0 text-gray-400 sm:size-3.5"
                />
                <span className="truncate">{contact.company}</span>
              </CardDescription>
            )}
          </div>

          <div className="ml-2 transform text-right transition-transform duration-200 hover:scale-105 sm:ml-4">
            <p className="mb-1 text-xs font-medium text-gray-500 sm:text-sm">
              {t("balance")}
            </p>
            {(() => {
              const invertedBalance = (contact.balance || 0) * -1;
              let colorClass = "text-blue-600 dark:text-blue-400";
              if (invertedBalance > 0)
                colorClass = "text-green-600 dark:text-green-400";
              else if (invertedBalance < 0)
                colorClass = "text-red-600 dark:text-red-400";
              return (
                <p
                  className={`text-lg font-bold tracking-tight sm:text-xl lg:text-2xl ${colorClass}`}
                >
                  <span
                    style={{ direction: "ltr", unicodeBidi: "isolate" }}
                    className="inline-flex"
                  >
                    {invertedBalance > 0 ? "+" : invertedBalance < 0 ? "-" : ""}
                    {Math.abs(invertedBalance).toLocaleString("en-US", {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    })}{" "}
                    <span className="text-sm sm:text-base lg:text-lg">
                      {t("EGP")}
                    </span>
                  </span>
                </p>
              );
            })()}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-3 pt-0 sm:p-4 sm:pt-0">
        {/* Desktop/Large Screen Layout */}
        {contact.email || contact.phone || contact.address ? (
          <div className="hidden grid-cols-2 gap-3 text-sm text-gray-600 dark:text-gray-400 sm:grid lg:grid-cols-3 lg:gap-4">
            {contact.email && (
              <div className="flex items-center gap-2">
                <Mail
                  size={14}
                  className="flex-shrink-0 text-blue-500 sm:size-4"
                />
                <span className="truncate">{contact.email}</span>
              </div>
            )}
            {contact.phone && (
              <div className="flex items-center gap-2">
                <Phone
                  size={14}
                  className="flex-shrink-0 text-green-500 sm:size-4"
                />
                <span className="truncate">{contact.phone}</span>
              </div>
            )}
            {contact.address && (
              <div className="flex items-center gap-2 lg:col-span-1">
                <MapPin
                  size={14}
                  className="flex-shrink-0 text-red-500 sm:size-4"
                />
                <span className="truncate">{contact.address}</span>
              </div>
            )}
          </div>
        ) : null}

        {/* Mobile/Small Screen Layout */}
        <div className="flex flex-col space-y-1.5 text-sm text-gray-600 dark:text-gray-400 sm:hidden">
          {contact.email && (
            <div className="flex items-center gap-2">
              <Mail size={14} className="flex-shrink-0 text-blue-500" />
              <span className="truncate">{contact.email}</span>
            </div>
          )}
          {contact.phone && (
            <div className="flex items-center gap-2">
              <Phone size={14} className="flex-shrink-0 text-green-500" />
              <span className="truncate">{contact.phone}</span>
            </div>
          )}
        </div>

        {/* Location Summary - Always Visible */}
        <div className="mt-2 space-y-1 text-xs text-gray-500 dark:text-gray-400 sm:mt-3 sm:text-sm">
          {contact.sharedLocations && contact.sharedLocations.length > 0 && (
            <div className="flex items-start gap-1">
              <span className="font-medium">{t("shared")}:</span>
              <span className="truncate">
                {contact.sharedLocations
                  .slice(0, 2)
                  .map((loc) => loc.name)
                  .join(", ")}
                {contact.sharedLocations.length > 2 &&
                  ` +${contact.sharedLocations.length - 2}`}
              </span>
            </div>
          )}
          {contact.ownedLocations && contact.ownedLocations.length > 0 && (
            <div className="flex items-start gap-1">
              <span className="font-medium">{t("owned")}:</span>
              <span className="truncate">
                {contact.ownedLocations
                  .slice(0, 2)
                  .map((loc) => loc.name)
                  .join(", ")}
                {contact.ownedLocations.length > 2 &&
                  ` +${contact.ownedLocations.length - 2}`}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ContactCard;

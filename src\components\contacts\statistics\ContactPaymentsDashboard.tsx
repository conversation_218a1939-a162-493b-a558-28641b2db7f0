import React from 'react';
import { Button } from '@/components/ui/button';
import { Mail } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import useLanguage from "@/hooks/useLanguage";
import {
  <PERSON><PERSON>hart as ReChartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ReChartsTooltip,
  Legend as <PERSON><PERSON><PERSON>sLegend,
  ResponsiveContainer,
  PieChart as ReChartsPieChart,
  Pie,
  Cell
} from 'recharts';

interface ContactPaymentsDashboardProps {
  paymentData: Array<{
    period: string;
    onTime: number;
    late: number;
    outstanding: number;
  }>;
  paymentStatusData: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  paymentMethodData: Array<{
    name: string;
    value: number;
  }>;
  paymentTransactions: Array<{
    id: string;
    date: Date | null;
    amount: number;
    status: string;
    method: string | null;
    description: string;
    dueDate: Date;
  }>;
  paymentMetrics: {
    totalPaid: number;
    totalOnTime: number;
    totalLate: number;
    totalOutstanding: number;
    onTimeRate: number;
    paidAmount: number;
    pendingAmount: number;
    totalTransactions: number;
  };
  COLORS: string[];
}

const ContactPaymentsDashboard: React.FC<ContactPaymentsDashboardProps> = ({
  paymentData,
  paymentStatusData,
  paymentMethodData,
  paymentTransactions,
  paymentMetrics,
  COLORS
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium">{t("paymentHistory")}</h3>
          <Button variant="outline" size="sm" className="h-8">
            {t("exportData")}
          </Button>
        </div>
        
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <ReChartsBarChart
              data={paymentData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="period" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} />
              <ReChartsTooltip />
              <ReChartsLegend />
              <Bar 
                dataKey="onTime" 
                name={t("onTimePayments")} 
                stackId="a" 
                fill="#10b981" 
              />
              <Bar 
                dataKey="late" 
                name={t("latePayments")} 
                stackId="a" 
                fill="#f59e0b" 
              />
              <Bar 
                dataKey="outstanding" 
                name={t("outstandingPayments")} 
                stackId="a" 
                fill="#3b82f6" 
              />
            </ReChartsBarChart>
          </ResponsiveContainer>
        </div>
        
        <div className="grid grid-cols-3 gap-4 mt-6">
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("totalPaidAmount")}</p>
            <p className="text-xl font-bold mt-1">${paymentMetrics.paidAmount.toLocaleString()}</p>
            <div className="flex items-center mt-2 text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                {paymentMetrics.totalPaid} {t("transactions")}
              </span>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("pendingAmount")}</p>
            <p className="text-xl font-bold mt-1">${paymentMetrics.pendingAmount.toLocaleString()}</p>
            <div className="flex items-center mt-2 text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                {paymentMetrics.totalOutstanding} {t("transactions")}
              </span>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("onTimePaymentRate")}</p>
            <p className="text-xl font-bold mt-1">{paymentMetrics.onTimeRate}%</p>
            <div className="flex items-center mt-2 text-sm">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                <div 
                  className="bg-green-500 h-1.5 rounded-full" 
                  style={{ width: `${paymentMetrics.onTimeRate}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Payment Status Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-medium mb-4">{t("paymentStatus")}</h3>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <ReChartsPieChart>
                <Pie
                  data={paymentStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {paymentStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <ReChartsTooltip formatter={(value: any) => [value, t("payments")]} />
              </ReChartsPieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="flex justify-center gap-4 mt-2">
            {paymentStatusData.map((status, idx) => (
              <div key={status.name} className="flex items-center">
                <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: status.color }} />
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {status.name}: {((status.value / paymentMetrics.totalTransactions) * 100).toFixed(0)}%
                </span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-medium mb-4">{t("paymentMethods")}</h3>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <ReChartsPieChart>
                <Pie
                  data={paymentMethodData}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {paymentMethodData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <ReChartsTooltip formatter={(value: any) => [value, t("transactions")]} />
              </ReChartsPieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
            {t("basedOnCompletedPayments")}
          </div>
        </div>
      </div>
      
      {/* Payment Records */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium">{t("recentPayments")}</h3>
          <Button variant="outline" size="sm">
            {t("viewAll")}
          </Button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b dark:border-gray-700">
                <th className="text-left py-3 px-4">{t("description")}</th>
                <th className="text-left py-3 px-4">{t("dueDate")}</th>
                <th className="text-left py-3 px-4">{t("paymentDate")}</th>
                <th className="text-right py-3 px-4">{t("amount")}</th>
                <th className="text-center py-3 px-4">{t("status")}</th>
                <th className="text-left py-3 px-4">{t("method")}</th>
              </tr>
            </thead>
            <tbody>
              {paymentTransactions.slice(0, 5).map((transaction) => (
                <tr 
                  key={transaction.id} 
                  className="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50"
                >
                  <td className="py-3 px-4 font-medium">
                    {transaction.description}
                  </td>
                  <td className="py-3 px-4">
                    {transaction.dueDate.toLocaleDateString()}
                  </td>
                  <td className="py-3 px-4">
                    {transaction.date ? transaction.date.toLocaleDateString() : '—'}
                  </td>
                  <td className="text-right py-3 px-4 font-medium">
                    ${transaction.amount.toLocaleString()}
                  </td>
                  <td className="text-center py-3 px-4">
                    {transaction.status === 'paid' ? (
                      <Badge className="bg-green-100 text-white dark:bg-green-900/20 dark:text-green-300">
                        {t("paid")}
                      </Badge>
                    ) : transaction.status === 'paid-late' ? (
                      <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300">
                        {t("paidLate")}
                      </Badge>
                    ) : (
                      <Badge className="bg-blue-100 text-white dark:bg-blue-900/20 dark:text-blue-300">
                        {t("pending")}
                      </Badge>
                    )}
                  </td>
                  <td className="py-3 px-4">
                    {transaction.method ? (
                      transaction.method === 'credit_card' ? t("creditCard") :
                      transaction.method === 'bank_transfer' ? t("bankTransfer") :
                      t("check")
                    ) : '—'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Payment Analytics */}
        <div className="mt-8 border-t dark:border-gray-700 pt-6">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
            {t("paymentAnalytics")}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">{t("averageDaysLate")}</p>
              <p className="text-xl font-bold mt-1 text-gray-900 dark:text-gray-100">
                {paymentMetrics.totalLate > 0 ? '5.3' : '0'}
              </p>
              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                {t("forLatePayments")}
              </div>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">{t("averageTransactionSize")}</p>
              <p className="text-xl font-bold mt-1 text-gray-900 dark:text-gray-100">
                ${Math.round((paymentMetrics.paidAmount + paymentMetrics.pendingAmount) / 
                  paymentMetrics.totalTransactions).toLocaleString()}
              </p>
              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                {t("perTransaction")}
              </div>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">{t("outstandingBalance")}</p>
              <p className="text-xl font-bold mt-1 text-gray-900 dark:text-gray-100">
                ${paymentMetrics.pendingAmount.toLocaleString()}
              </p>
              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                {t("fromPendingPayments")}
              </div>
            </div>
          </div>
        </div>
        
        {/* Suggest action */}
        {paymentMetrics.pendingAmount > 0 && (
          <div className="mt-6 p-4 border border-blue-100 dark:border-blue-900/40 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="text-blue-500 mt-1">
                <Mail className="h-5 w-5" />
              </div>
              <div>
                <h5 className="font-medium text-blue-700 dark:text-blue-400">{t("contactAboutPayments")}</h5>
                <p className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                  {t("contactHasOutstandingPayments")}{" : "}{ paymentMetrics.totalOutstanding}
                </p>
                <div className="flex gap-2 mt-2">
                  <Button size="sm" variant="default" className="bg-blue-600 hover:bg-blue-700 text-white">
                    {t("sendReminder")}
                  </Button>
                  <Button size="sm" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                    {t("scheduleCall")}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContactPaymentsDashboard;

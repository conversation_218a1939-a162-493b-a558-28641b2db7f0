"use client";

import React from 'react';
import { useParams } from 'next/navigation';
import Layout from '@/components/Layouts/Layout';
import LocationHistory from '@/components/Locations/LocationHistory';

const LocationHistoryPage = () => {
  const params = useParams();
  const locationId = params?.id as string | undefined;

  if (!locationId) {
    return <div>Error: Location ID not found.</div>;
  }

  return (
    <Layout>
      <LocationHistory locationId={locationId} />
    </Layout>
  );
};

export default LocationHistoryPage;

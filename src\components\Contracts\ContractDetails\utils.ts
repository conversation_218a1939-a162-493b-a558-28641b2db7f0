// Utility functions for Contract Details components

// Safe value handling
export const safeValue = (value: any, defaultValue: any = "N/A") => {
  return value !== undefined && value !== null ? value : defaultValue;
};

// Date formatting
export const formatDate = (dateString: string | undefined) => {
  return dateString ? new Date(dateString).toLocaleDateString() : "N/A";
};

// Currency formatting
export const formatCurrency = (amount: number | undefined) => {
  return amount !== undefined && amount !== null
    ? new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 0,
      }).format(amount)
    : "N/A";
};

// Status badge styling
export const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
    case "pending":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
    case "expired":
      return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
    case "terminated":
      return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
    case "draft":
      return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
  }
};

// Payment status badge styling
export const getPaymentStatusBadgeClass = (status: string) => {
  switch (status) {
    case "up-to-date":
      return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
    case "overdue":
      return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
    case "partially-paid":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
  }
};

// Calculate contract duration in months
export const getContractDuration = (startDate: string, endDate: string) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());
  return Math.max(0, months);
};

// Get the time until contract expiration
export const getTimeUntilExpiration = (endDate: string, t: (key: string) => string, language?: string) => {
  const today = new Date();
  const expirationDate = new Date(endDate);
  const diffTime = expirationDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return t("expired");
  } else if (diffDays === 0) {
    return t("expiresToday");
  } else if (diffDays === 1) {
    return t("expiresTomorrow");
  } else if (diffDays <= 30) {
    const formattedDays = Number(diffDays).toLocaleString(
      language === "ar" ? "ar-EG" : "en-US"
    );
    return `${formattedDays} ${t("daysRemaining")}`;
  } else if (diffDays <= 365) {
    const months = Math.floor(diffDays / 30);
    const formattedMonths = Number(months).toLocaleString(
      language === "ar" ? "ar-EG" : "en-US"
    );
    return `${formattedMonths} ${t("monthsRemaining")}`;
  } else {
    const years = Math.floor(diffDays / 365);
    const formattedYears = Number(years).toLocaleString(
      language === "ar" ? "ar-EG" : "en-US"
    );
    return `${formattedYears} ${t("yearsRemaining")}`;
  }
};

// Calculate days until next payment
export const getDaysUntilNextPayment = (paymentDay: number, t: (key: string) => string) => {
  const today = new Date();
  // Get current month and year
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();
  
  // Create date object for payment date this month
  const paymentDate = new Date(currentYear, currentMonth, paymentDay);
  
  // If payment date has passed this month, look at next month
  if (today > paymentDate) {
    paymentDate.setMonth(paymentDate.getMonth() + 1);
  }
  
  // Calculate days difference
  const diffTime = paymentDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return t("Payment due today");
  } else {
    return t("Payment due in") + " " + diffDays + " " + t("days");
  }
};

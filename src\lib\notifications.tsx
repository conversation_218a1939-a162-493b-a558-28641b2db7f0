import { AxiosInstance } from "axios";
import apiClient from "./api/apiClient";

// Interfaces for notification data structures
interface NotificationType {
  id: string;
  name: string;
  created_at: string;
}

export interface Notification {
  id: string;
  title: string;
  title_ar: string;
  message: string;
  message_ar: string;
  priority: string;
  priority_display: string;
  type: NotificationType;
  read_by: string[];
  created_at: string;
  is_read: boolean;
  view_date: string | null;
  category: string;
  category_id: string;
}

interface NotificationFilters {
  read?: boolean;
  priority?: string;
  type_id?: string;
  search?: string;
}

const NotificationServices = () => ({
  // Get unread notification count
  getUnreadCount: async (signal?: AbortSignal): Promise<number> => {
    try {
      const response = await apiClient.get("/notifications/api/unread-count/", {
        signal: signal,
      });

      // Check for different response formats
      if (typeof response.data === "number") {
        return response.data;
      } else if (response.data && typeof response.data.count === "number") {
        return response.data.count;
      } else if (
        response.data &&
        typeof response.data.unread_count === "number"
      ) {
        // Add this condition to handle the specific response format
        return response.data.unread_count;
      }

      // If we can't find a valid count, log the response and return 0
      console.error(
        "Unexpected unread notification count response format:",
        response.data,
      );
      return 0;
    } catch (error) {
      // Check if this is an abort error
      if (
        (error as any).name === "AbortError" ||
        (error as any).code === "ECONNABORTED"
      ) {
        console.log("Unread notification count request was cancelled");
        return 0;
      }
      console.error("Error fetching unread notification count:", error);
      return 0;
    }
  },

  // List notifications with optional filtering
  getNotifications: async (
    filters?: NotificationFilters,
    signal?: AbortSignal,
  ): Promise<Notification[]> => {
    try {
      console.log("Making API call to fetch notifications...");

      // Prepare query parameters from filters
      let queryParams = new URLSearchParams();
      if (filters) {
        if (filters.read !== undefined) {
          queryParams.append("read", filters.read.toString());
        }
        if (filters.priority) {
          queryParams.append("priority", filters.priority);
        }
        if (filters.type_id) {
          queryParams.append("type_id", filters.type_id);
        }
        if (filters.search) {
          queryParams.append("search", filters.search);
        }
      }

      // Construct URL with query parameters
      const queryString = queryParams.toString();
      const url = `/notifications/api/getall/${queryString ? `?${queryString}` : ""}`;

      const response = await apiClient.get(url, {
        signal: signal,
      });

      console.log("Raw notifications API response:", response);

      if (!response.data) {
        console.error("Notifications API response has no data property");
        return [];
      }

      // Check for 'notifications' property first
      if (response.data.notifications) {
        console.log(
          "Found notifications property in response, count:",
          response.data.notifications.length,
        );
        return response.data.notifications as Notification[];
      }

      // If no notifications property exists, check if response.data is an array directly
      if (Array.isArray(response.data)) {
        console.log(
          "Response data is an array, returning directly, count:",
          response.data.length,
        );
        return response.data;
      }

      console.error(
        "Could not find notifications data in the response:",
        response.data,
      );
      return [];
    } catch (error) {
      // Check if this is an abort error
      if (
        (error as any).name === "AbortError" ||
        (error as any).code === "ECONNABORTED"
      ) {
        console.log("Notifications API request was cancelled");
        return [];
      }
      console.error("Error fetching notifications:", error);
      throw error;
    }
  },

  // Mark notification as read
  markAsRead: async (notificationId: string): Promise<boolean> => {
    try {
      console.log(`Marking notification ${notificationId} as read...`);
      const response = await apiClient.post(
        `/notifications/api/markasread/${notificationId}/`,
      );
      console.log("Mark as read response:", response);

      return response.status === 200;
    } catch (error) {
      console.error(
        `Error marking notification ${notificationId} as read:`,
        error,
      );
      return false;
    }
  },

  // Mark notification as unread
  markAsUnread: async (notificationId: string): Promise<boolean> => {
    try {
      console.log(`Marking notification ${notificationId} as unread...`);
      const response = await apiClient.post(
        `/notifications/api/markasunread/${notificationId}/`,
      );
      console.log("Mark as unread response:", response);

      return response.status === 200;
    } catch (error) {
      console.error(
        `Error marking notification ${notificationId} as unread:`,
        error,
      );
      return false;
    }
  },
});

export default NotificationServices;

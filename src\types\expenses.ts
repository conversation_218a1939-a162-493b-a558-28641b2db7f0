// Expense types based on API specifications

export interface UpdateExpensePayload {
  title: string;
  amount: number; // decimal with max 12 digits, 2 decimal places
  due_date: string; // ISO format: YYYY-MM-DDTHH:MM:SSZ
  paid_date?: string | null; // ISO format, optional
  description?: string;
  status?: "completed" | "pending" | "cancelled" | "upcoming" | "overdue";
  priority?: "low" | "medium" | "high";
  type_id?: string; // UUID
}

export interface UpdateExpenseStatusPayload {
  event_id: string;
  status: "completed" | "pending" | "cancelled" | "upcoming" | "overdue";
  paid_date?: string; // ISO format, automatically sets status to "completed" if provided
}

export interface ExpenseEditResponse {
  id: string;
  title: string;
  amount: number;
  due_date: string;
  paid_date?: string | null;
  description?: string;
  status: "completed" | "pending" | "cancelled" | "upcoming" | "overdue";
  priority: "low" | "medium" | "high";
  type_id?: string;
  contact: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  location: {
    id: string;
    name: string;
    address?: string;
  };
  reservation_id?: string | null;
  contract_id?: string | null;
  actual_amount?: number;
  status_display?: string;
  created_at: string;
  updated_at: string;
  history_records?: any[];
}

export interface ExpenseEditError {
  error: string;
  can_update: boolean;
  reservation?: {
    id: string;
    title: string;
    start_date: string;
    end_date: string;
  };
  contract?: {
    id: string;
    title: string;
    start_date: string;
    end_date: string;
  };
  details?: {
    [field: string]: string[];
  };
}

// Helper function to check if expense can be edited
export const canEditExpense = (expense: any): boolean => {
  return !expense.reservation_id && !expense.contract_id;
};

// Helper function to format amount for API
export const formatAmountForAPI = (amount: number): number => {
  return Math.round(amount * 100) / 100; // Ensure 2 decimal places
};

// Helper function to validate ISO date format
export const isValidISODate = (dateString: string): boolean => {
  const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
  return isoRegex.test(dateString) && !isNaN(Date.parse(dateString));
};

// Helper function to convert date to ISO format
export const toISOString = (date: Date | string): string => {
  if (typeof date === 'string') {
    return new Date(date).toISOString();
  }
  return date.toISOString();
};

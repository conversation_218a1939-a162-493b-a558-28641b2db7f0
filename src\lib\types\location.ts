export interface Reservation {
  id: string;
  title: string;
  clientId: string;
  clientName: string;
  capacity: number;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'pending' | 'completed';
}

export interface PartnerShare {
  name: string;
  percentage: number;
}

export interface Location {
  id: string;
  name: string;
  address: string;
  description: string;
  type: string;
  status: 'active' | 'inactive';
  capacity: number;
  ourPercentage: number;
  ownedBy: string;
  sharedWith: string[];
  primaryOwner?: {
    id: string;
    name: string;
    percentage: number;
  };
  ownershipShares?: PartnerShare[];
  reservations: Reservation[];
  reservedBy: string;
  reservedFrom: Date;
  reservedUntil: Date;
  lastlyReservedIn: Date;
  createdAt: Date;
  createdBy: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  areWeOwner?: boolean;
  res_start_date?: Date;
  res_end_date?: Date;
  weHaveShares?: boolean;
}
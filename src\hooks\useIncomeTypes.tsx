import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api/apiClient';
import { IncomeType } from '@/lib/interfaces/eventTypes';
import createIncomeTypeServices from '@/lib/incomeTypes';

export interface IncomeTypeApiResponse {
  income_types: IncomeType[];
  }


  export const useIncomeTypes= () => {
    return useQuery<IncomeTypeApiResponse>({
      queryKey: ['income_types'],
      queryFn: async () => {
        const { data } = await apiClient.get('/income/api/types/');
        return data;
      },
      staleTime: 10 * 60 * 1000, 
    });
  };

  export function useIncomeTypeServices() {
    return createIncomeTypeServices(apiClient);
  }

export interface Privileges {
  componentsToView: {
    dashboard: boolean;
    products: boolean;
    customers: boolean;
    reservations: boolean;
    analytics: boolean;
    settings: boolean;
    users: boolean;
    reports: boolean;
    inventory: boolean;
    marketing: boolean;
    // Add more component permissions as needed
  };
  
  notificationsToReceive: {
    newOrders: boolean;
    paymentConfirmations: boolean;
    productAlerts: boolean;
    customerMessages: boolean;
    systemUpdates: boolean;
    securityAlerts: boolean;
    marketingReports: boolean;
    stockAlerts: boolean;
    // Add more notification permissions as needed
  };
  
  sidebarItems: {
    dashboard: boolean;
    products: boolean;
    customers: boolean;
    orders: boolean;
    analytics: boolean;
    settings: boolean;
    users: boolean;
    reports: boolean;
    inventory: boolean;
    marketing: boolean;
    // Add more sidebar permissions as needed
  };
}

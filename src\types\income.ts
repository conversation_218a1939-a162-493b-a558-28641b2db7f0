// Income types based on API specifications

export interface UpdateIncomePayload {
  title: string;
  amount: number; // decimal with max 12 digits, 2 decimal places
  due_date: string; // ISO format: YYYY-MM-DDTHH:MM:SSZ
  received_date?: string | null; // ISO format, optional
  description?: string;
  status?: "completed" | "pending" | "cancelled" | "upcoming" | "overdue";
  priority?: "low" | "medium" | "high";
  type_id?: string; // UUID
}

export interface UpdateIncomeStatusPayload {
  income_id: string;
  status: "completed" | "pending" | "cancelled" | "upcoming" | "overdue";
  received_date?: string; // ISO format, automatically sets status to "completed" if provided
}

export interface IncomeEditResponse {
  id: string;
  title: string;
  amount: number;
  due_date: string;
  received_date?: string | null;
  description?: string;
  status: "completed" | "pending" | "cancelled" | "upcoming" | "overdue";
  priority: "low" | "medium" | "high";
  type_id?: string;
  contact: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  location: {
    id: string;
    name: string;
    address?: string;
  };
  reservation_id?: string | null;
  contract_id?: string | null;
  actual_amount?: number;
  status_display?: string;
  created_at: string;
  updated_at: string;
  history_records?: any[];
}

export interface IncomeEditError {
  error: string;
  can_update: boolean;
  reservation?: {
    id: string;
    title: string;
    start_date: string;
    end_date: string;
  };
  contract?: {
    id: string;
    title: string;
    start_date: string;
    end_date: string;
  };
  details?: {
    [field: string]: string[];
  };
}

// Helper function to check if income can be edited
export const canEditIncome = (income: any): boolean => {
  return !income.reservation_id && !income.contract_id;
};

// Helper function to format amount for API
export const formatAmountForAPI = (amount: number): number => {
  return Math.round(amount * 100) / 100; // Ensure 2 decimal places
};

// Helper function to validate ISO date format
export const isValidISODate = (dateString: string): boolean => {
  const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
  return isoRegex.test(dateString) && !isNaN(Date.parse(dateString));
};

// Helper function to convert date to ISO format
export const toISOString = (date: Date | string): string => {
  if (typeof date === 'string') {
    return new Date(date).toISOString();
  }
  return date.toISOString();
};

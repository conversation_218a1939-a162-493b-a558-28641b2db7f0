export interface ReservationHistory {
    id: string;
    locationId: string;
    locationName: string;
    startDate: Date;
    endDate: Date;
    totalAmount: number;
    ourPercentage: number;
    agencyPercentage?: number;
    status: 'completed' | 'ongoing' | 'cancelled';
    payments: {
      date: Date;
      amount: number;
      type: 'instalment' | 'final';
      status: 'paid' | 'pending' | 'overdue';
    }[];
  }
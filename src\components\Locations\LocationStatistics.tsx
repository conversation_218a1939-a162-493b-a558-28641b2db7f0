"use client";

import React, { useState, useMemo } from 'react';
import { 
  Card, CardHeader, CardTitle, CardContent, CardDescription 
} from '@/components/cards/card';
import { BarChart3 } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import useLanguage from "@/hooks/useLanguage";
import { DateRange } from "react-day-picker";

// Import modular components
import StatisticsOverview from './statistics/StatisticsOverview';
import RevenueDashboard from './statistics/RevenueDashboard';
import OccupancyDashboard from './statistics/OccupancyDashboard';
import ClientsDashboard from './statistics/ClientsDashboard';

// Import Recharts for shared types and active shape renderer
import { Sector } from 'recharts';

// Define interfaces for the component
interface LocationStatisticsProps {
  locationId: string;
  locationName?: string;
}

const LocationStatistics: React.FC<LocationStatisticsProps> = ({ locationId, locationName }) => {
  const { t } = useLanguage();
  
  // State hooks for filtering and view options
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1),
    to: new Date()
  });
  const [activeTab, setActiveTab] = useState("revenue");
  const [timeframe, setTimeframe] = useState("monthly");
  const [activePieIndex, setActivePieIndex] = useState(0);
  
  // Mock data generation - in a real app, this would be API calls
  const revenueData = useMemo(() => {
    // Generate 12 months of data with seasonality
    const data: Array<{
      period: string;
      revenue: number;
      projectedRevenue: number;
      lastYearRevenue: number;
    }> = [];
    const today = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthName = month.toLocaleString('default', { month: 'short' });
      
      // Create realistic revenue patterns with seasonality
      const baseRevenue = 15000 + Math.random() * 8000;
      // Higher revenue in summer and December
      const monthFactor = month.getMonth();
      let seasonalMultiplier = 1;
      
      // Summer boost (May-August)
      if (monthFactor >= 4 && monthFactor <= 7) {
        seasonalMultiplier = 1.3;
      }
      // Holiday season boost (December)
      else if (monthFactor === 11) {
        seasonalMultiplier = 1.4;
      }
      // Winter lull (January-February)
      else if (monthFactor <= 1) {
        seasonalMultiplier = 0.8;
      }
      
      const revenue = Math.round(baseRevenue * seasonalMultiplier);
      const lastYearRevenue = Math.round(revenue * (0.85 + Math.random() * 0.15));
      const projectedRevenue = i < 2 ? Math.round(revenue * 1.1) : revenue;
      
      data.push({
        period: `${monthName} ${month.getFullYear()}`,
        revenue: revenue,
        projectedRevenue: projectedRevenue,
        lastYearRevenue: lastYearRevenue
      });
    }
    
    return data;
  }, []);

  const occupancyData = useMemo(() => {
    // Generate occupancy data with seasonal patterns
    const data: Array<{
      period: string;
      occupancyRate: number;
      availableRate: number;
    }> = [];
    const today = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthName = month.toLocaleString('default', { month: 'short' });
      
      // Create realistic occupancy patterns with seasonal effects
      const monthFactor = month.getMonth();
      let baseOccupancy = 65; // Base occupancy percentage
      
      // Summer boost (May-August)
      if (monthFactor >= 4 && monthFactor <= 7) {
        baseOccupancy = 85;
      }
      // Holiday season boost (December)
      else if (monthFactor === 11) {
        baseOccupancy = 90;
      }
      // Winter lull (January-February)
      else if (monthFactor <= 1) {
        baseOccupancy = 55;
      }
      
      // Add some randomness
      const occupancyRate = Math.min(98, Math.round(baseOccupancy + (Math.random() * 10 - 5)));
      
      data.push({
        period: `${monthName} ${month.getFullYear()}`,
        occupancyRate: occupancyRate,
        availableRate: 100 - occupancyRate
      });
    }
    
    return data;
  }, []);

  const clientData = useMemo(() => {
    // Sample top clients data
    return [
      { name: "Coca-Cola", revenue: 65000, reservations: 12, adTypes: ["Billboard", "Digital Display"] },
      { name: "Apple", revenue: 58000, reservations: 8, adTypes: ["Premium Billboard", "Interactive Digital"] },
      { name: "Nike", revenue: 42000, reservations: 10, adTypes: ["Street Banner", "Billboard"] },
      { name: "Samsung", revenue: 38000, reservations: 9, adTypes: ["Digital Display", "Bus Stop Ad"] },
      { name: "Adidas", revenue: 31000, reservations: 7, adTypes: ["Billboard", "Street Banner"] },
      { name: "McDonald's", revenue: 28000, reservations: 14, adTypes: ["Street Banner", "Bus Stop Ad"] },
      { name: "Amazon", revenue: 24000, reservations: 6, adTypes: ["Digital Display", "Billboard"] },
      { name: "Sony", revenue: 21000, reservations: 5, adTypes: ["Premium Billboard"] }
    ];
  }, []);


  const locationBreakdownData = useMemo(() => {
    // Sample data for different location types
    return [
      { area: "Downtown", revenue: 112000, occupancyRate: 92, adsCount: 18 },
      { area: "Shopping District", revenue: 89000, occupancyRate: 87, adsCount: 15 },
      { area: "Business Park", revenue: 67000, occupancyRate: 78, adsCount: 12 },
      { area: "Tourist Area", revenue: 62000, occupancyRate: 85, adsCount: 10 },
      { area: "Residential", revenue: 43000, occupancyRate: 65, adsCount: 14 },
      { area: "Transportation Hub", revenue: 38000, occupancyRate: 72, adsCount: 8 }
    ];
  }, []);

  // Calculate aggregate metrics
  const totalRevenue = useMemo(() => 
    revenueData.reduce((sum, item) => sum + item.revenue, 0), 
  [revenueData]);

  const averageOccupancy = useMemo(() => 
    Math.round(occupancyData.reduce((sum, item) => sum + item.occupancyRate, 0) / occupancyData.length), 
  [occupancyData]);


  const totalClients = clientData.length;

  const revenueGrowth = useMemo(() => {
    if (revenueData.length < 2) return 0;
    const currentRevenue = revenueData[revenueData.length - 1].revenue;
    const previousRevenue = revenueData[revenueData.length - 2].revenue;
    return previousRevenue !== 0 ? Math.round((currentRevenue - previousRevenue) / previousRevenue * 100) : 0;
  }, [revenueData]);

  // Format chart data
  const clientRevenuePieData = useMemo(() => 
    clientData.map(client => ({
      name: client.name,
      value: client.revenue
    })),
  [clientData]);
  

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#e06666', '#66e066', '#6681e0'];

  // Create an interactive tooltip for pie charts
  const renderActiveShape = (props: any) => {
    const RADIAN = Math.PI / 180;
    const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle,
      fill, payload, percent, value } = props;
    const sin = Math.sin(-RADIAN * midAngle);
    const cos = Math.cos(-RADIAN * midAngle);
    const sx = cx + (outerRadius + 10) * cos;
    const sy = cy + (outerRadius + 10) * sin;
    const mx = cx + (outerRadius + 30) * cos;
    const my = cy + (outerRadius + 30) * sin;
    const ex = mx + (cos >= 0 ? 1 : -1) * 22;
    const ey = my;
    const textAnchor = cos >= 0 ? 'start' : 'end';
  
    return (
      <g>
        <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill}>
          {payload.name}
        </text>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
        <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
        <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
        <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#333">{`$${value.toLocaleString()}`}</text>
        <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={18} textAnchor={textAnchor} fill="#999">
          {`(${(percent * 100).toFixed(2)}%)`}
        </text>
      </g>
    );
  };

  // Create time-period specific data for chart visualizations
  const weeklyRevenueData = useMemo(() => {
    const data: Array<{
      period: string;
      revenue: number;
      projectedRevenue: number;
      lastYearRevenue: number;
    }> = [];
    
    // Generate weekly data from the last 3 months of revenueData
    revenueData.slice(-3).forEach(monthData => {
      const [month, year] = monthData.period.split(' ');
      for (let week = 1; week <= 4; week++) {
        // Use a weighted distribution to make weekly data realistic
        const weekFactor = week === 3 ? 1.2 : week === 2 ? 1.1 : week === 4 ? 0.9 : 0.8;
        
        const weeklyRevenue = Math.round(monthData.revenue / 4 * weekFactor);
        const weeklyProjected = Math.round(monthData.projectedRevenue / 4 * weekFactor);
        const weeklyLastYear = Math.round(monthData.lastYearRevenue / 4 * weekFactor);
        
        data.push({
          period: `${month} W${week}`,
          revenue: weeklyRevenue,
          projectedRevenue: weeklyProjected,
          lastYearRevenue: weeklyLastYear
        });
      }
    });
    
    return data;
  }, [revenueData]);

  // Generate daily data for closer timeframes
  const dailyRevenueData = useMemo(() => {
    const data: Array<{
      period: string;
      revenue: number;
      projectedRevenue: number;
      lastYearRevenue: number;
    }> = [];
    const lastMonth = revenueData[revenueData.length - 1];
    
    // Generate last 30 days of data
    for (let i = 30; i > 0; i--) {
      const day = new Date();
      day.setDate(day.getDate() - i);
      const dayName = day.toLocaleDateString('en-US', { day: '2-digit', month: 'short' });
      
      // Create daily patterns - weekends have lower revenue
      const weekday = day.getDay();
      const weekendFactor = (weekday === 0 || weekday === 6) ? 0.7 : 1.0;
      
      // Random variation plus weekend adjustment
      const dailyRevenue = Math.round((lastMonth.revenue / 30) * weekendFactor * (0.7 + Math.random() * 0.6));
      const dailyProjected = Math.round(dailyRevenue * 1.05);
      const dailyLastYear = Math.round(dailyRevenue * 0.9);
      
      data.push({
        period: dayName,
        revenue: dailyRevenue,
        projectedRevenue: dailyProjected,
        lastYearRevenue: dailyLastYear
      });
    }
    
    return data;
  }, [revenueData]);

  // Get data based on selected timeframe
  const getRevenueDataByTimeframe = () => {
    switch(timeframe) {
      case "daily": return dailyRevenueData;
      case "weekly": return weeklyRevenueData;
      default: return revenueData;
    }
  };

  // Calculate seasonal metrics
  const seasonalMetrics = useMemo(() => {
    const seasons = {
      winter: { revenue: 0, occupancy: 0, count: 0 },
      spring: { revenue: 0, occupancy: 0, count: 0 },
      summer: { revenue: 0, occupancy: 0, count: 0 },
      fall: { revenue: 0, occupancy: 0, count: 0 }
    };
    
    revenueData.forEach((data, idx) => {
      const month = new Date(data.period).getMonth();
      const occupancyRate = occupancyData[idx]?.occupancyRate || 0;
      
      // Assign months to seasons
      if (month >= 0 && month <= 1 || month === 11) {
        seasons.winter.revenue += data.revenue;
        seasons.winter.occupancy += occupancyRate;
        seasons.winter.count++;
      } else if (month >= 2 && month <= 4) {
        seasons.spring.revenue += data.revenue;
        seasons.spring.occupancy += occupancyRate;
        seasons.spring.count++;
      } else if (month >= 5 && month <= 7) {
        seasons.summer.revenue += data.revenue;
        seasons.summer.occupancy += occupancyRate;
        seasons.summer.count++;
      } else {
        seasons.fall.revenue += data.revenue;
        seasons.fall.occupancy += occupancyRate;
        seasons.fall.count++;
      }
    });
    
    // Calculate averages
    Object.keys(seasons).forEach(season => {
      const s = seasons[season as keyof typeof seasons];
      if (s.count > 0) {
        s.occupancy = Math.round(s.occupancy / s.count);
      }
    });
    
    return seasons;
  }, [revenueData, occupancyData]);
  
  // Format seasonal data for charts
  const seasonalChartData = useMemo(() => {
    return Object.entries(seasonalMetrics).map(([season, data]) => ({
      name: season.charAt(0).toUpperCase() + season.slice(1),
      revenue: data.revenue,
      occupancy: data.occupancy
    }));
  }, [seasonalMetrics]);
  
  // Generate top locations data for revenue dashboard
  const topLocations = useMemo(() => {
    return locationBreakdownData.map(loc => ({
      name: loc.area,
      revenue: loc.revenue,
      reservations: loc.adsCount,
      percentage: Math.round((loc.revenue / totalRevenue) * 100)
    })).sort((a, b) => b.revenue - a.revenue);
  }, [locationBreakdownData, totalRevenue]);
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="text-xl md:text-2xl font-bold flex items-center gap-2">
              <BarChart3 className="h-5 w-5 md:h-6 md:w-6" />
              {locationName ? `${locationName} - ${t("statistics")}` : t("locationStatistics")}
            </CardTitle>
            
            <div className="flex flex-wrap gap-3 items-center">
              <Select
                value={timeframe}
                onValueChange={setTimeframe}
              >
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder={t("timeframe")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">{t("daily")}</SelectItem>
                  <SelectItem value="weekly">{t("weekly")}</SelectItem>
                  <SelectItem value="monthly">{t("monthly")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <CardDescription className="mt-2">
            {t("statisticsForOutdoorAdvertising")}
          </CardDescription>
        </CardHeader>
        
        <Separator className="mb-4" />
        
        <CardContent className="p-4 sm:p-6 pt-0 space-y-8">
          {/* Overview Cards - Using the StatisticsOverview component */}
          {/* <StatisticsOverview
            totalRevenue={totalRevenue}
            revenueGrowth={revenueGrowth}
            averageOccupancy={averageOccupancy}
            totalClients={totalClients}
          /> */}
          
          {/* Tabs Navigation */}
          <Tabs 
            defaultValue="revenue" 
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <div className="relative">
              <TabsList className="w-full h-auto flex flex-wrap md:grid md:grid-cols-4 gap-1">
                <TabsTrigger value="revenue" className="flex items-center gap-2 text-xs sm:text-sm flex-1 md:flex-auto">
                  <span className="truncate">{t("revenue")}</span>
                </TabsTrigger>
                <TabsTrigger value="occupancy" className="flex items-center gap-2 text-xs sm:text-sm flex-1 md:flex-auto">
                  <span className="truncate">{t("occupancy")}</span>
                </TabsTrigger>
                <TabsTrigger value="clients" className="flex items-center gap-2 text-xs sm:text-sm flex-1 md:flex-auto">
                  <span className="truncate">{t("clients")}</span>
                </TabsTrigger>
              </TabsList>
            </div>
            
            {/* Tab Content using modular components */}
            <TabsContent value="revenue">
              <RevenueDashboard
                revenueData={revenueData}
                seasonalChartData={seasonalChartData}
                locationPieData={locationBreakdownData.map(loc => ({ name: loc.area, value: loc.revenue }))}
                topLocations={topLocations}
                totalRevenue={totalRevenue}
                revenueGrowth={revenueGrowth}
                renderActiveShape={renderActiveShape}
                COLORS={COLORS}
                getRevenueDataByTimeframe={getRevenueDataByTimeframe}
              />
            </TabsContent>
            
            <TabsContent value="occupancy">
              <OccupancyDashboard
                occupancyData={occupancyData}
                seasonalChartData={seasonalChartData}
                locationBreakdownData={locationBreakdownData}
                averageOccupancy={averageOccupancy}
              />
            </TabsContent>
            
            <TabsContent value="clients">
              <ClientsDashboard
                clientData={clientData}
                renderActiveShape={renderActiveShape}
                COLORS={COLORS}
                clientRevenuePieData={clientRevenuePieData}
                totalClients={totalClients}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default LocationStatistics;

import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer } from "recharts";
import { EventDetails } from "@/lib/interfaces/finaces";

interface EventStatusDistributionSectionProps {
    events: EventDetails[];
}

const EventStatusDistributionSection: React.FC<EventStatusDistributionSectionProps> = ({ events }) => {
    const data = [
        { name: "Pending", value: events.filter(event => event.status === "pending").length },
        { name: "Completed", value: events.filter(event => event.status === "completed").length },
        { name: "Cancelled", value: events.filter(event => event.status === "cancelled").length },
    ];

    const COLORS = ["#FFBB28", "#00C49F", "#FF8042"];

    return (
        <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <h2 className="text-xl font-semibold mb-6 dark:text-white">Event Status Distribution</h2>
            <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                    <Pie data={data} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={150} fill="#8884d8" label>
                        {data.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                    </Pie>
                    <Tooltip />
                </PieChart>
            </ResponsiveContainer>
        </div>
    );
};

export default EventStatusDistributionSection;
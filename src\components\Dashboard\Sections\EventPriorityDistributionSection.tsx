import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer } from "recharts";
import { EventDetails } from "@/lib/interfaces/finaces";

interface EventPriorityDistributionSectionProps {
    events: EventDetails[];
}

const EventPriorityDistributionSection: React.FC<EventPriorityDistributionSectionProps> = ({ events }) => {
    const data = [
        { name: "Low", value: events.filter(event => event.priority === "low").length },
        { name: "Medium", value: events.filter(event => event.priority === "medium").length },
        { name: "High", value: events.filter(event => event.priority === "high").length },
    ];

    const COLORS = ["#0088FE", "#00C49F", "#FF8042"];

    return (
        <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <h2 className="text-xl font-semibold mb-6 dark:text-white">Event Priority Distribution</h2>
            <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                    <Pie data={data} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={150} fill="#8884d8" label>
                        {data.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                    </Pie>
                    <Tooltip />
                </PieChart>
            </ResponsiveContainer>
        </div>
    );
};

export default EventPriorityDistributionSection;
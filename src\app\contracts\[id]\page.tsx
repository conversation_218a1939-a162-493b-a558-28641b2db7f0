"use client";

import React, { useState, useEffect } from "react";
import Layout from "@/components/Layouts/Layout";
import { Contract } from "@/lib/interfaces/contract";
// import ContractDetails from "@/components/Contracts/ContractDetails";
import ContractServices from "@/lib/contracts";

interface PageProps {
  params: {
    id: string;
  };
}

export default function ContractDetailsPage({ params }: PageProps) {
  const [contract, setContract] = useState<Contract | null>(null);
  const [loading, setLoading] = useState(true);
  const contractService =  ContractServices();

  useEffect(() => {
    const fetchContract = async () => {
      try {
        setLoading(true);
        const response = await contractService.getContractsByLocation(params.id);
        if (!response || response.length === 0) {
          setLoading(false);
          return;
        }

        const foundContract = response.find(
          (c: any) => c.id === parseInt(params.id)
        );
        
        if (foundContract) {
          // setContract(foundContract as Contract);
        }
        setLoading(false);
      } catch (error) {
        console.error("Error fetching contract:", error);
        setLoading(false);
      }
    };

    fetchContract();
  }, [params.id]);

  // Handle updating the contract
  const handleUpdateContract = (updatedContract: Contract) => {
    // In a real app, this would make an API call
    setContract(updatedContract);
    alert("Contract updated successfully!");
  };

  // Handle deleting the contract (redirect to contracts page)
  const handleDeleteContract = () => {
    // In a real app, this would make an API call
    alert("Contract deleted successfully!");
    window.location.href = "/contracts";
  };

  return (
    <Layout>
      <div className="container mx-auto py-6">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white"></div>
          </div>
        ) : contract ? (
          // <ContractDetails
          //   contract={contract}
          //   onClose={() => window.location.href = "/contracts"}
          //   onUpdate={handleUpdateContract}
          //   onDelete={handleDeleteContract}
          // />
          <>
          </>
        ) : (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Contract not found. <a href="/contracts" className="underline">Return to contracts list</a>
          </div>
        )}
      </div>
    </Layout>
  );
}

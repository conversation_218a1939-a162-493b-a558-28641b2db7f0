// components/contract/InstallmentsComponent.tsx
import React, { useEffect } from "react";
import { ContractFormData } from "@/lib/interfaces/contract";
import useLanguage from "@/hooks/useLanguage";
import { useContacts } from "@/hooks/useContact";
import { useLocations } from "@/hooks/useLocations";
import { useExpenseTypes } from "@/hooks/useExpensesTypes";

import { FaPlus, FaTrash, FaInfoCircle, FaCheck } from "react-icons/fa";

interface InstallmentsComponentProps {
  formData: ContractFormData;
  setFormData: React.Dispatch<React.SetStateAction<ContractFormData>>;
  isEditingContract?: boolean;
}

interface ExtendedInstallment {
  id?: string;
  amount: number;
  due_date: string;
  notification_view_date: string;
  status?: string;
  priority?: string;
  paid_date?: string;
  type?: "income" | "expense";
  title?: string;
  description?: string;
  contact_id?: string;
  location_id?: string;
  type_id?: string;
  expense_id?: string;
  is_deleted?: boolean;
}

const InstallmentsComponent: React.FC<InstallmentsComponentProps> = ({
  formData,
  setFormData,
  isEditingContract = false,
}) => {
  const { t } = useLanguage();
  const { data: contacts } = useContacts();
  const { data: locations } = useLocations();
  const { data: expenseTypes } = useExpenseTypes();

  // Only populate from contract events when editing contracts
  const isContractEditMode = isEditingContract && formData.contract?.id;

  // Populate installments from existing contract data when editing contracts only
  useEffect(() => {
    if (isContractEditMode && formData.installments.length === 0) {
      const existingInstallments: ExtendedInstallment[] = [];
      const contractData = formData.contract as any;

      // Add expense events as installments - only when editing contracts
      if (
        contractData.expense_events &&
        contractData.expense_events.length > 0
      ) {
        contractData.expense_events.forEach((expense: any) => {
          existingInstallments.push({
            id: expense.id,
            amount: expense.amount || 0,
            due_date: expense.due_date ? expense.due_date.split("T")[0] : "",
            notification_view_date:
              expense.notification_view_date &&
              expense.notification_view_date !== "Invalid date"
                ? expense.notification_view_date.split("T")[0]
                : expense.due_date
                  ? expense.due_date.split("T")[0]
                  : "",
            status: expense.status || "pending",
            paid_date: expense.paid_date ? expense.paid_date.split("T")[0] : "",
            type: "expense",
            title: expense.title || "",
            description: expense.description || "",
            priority: expense.priority || "medium",
            contact_id: expense.contact_id || formData.contact_id,
            location_id: expense.location_id || formData.location_id,
            type_id: expense.type_id || formData.type_id,
            is_deleted: false,
          });
        });
      }

      // Add income events as installments - only when editing contracts
      if (contractData.income_events && contractData.income_events.length > 0) {
        contractData.income_events.forEach((income: any) => {
          existingInstallments.push({
            id: income.id,
            amount: income.amount || 0,
            due_date: income.due_date ? income.due_date.split("T")[0] : "",
            notification_view_date:
              income.notification_view_date &&
              income.notification_view_date !== "Invalid date"
                ? income.notification_view_date.split("T")[0]
                : income.due_date
                  ? income.due_date.split("T")[0]
                  : "",
            status: income.status || "pending",
            paid_date: income.paid_date ? income.paid_date.split("T")[0] : "",
            type: "income",
            title: income.title || "",
            description: income.description || "",
            priority: income.priority || "medium",
            contact_id: income.contact_id || formData.contact_id,
            location_id: income.location_id || formData.location_id,
            expense_id: income.expense_id,
            is_deleted: false,
          });
        });
      }

      // Sort installments by due date and group incomes under their expenses
      const sortedInstallments = existingInstallments.sort((a, b) => {
        if (a.expense_id === b.id) return 1;
        if (b.expense_id === a.id) return -1;

        const dateA = new Date(a.due_date).getTime();
        const dateB = new Date(b.due_date).getTime();
        return dateA - dateB;
      });

      if (sortedInstallments.length > 0) {
        setFormData((prev) => ({
          ...prev,
          installments: sortedInstallments as any,
          installmentCount: sortedInstallments.length,
        }));
      }
    }
  }, [isContractEditMode]);

  // Simple installment handlers for creating contracts/reservations
  const handleSimpleInstallmentChange = (
    index: number,
    field: string,
    value: any,
  ) => {
    setFormData((prev) => {
      const updatedInstallments = [...prev.installments];
      updatedInstallments[index] = {
        ...updatedInstallments[index],
        [field]: field === "amount" ? Number(value) : value,
      };
      return {
        ...prev,
        installments: updatedInstallments,
      };
    });
  };

  const handleAddSimpleInstallment = () => {
    const lastInstallment =
      formData.installments[formData.installments.length - 1];
    const newDueDate = lastInstallment
      ? (() => {
          const date = new Date(lastInstallment.due_date);
          date.setMonth(date.getMonth() + 1);
          return date.toISOString().split("T")[0];
        })()
      : formData.contract.start_date;

    setFormData((prev) => ({
      ...prev,
      installments: [
        ...prev.installments,
        {
          amount: 0,
          due_date: newDueDate,
          notification_view_date: newDueDate,
          status: "pending",
          paid_date: "",
        },
      ],
    }));
  };

  const handleRemoveSimpleInstallment = (index: number) => {
    const updatedInstallments = formData.installments.filter(
      (_, i) => i !== index,
    );
    setFormData((prev) => ({
      ...prev,
      installments: updatedInstallments,
    }));
  };

  const generateEqualInstallments = () => {
    if (
      formData.contract.total_amount <= 0 ||
      formData.installmentCount <= 0 ||
      !formData.contract.start_date
    ) {
      return;
    }

    const exactAmount =
      formData.contract.total_amount / formData.installmentCount;
    const formattedAmount = parseFloat(exactAmount.toFixed(2));
    const totalAfterFormatting = formattedAmount * formData.installmentCount;
    const roundingDifference = parseFloat(
      (formData.contract.total_amount - totalAfterFormatting).toFixed(2),
    );

    const startDate = new Date(formData.contract.start_date);
    const newInstallments = Array(formData.installmentCount)
      .fill(null)
      .map((_, index) => {
        const dueDate = new Date(startDate);
        dueDate.setMonth(dueDate.getMonth() + index);
        const installmentAmount =
          index === formData.installmentCount - 1
            ? formattedAmount + roundingDifference
            : formattedAmount;
        return {
          amount: parseFloat(installmentAmount.toFixed(2)),
          due_date: dueDate.toISOString().split("T")[0],
          notification_view_date: dueDate.toISOString().split("T")[0],
          status: "pending" as const,
          paid_date: "",
        };
      });

    setFormData((prev) => ({
      ...prev,
      installments: newInstallments,
    }));
  };

  // Filter installments based on mode
  const visibleInstallments = isContractEditMode
    ? formData.installments.filter((item: any) => !item.is_deleted)
    : formData.installments;

  // Enhanced view for contract editing ONLY
  if (isContractEditMode) {
    // Handlers specific to contract editing mode
    const handleInstallmentChange = (
      index: number,
      field: string,
      value: any,
    ) => {
      setFormData((prev) => {
        const updatedInstallments = [...prev.installments];
        updatedInstallments[index] = {
          ...updatedInstallments[index],
          [field]: field === "amount" ? Number(value) : value,
        };
        return {
          ...prev,
          installments: updatedInstallments,
        };
      });
    };

    const handleAddExpense = () => {
      const newDueDate =
        formData.contract.start_date || new Date().toISOString().split("T")[0];

      setFormData((prev) => ({
        ...prev,
        installments: [
          ...prev.installments,
          {
            amount: 0,
            due_date: newDueDate,
            notification_view_date: newDueDate,
            status: "pending",
            paid_date: "",
            type: "expense",
            title: "",
            description: "",
            priority: "medium",
            contact_id: prev.contact_id,
            location_id: prev.location_id,
            type_id: prev.type_id,
            is_deleted: false,
          } as any,
        ],
      }));
    };

    const handleAddIncome = (expenseIndex?: number) => {
      const newDueDate =
        formData.contract.start_date || new Date().toISOString().split("T")[0];
      const expenseId =
        expenseIndex !== undefined
          ? (formData.installments[expenseIndex] as any).id
          : undefined;

      setFormData((prev) => ({
        ...prev,
        installments: [
          ...prev.installments,
          {
            amount: 0,
            due_date: newDueDate,
            notification_view_date: newDueDate,
            status: "pending",
            paid_date: "",
            type: "income",
            title: "",
            description: "",
            priority: "medium",
            contact_id: prev.contact_id,
            location_id: prev.location_id,
            expense_id: expenseId,
            is_deleted: false,
          } as any,
        ],
      }));
    };

    const handleRemoveInstallment = (index: number) => {
      setFormData((prev) => {
        const updatedInstallments = [...prev.installments];
        const installment = updatedInstallments[index] as any;

        if (installment.id) {
          // Mark existing item as deleted
          updatedInstallments[index] = {
            ...installment,
            is_deleted: true,
          };
        } else {
          // Remove new item completely
          updatedInstallments.splice(index, 1);
        }

        return {
          ...prev,
          installments: updatedInstallments,
        };
      });
    };

    return (
      <div className="mt-6 sm:mt-8">
        <div className="mb-4 flex flex-wrap items-center justify-between">
          <h3 className="mb-2 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-0">
            {t("Contract Events")}
          </h3>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={handleAddExpense}
              className="flex items-center gap-2 rounded-lg bg-red-500 px-4 py-2 text-white hover:bg-red-600"
            >
              <FaPlus /> {t("Add Expense")}
            </button>
            <button
              type="button"
              onClick={() => handleAddIncome()}
              className="flex items-center gap-2 rounded-lg bg-green-500 px-4 py-2 text-white hover:bg-green-600"
            >
              <FaPlus /> {t("Add Income")}
            </button>
          </div>
        </div>

        {/* Legend */}
        <div className="mb-4 rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
          <h4 className="mb-2 text-sm font-semibold text-gray-800 dark:text-gray-200">
            {t("Legend")}:
          </h4>
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 rounded bg-red-100"></div>
              <span>{t("Expense")}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 rounded bg-green-100"></div>
              <span>{t("Income")}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-blue-600">└─</span>
              <span>{t("Income linked to expense")}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-orange-600">⚪</span>
              <span>{t("Independent income")}</span>
            </div>
          </div>
        </div>

        {visibleInstallments.length > 0 ? (
          <div className="space-y-4">
            {/* Group installments by expense relationships */}
            {(() => {
              const expenses = visibleInstallments.filter(
                (item: any) => item.type === "expense",
              );
              const independentIncomes = visibleInstallments.filter(
                (item: any) => item.type === "income" && !item.expense_id,
              );

              return (
                <>
                  {/* Render expenses with their related incomes */}
                  {expenses.map((expense: any, expenseIndex) => {
                    const relatedIncomes = visibleInstallments.filter(
                      (item: any) =>
                        item.type === "income" &&
                        item.expense_id === expense.id,
                    );
                    const actualExpenseIndex = visibleInstallments.findIndex(
                      (item: any) => item.id === expense.id || item === expense,
                    );

                    return (
                      <div
                        key={expense.id || `expense-${expenseIndex}`}
                        className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
                      >
                        {/* Expense Header */}
                        <div className="bg-red-50 p-3 dark:bg-red-900/20">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span className="rounded-full bg-red-100 px-2 py-1 text-xs font-semibold text-red-800 dark:bg-red-800 dark:text-red-100">
                                {t("Expense")}
                              </span>
                              <span className="font-medium text-gray-900 dark:text-white">
                                {expense.title || t("Untitled Expense")}
                              </span>
                            </div>
                            <div className="flex gap-2">
                              <button
                                type="button"
                                onClick={() =>
                                  handleAddIncome(actualExpenseIndex)
                                }
                                className="rounded bg-green-500 px-2 py-1 text-xs text-white hover:bg-green-600"
                              >
                                + {t("Add Related Income")}
                              </button>
                              <button
                                type="button"
                                onClick={() =>
                                  handleRemoveInstallment(actualExpenseIndex)
                                }
                                className="text-red-600 hover:text-red-900"
                              >
                                <FaTrash size={16} />
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* Expense Details */}
                        <div className="grid grid-cols-1 gap-4 p-4 md:grid-cols-2 lg:grid-cols-4">
                          <div>
                            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                              {t("Title")}
                            </label>
                            <input
                              type="text"
                              value={expense.title || ""}
                              onChange={(e) =>
                                handleInstallmentChange(
                                  actualExpenseIndex,
                                  "title",
                                  e.target.value,
                                )
                              }
                              className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                              placeholder={t("Enter title")}
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                              {t("Amount")}
                            </label>
                            <input
                              type="number"
                              value={expense.amount}
                              onChange={(e) =>
                                handleInstallmentChange(
                                  actualExpenseIndex,
                                  "amount",
                                  +e.target.value,
                                )
                              }
                              className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                              {t("Due Date")}
                            </label>
                            <input
                              type="date"
                              value={expense.due_date}
                              onChange={(e) =>
                                handleInstallmentChange(
                                  actualExpenseIndex,
                                  "due_date",
                                  e.target.value,
                                )
                              }
                              className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                              {t("Status")}
                            </label>
                            <select
                              value={expense.status || ""}
                              onChange={(e) =>
                                handleInstallmentChange(
                                  actualExpenseIndex,
                                  "status",
                                  e.target.value,
                                )
                              }
                              className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                            >
                              <option value="">{t("Select Status")}</option>
                              <option value="upcoming">{t("Upcoming")}</option>
                              <option value="pending">{t("Pending")}</option>
                              <option value="completed">
                                {t("Completed")}
                              </option>
                              <option value="overdue">{t("Overdue")}</option>
                              <option value="cancelled">
                                {t("Cancelled")}
                              </option>
                            </select>
                          </div>
                          <div className="md:col-span-2">
                            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                              {t("Description")}
                            </label>
                            <textarea
                              value={expense.description || ""}
                              onChange={(e) =>
                                handleInstallmentChange(
                                  actualExpenseIndex,
                                  "description",
                                  e.target.value,
                                )
                              }
                              className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                              placeholder={t("Enter description")}
                              rows={2}
                            />
                          </div>
                        </div>

                        {/* Related Incomes */}
                        {relatedIncomes.length > 0 && (
                          <div className="border-t border-gray-200 bg-green-50 dark:border-gray-700 dark:bg-green-900/20">
                            <div className="p-3">
                              <h5 className="mb-2 flex items-center gap-2 text-sm font-medium text-gray-800 dark:text-gray-200">
                                <span className="text-blue-600">└─</span>
                                {t("Related Incomes")} ({relatedIncomes.length})
                              </h5>
                              <div className="space-y-3">
                                {relatedIncomes.map((income: any) => {
                                  const actualIncomeIndex =
                                    visibleInstallments.findIndex(
                                      (item: any) =>
                                        item.id === income.id ||
                                        item === income,
                                    );
                                  return (
                                    <div
                                      key={
                                        income.id ||
                                        `income-${actualIncomeIndex}`
                                      }
                                      className="rounded border border-green-200 bg-white p-3 dark:border-green-700 dark:bg-gray-800"
                                    >
                                      <div className="mb-2 flex items-center justify-between">
                                        <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-semibold text-green-800 dark:bg-green-800 dark:text-green-100">
                                          {t("Income")}
                                        </span>
                                        <button
                                          type="button"
                                          onClick={() =>
                                            handleRemoveInstallment(
                                              actualIncomeIndex,
                                            )
                                          }
                                          className="text-red-600 hover:text-red-900"
                                        >
                                          <FaTrash size={14} />
                                        </button>
                                      </div>
                                      <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
                                        <div>
                                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                            {t("Title")}
                                          </label>
                                          <input
                                            type="text"
                                            value={income.title || ""}
                                            onChange={(e) =>
                                              handleInstallmentChange(
                                                actualIncomeIndex,
                                                "title",
                                                e.target.value,
                                              )
                                            }
                                            className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                            placeholder={t("Enter title")}
                                          />
                                        </div>
                                        <div>
                                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                            {t("Amount")}
                                          </label>
                                          <input
                                            type="number"
                                            value={income.amount}
                                            onChange={(e) =>
                                              handleInstallmentChange(
                                                actualIncomeIndex,
                                                "amount",
                                                +e.target.value,
                                              )
                                            }
                                            className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                          />
                                        </div>
                                        <div>
                                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                            {t("Due Date")}
                                          </label>
                                          <input
                                            type="date"
                                            value={income.due_date}
                                            onChange={(e) =>
                                              handleInstallmentChange(
                                                actualIncomeIndex,
                                                "due_date",
                                                e.target.value,
                                              )
                                            }
                                            className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                          />
                                        </div>
                                        <div>
                                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                            {t("Status")}
                                          </label>
                                          <select
                                            value={income.status || ""}
                                            onChange={(e) =>
                                              handleInstallmentChange(
                                                actualIncomeIndex,
                                                "status",
                                                e.target.value,
                                              )
                                            }
                                            className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                          >
                                            <option value="">
                                              {t("Select Status")}
                                            </option>
                                            <option value="upcoming">
                                              {t("Upcoming")}
                                            </option>
                                            <option value="pending">
                                              {t("Pending")}
                                            </option>
                                            <option value="completed">
                                              {t("Completed")}
                                            </option>
                                            <option value="overdue">
                                              {t("Overdue")}
                                            </option>
                                            <option value="cancelled">
                                              {t("Cancelled")}
                                            </option>
                                          </select>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}

                  {/* Independent Incomes Section */}
                  {independentIncomes.length > 0 && (
                    <div className="rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-700 dark:bg-orange-900/20">
                      <h4 className="mb-3 flex items-center gap-2 text-lg font-semibold text-gray-800 dark:text-gray-200">
                        <span className="text-orange-600">⚪</span>
                        {t("Independent Incomes")} ({independentIncomes.length})
                      </h4>
                      <div className="space-y-3">
                        {independentIncomes.map((income: any) => {
                          const actualIncomeIndex =
                            visibleInstallments.findIndex(
                              (item: any) =>
                                item.id === income.id || item === income,
                            );
                          return (
                            <div
                              key={
                                income.id ||
                                `independent-income-${actualIncomeIndex}`
                              }
                              className="rounded border border-green-200 bg-white p-3 dark:border-green-700 dark:bg-gray-800"
                            >
                              <div className="mb-2 flex items-center justify-between">
                                <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-semibold text-green-800 dark:bg-green-800 dark:text-green-100">
                                  {t("Independent Income")}
                                </span>
                                <button
                                  type="button"
                                  onClick={() =>
                                    handleRemoveInstallment(actualIncomeIndex)
                                  }
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <FaTrash size={14} />
                                </button>
                              </div>
                              <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                    {t("Title")}
                                  </label>
                                  <input
                                    type="text"
                                    value={income.title || ""}
                                    onChange={(e) =>
                                      handleInstallmentChange(
                                        actualIncomeIndex,
                                        "title",
                                        e.target.value,
                                      )
                                    }
                                    className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                    placeholder={t("Enter title")}
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                    {t("Amount")}
                                  </label>
                                  <input
                                    type="number"
                                    value={income.amount}
                                    onChange={(e) =>
                                      handleInstallmentChange(
                                        actualIncomeIndex,
                                        "amount",
                                        +e.target.value,
                                      )
                                    }
                                    className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                    {t("Due Date")}
                                  </label>
                                  <input
                                    type="date"
                                    value={income.due_date}
                                    onChange={(e) =>
                                      handleInstallmentChange(
                                        actualIncomeIndex,
                                        "due_date",
                                        e.target.value,
                                      )
                                    }
                                    className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                    {t("Status")}
                                  </label>
                                  <select
                                    value={income.status || ""}
                                    onChange={(e) =>
                                      handleInstallmentChange(
                                        actualIncomeIndex,
                                        "status",
                                        e.target.value,
                                      )
                                    }
                                    className="mt-1 w-full rounded border px-2 py-1 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                                  >
                                    <option value="">
                                      {t("Select Status")}
                                    </option>
                                    <option value="upcoming">
                                      {t("Upcoming")}
                                    </option>
                                    <option value="pending">
                                      {t("Pending")}
                                    </option>
                                    <option value="completed">
                                      {t("Completed")}
                                    </option>
                                    <option value="overdue">
                                      {t("Overdue")}
                                    </option>
                                    <option value="cancelled">
                                      {t("Cancelled")}
                                    </option>
                                  </select>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {/* Show message if no events */}
                  {expenses.length === 0 && independentIncomes.length === 0 && (
                    <div className="rounded-lg border-2 border-dashed border-gray-300 p-8 text-center dark:border-gray-600">
                      <FaInfoCircle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                      <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
                        {t("No events yet")}
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400">
                        {t(
                          "Add expenses and incomes to manage contract events",
                        )}
                      </p>
                    </div>
                  )}
                </>
              );
            })()}
          </div>
        ) : (
          <div className="rounded-lg border-2 border-dashed border-gray-300 p-8 text-center dark:border-gray-600">
            <FaInfoCircle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
            <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
              {t("No events yet")}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {t("Add expenses and incomes to manage contract events")}
            </p>
          </div>
        )}
      </div>
    );
  }

  // Simple view for creating contracts/reservations
  return (
    <div className="mt-6 sm:mt-8">
      <div className="mb-4 flex flex-wrap items-center justify-between">
        <h3 className="mb-2 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-0">
          {t("Payment Installments")}
        </h3>
        <button
          type="button"
          onClick={handleAddSimpleInstallment}
          className="mt-2 flex w-full items-center justify-center gap-2 rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600 sm:mt-0 sm:w-auto sm:py-3"
        >
          <FaPlus /> {t("Add Installment")}
        </button>
      </div>

      <div className="mb-4">
        <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("Number of Installments")}
        </label>
        <div className="flex flex-col items-start gap-3 sm:flex-row sm:items-center sm:gap-4">
          <input
            type="number"
            name="installmentCount"
            value={formData.installmentCount}
            onChange={(e) => {
              const value = Math.max(1, Number(e.target.value));
              setFormData((prev) => ({
                ...prev,
                installmentCount: value,
              }));
            }}
            className="w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:w-32 sm:py-3"
            min="1"
          />

          <button
            type="button"
            onClick={() => {
              if (
                formData.contract.total_amount <= 0 ||
                formData.installmentCount <= 0
              ) {
                alert(
                  t(
                    "Total amount and installment count must be greater than zero",
                  ),
                );
                return;
              }
              generateEqualInstallments();
            }}
            className="flex w-full items-center justify-center gap-2 rounded-lg bg-green-500 px-4 py-2 text-white hover:bg-green-600 sm:w-auto sm:py-3"
          >
            <FaCheck className="hidden sm:block" />
            {t("Generate Equal Installments")}
          </button>
        </div>
      </div>

      {formData.installments.length > 0 && (
        <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="hidden sm:block">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    {t("Amount")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    {t("Due Date")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    {t("Notification View Date")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    {t("Status")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    {t("Paid Date")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    {t("Action")}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                {formData.installments.map((installment, index) => (
                  <tr key={index}>
                    <td className="whitespace-nowrap px-6 py-4">
                      <input
                        type="number"
                        value={installment.amount}
                        onChange={(e) =>
                          handleSimpleInstallmentChange(
                            index,
                            "amount",
                            +e.target.value,
                          )
                        }
                        className="w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                      />
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <input
                        type="date"
                        value={installment.due_date}
                        onChange={(e) =>
                          handleSimpleInstallmentChange(
                            index,
                            "due_date",
                            e.target.value,
                          )
                        }
                        className="w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                      />
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <input
                        type="date"
                        value={installment.notification_view_date}
                        onChange={(e) =>
                          handleSimpleInstallmentChange(
                            index,
                            "notification_view_date",
                            e.target.value,
                          )
                        }
                        className="w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                      />
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <select
                        value={installment.status || ""}
                        onChange={(e) => {
                          handleSimpleInstallmentChange(
                            index,
                            "status",
                            e.target.value,
                          );
                          // Clear paid date if status is not completed
                          if (e.target.value !== "completed") {
                            handleSimpleInstallmentChange(
                              index,
                              "paid_date",
                              "",
                            );
                          }
                        }}
                        className="w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                      >
                        <option value="">{t("Select Status")}</option>
                        <option value="upcoming">{t("Upcoming")}</option>
                        <option value="pending">{t("Pending")}</option>
                        <option value="completed">{t("Completed")}</option>
                        <option value="overdue">{t("Overdue")}</option>
                        <option value="cancelled">{t("Cancelled")}</option>
                      </select>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <input
                        type="date"
                        value={installment.paid_date || ""}
                        onChange={(e) =>
                          handleSimpleInstallmentChange(
                            index,
                            "paid_date",
                            e.target.value,
                          )
                        }
                        disabled={installment.status !== "completed"}
                        className={`w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 ${
                          installment.status !== "completed"
                            ? "cursor-not-allowed bg-gray-100 dark:bg-gray-600"
                            : ""
                        }`}
                        required={installment.status === "completed"}
                      />
                      {installment.status === "completed" &&
                        !installment.paid_date && (
                          <p className="mt-1 text-xs text-red-500">
                            {t("Required for completed installments")}
                          </p>
                        )}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <button
                        type="button"
                        onClick={() => handleRemoveSimpleInstallment(index)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        aria-label={t("Remove installment")}
                      >
                        <FaTrash size={18} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Card View for Installments */}
          <div className="sm:hidden">
            {formData.installments.map((installment, index) => (
              <div
                key={index}
                className="border-b border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
              >
                <div className="mb-3 flex items-center justify-between">
                  <span className="font-semibold text-gray-700 dark:text-gray-300">
                    {t("Installment")} #{index + 1}
                  </span>
                  <button
                    type="button"
                    onClick={() => handleRemoveSimpleInstallment(index)}
                    className="p-2 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    aria-label={t("Remove installment")}
                  >
                    <FaTrash size={16} />
                  </button>
                </div>
                <div className="space-y-3">
                  <div>
                    <label className="mb-1 block text-xs font-medium text-gray-500 dark:text-gray-400">
                      {t("Amount")}
                    </label>
                    <input
                      type="number"
                      value={installment.amount}
                      onChange={(e) =>
                        handleSimpleInstallmentChange(
                          index,
                          "amount",
                          +e.target.value,
                        )
                      }
                      className="w-full rounded-lg border px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                    />
                  </div>
                  <div>
                    <label className="mb-1 block text-xs font-medium text-gray-500 dark:text-gray-400">
                      {t("Due Date")}
                    </label>
                    <input
                      type="date"
                      value={installment.due_date}
                      onChange={(e) =>
                        handleSimpleInstallmentChange(
                          index,
                          "due_date",
                          e.target.value,
                        )
                      }
                      className="w-full rounded-lg border px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                    />
                  </div>
                  <div>
                    <label className="mb-1 block text-xs font-medium text-gray-500 dark:text-gray-400">
                      {t("Notification View Date")}
                    </label>
                    <input
                      type="date"
                      value={installment.notification_view_date}
                      onChange={(e) =>
                        handleSimpleInstallmentChange(
                          index,
                          "notification_view_date",
                          e.target.value,
                        )
                      }
                      className="w-full rounded-lg border px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                    />
                  </div>
                  <div>
                    <label className="mb-1 block text-xs font-medium text-gray-500 dark:text-gray-400">
                      {t("Status")}
                    </label>
                    <select
                      value={installment.status || ""}
                      onChange={(e) => {
                        handleSimpleInstallmentChange(
                          index,
                          "status",
                          e.target.value,
                        );
                        // Clear paid date if status is not completed
                        if (e.target.value !== "completed") {
                          handleSimpleInstallmentChange(index, "paid_date", "");
                        }
                      }}
                      className="w-full rounded-lg border px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                    >
                      <option value="">{t("Select Status")}</option>
                      <option value="upcoming">{t("Upcoming")}</option>
                      <option value="pending">{t("Pending")}</option>
                      <option value="completed">{t("Completed")}</option>
                      <option value="overdue">{t("Overdue")}</option>
                      <option value="cancelled">{t("Cancelled")}</option>
                    </select>
                  </div>
                  {installment.status === "completed" && (
                    <div>
                      <label className="mb-1 block text-xs font-medium text-gray-500 dark:text-gray-400">
                        {t("Paid Date")} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="date"
                        value={installment.paid_date || ""}
                        onChange={(e) =>
                          handleSimpleInstallmentChange(
                            index,
                            "paid_date",
                            e.target.value,
                          )
                        }
                        className="w-full rounded-lg border px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                        required
                      />
                      {!installment.paid_date && (
                        <p className="mt-1 text-xs text-red-500">
                          {t("Required for completed installments")}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default InstallmentsComponent;

import React, { useState } from "react";
import { Contract } from "@/lib/interfaces/contract";
import { FaEdit, FaTrashAlt, FaTimes, FaDownload } from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import { safeValue, getStatusBadgeClass } from "./utils";
import DeleteConfirmationModal from "../../Modals/DeleteConfirmationModal";
import ContractServices from "@/lib/contracts";

interface ContractHeaderProps {
  contract: Contract;
  onEdit: () => void;
  onDelete: () => void;
  onClose: () => void;
  onDownload: () => void;
  totalPaid: number;
  totalRemaining: number;
  isFinancialDataReady: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  canExport?: boolean;
}

const ContractHeader: React.FC<ContractHeaderProps> = ({
  contract,
  onEdit,
  onDelete,
  onClose,
  onDownload,
  totalPaid,
  totalRemaining,
  isFinancialDataReady,
  canEdit = true,
  canDelete = true,
  canExport = true,
}) => {
  const { t, language } = useLanguage();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const contractService = ContractServices();

  const formatCurrency = (amount: number) => {
    // Handle NaN, null, undefined, or invalid numbers
    if (isNaN(amount) || amount === null || amount === undefined) {
      amount = 0;
    }

    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleDelete = async () => {
    if (contract && contract.id) {
      setIsDeleting(true);
      setDeleteError(null);

      try {
        await contractService.softDeleteContract(contract.id.toString());
        // Toast is now handled in the service, so we don't need to show it here
        onDelete(); // Update UI after successful deletion
        setIsDeleteModalOpen(false); // Close modal
      } catch (error: any) {
        console.error("Error deleting contract:", error);
        // Handle error response for the modal display
        if (error.response && error.response.data) {
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting contract";
          setDeleteError(errorMessage);
        } else {
          setDeleteError("Failed to delete contract. Please try again.");
        }
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <>
      <div className="mb-4 flex flex-col items-start justify-between gap-3 sm:mb-6 sm:flex-row sm:items-center">
        <div>
          <h2 className="mb-1 text-xl font-bold text-gray-900 dark:text-white sm:text-2xl">
            {safeValue(contract.title)}
          </h2>
          <div className="flex flex-wrap items-center gap-2">
            <span
              className={`rounded-full px-2 py-1 text-xs font-semibold ${getStatusBadgeClass(contract.status)}`}
            >
              {t(
                safeValue(contract.status).charAt(0).toUpperCase() +
                  safeValue(contract.status).slice(1),
              )}
            </span>
          </div>
        </div>
        <div className="mt-2 flex flex-wrap gap-2 sm:mt-0">

          {canExport && (
            <button
              onClick={onDownload}
              className="rounded-lg bg-gray-100 p-2 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              title={t("Download as PDF")}
              aria-label={t("Download as PDF")}
            >
              <FaDownload size={18} />
            </button>
          )}
          {canEdit && (
            <button
              onClick={onEdit}
              className="rounded-lg bg-blue-100 p-2 text-blue-700 hover:bg-blue-200 dark:bg-blue-700 dark:text-blue-200 dark:hover:bg-blue-600"
              title={t("Edit Contract")}
              aria-label={t("Edit Contract")}
            >
              <FaEdit size={18} />
            </button>
          )}
          {canDelete && (
            <button
              onClick={() => setIsDeleteModalOpen(true)}
              className="rounded-lg bg-red-100 p-2 text-red-700 hover:bg-red-200 dark:bg-red-700 dark:text-red-200 dark:hover:bg-red-600"
              title={t("Delete Contract")}
              aria-label={t("Delete Contract")}
            >
              <FaTrashAlt size={18} />
            </button>
          )}
          <button
            onClick={onClose}
            className="rounded-lg bg-gray-100 p-2 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            title={t("Close")}
            aria-label={t("Close")}
          >
            <FaTimes size={18} />
          </button>
        </div>
      </div>

      {/* Financial Summary Cards */}
      <div className="mb-6 grid grid-cols-1 gap-3 sm:grid-cols-3 sm:gap-4">
        <div className="rounded-lg bg-green-50 p-3 dark:bg-green-900 sm:p-4">
          <h3 className="mb-1 text-sm font-medium text-green-800 dark:text-green-200">
            {t("Total Amount")}
          </h3>
          <p className="text-lg font-bold text-green-900 dark:text-green-100 sm:text-2xl">
            {formatCurrency(contract.total_amount || 0)}
          </p>
        </div>
        <div className="rounded-lg bg-blue-50 p-3 dark:bg-blue-900 sm:p-4">
          <h3 className="mb-1 text-sm font-medium text-blue-800 dark:text-blue-200">
            {t("Amount Paid")}
          </h3>
          {!isFinancialDataReady ? (
            <div className="flex h-8 items-center justify-center">
              <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <p className="text-lg font-bold text-blue-900 dark:text-blue-100 sm:text-2xl">
              {formatCurrency(totalPaid || 0)}
            </p>
          )}
        </div>
        <div className="rounded-lg bg-yellow-50 p-3 dark:bg-yellow-900 sm:p-4">
          <h3 className="mb-1 text-sm font-medium text-yellow-800 dark:text-yellow-200">
            {t("Remaining")}
          </h3>
          {!isFinancialDataReady ? (
            <div className="flex h-8 items-center justify-center">
              <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-yellow-500"></div>
            </div>
          ) : (
            <p className="text-lg font-bold text-yellow-900 dark:text-yellow-100 sm:text-2xl">
              {formatCurrency(totalRemaining || 0)}
            </p>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeleteError(null);
        }}
        onConfirm={handleDelete}
        message={`${t("deleteContractWarning")} ${t("deleteContractConditions")}`}
        title={t("deleteContract")}
        itemName={contract?.title || ""}
        isLoading={isDeleting}
        error={deleteError}
      />
    </>
  );
};

export default ContractHeader;

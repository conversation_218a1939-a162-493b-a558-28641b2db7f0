import React from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";

interface CalendarDayProps {
  day: number;
  year: number;
  viewType: string;
  events: EventDetails[];
  onDayClick: (day: number) => void;
}

const CalendarDay: React.FC<CalendarDayProps> = ({
  day,
  year,
  viewType,
  events,
  onDayClick,
}) => {
  // Group events by category for visual indicators
  const incomeEvents = events.filter((event) => event.category === "income");
  const expenseEvents = events.filter((event) => event.category === "expense");
  const { t } = useLanguage();
  const hasEvents = events.length > 0;
  const today = new Date();
  const isToday =
    today.getDate() === day &&
    today.getMonth() === new Date(year, 0).getMonth() &&
    today.getFullYear() === year;

  return (
    <td
      className={`relative h-16 cursor-pointer border border-stroke p-1 transition duration-300 ease-in-out hover:bg-gray-100 dark:border-strokedark dark:hover:bg-gray-700 sm:h-20 sm:p-2 md:h-24 md:p-3 lg:h-28 lg:p-4 xl:h-32 xl:p-5 ${
        isToday
          ? "bg-blue-50 ring-2 ring-inset ring-blue-500 dark:bg-blue-900/30"
          : ""
      } ${hasEvents ? "bg-gray-50 dark:bg-gray-800/50" : ""}`}
      onClick={() => onDayClick(day)}
    >
      <div className="flex h-full flex-col">
        {/* Day Number */}
        <span
          className={`text-xs font-semibold sm:text-sm md:text-base lg:text-lg ${
            isToday
              ? "text-blue-600 dark:text-blue-400"
              : "text-gray-900 dark:text-white"
          }`}
        >
          {day}
        </span>

        {/* Events Indicators */}
        {viewType === "events" && hasEvents && (
          <div className="mt-1 flex-1 space-y-0.5 overflow-hidden sm:mt-2 sm:space-y-1">
            {incomeEvents.length > 0 && (
              <div className="flex items-center">
                <span className="mr-1 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500 sm:h-2 sm:w-2"></span>
                <span className="truncate text-xs text-gray-600 dark:text-gray-400 sm:text-xs">
                  {incomeEvents.length} {t("income")}
                </span>
              </div>
            )}
            {expenseEvents.length > 0 && (
              <div className="flex items-center">
                <span className="mr-1 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-red-500 sm:h-2 sm:w-2"></span>
                <span className="truncate text-xs text-gray-600 dark:text-gray-400 sm:text-xs">
                  {expenseEvents.length} {t("expense")}
                </span>
              </div>
            )}

            {/* Total Events Count for Mobile */}
            {incomeEvents.length + expenseEvents.length > 2 && (
              <div className="flex items-center sm:hidden">
                <span className="mr-1 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-gray-400"></span>
                <span className="truncate text-xs text-gray-500 dark:text-gray-500">
                  +{incomeEvents.length + expenseEvents.length - 2} {t("more")}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </td>
  );
};

export default CalendarDay;

// src/lib/interfaces/userDetails.ts

/**
 * User permissions structure for each module
 */
export interface UserPermissions {
  [module: string]: {
    sidebar: boolean;
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
    export: boolean;
    import_data: boolean;
    approve: boolean;
    reject: boolean;
    analytics: boolean;
    notifications: boolean;
    view_history: boolean;
    // Additional module-specific permissions
    manage_accounts?: boolean;
    view_activity_log?: boolean;
    view_terms?: boolean;
    manage_templates?: boolean;
  };
}

/**
 * Frontend user details interface
 */
export interface UserDetails {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  password: string; // Note: Be careful with password handling in frontend
  created_at: string;
  role: string;
  lastActivity: string;
  permissions?: UserPermissions; // Optional as it might be loaded separately
}
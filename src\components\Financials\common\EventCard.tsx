import React, { useState } from "react";
import {
  FaEdit,
  FaTrashAlt,
  FaCalendarAlt,
  FaMoneyBillAlt,
  FaUser,
  FaMapMarkerAlt,
  FaInfoCircle,
  FaChevronDown,
  FaLock,
} from "react-icons/fa";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";
import IncomeServices from "@/lib/income";
import ExpenseServices from "@/lib/expenses";
import { SuccessPopup } from "@/components/common/successPopUp";
import { ErrorPopup } from "@/components/common/errorPopUp";
import { canEditIncome } from "@/types/income";
import { canEditExpense } from "@/types/expenses";

interface EventCardProps {
  event: EventDetails;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onClick: (id: string) => void;
}

const statusOptions = [
  "completed",
  "pending",
  "cancelled",
  "upcoming",
  "overdue",
] as const;

type StatusType = (typeof statusOptions)[number];

const EventCard: React.FC<EventCardProps> = ({
  event,
  onEdit,
  onDelete,
  onClick,
}) => {
  const { t, language } = useLanguage();
  const titleStyle =
    event.category === "income"
      ? "text-green-600 dark:text-green-400"
      : "text-red-600 dark:text-red-400";

  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [currentStatus, setCurrentStatus] = useState<StatusType>(
    event.status as StatusType,
  );
  const [dateValue, setDateValue] = useState(""); // Add state for date

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const handleStatusChange = async (newStatus: StatusType) => {
    try {
      // If changing to completed, validate that date is provided
      if (newStatus === "completed" && !dateValue) {
        setShowError(true);
        const errorMsg =
          event.category === "income"
            ? t("Please select received date")
            : t("Please select paid date");
        setErrorMessage(errorMsg);
        setTimeout(() => setShowError(false), 2500);
        setShowStatusDropdown(false);
        return;
      }

      const requestBody: any = { status: newStatus };

      // Add appropriate date field based on category
      if (newStatus === "completed" && dateValue) {
        if (event.category === "income") {
          requestBody.received_date = dateValue;
        } else {
          requestBody.paid_date = dateValue;
        }
      }

      if (event.category === "income") {
        await IncomeServices().updateIncomeStatus(event.id, requestBody);
      } else {
        await ExpenseServices().updateExpenseStatus(event.id, requestBody);
      }

      setCurrentStatus(newStatus);
      setShowSuccess(true);
      setSuccessMessage(t("Status updated successfully!"));
      setTimeout(() => setShowSuccess(false), 2000);
    } catch (err) {
      setShowError(true);
      setErrorMessage(t("Failed to update status."));
      setTimeout(() => setShowError(false), 2500);
    }
    setShowStatusDropdown(false);
  };

  return (
    <div
      className="cursor-pointer rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
      onClick={() => onClick(event.id)}
    >
      {/* Table-like Row Layout */}
      <div className="grid grid-cols-2 items-center gap-4 sm:grid-cols-3 md:grid-cols-6">
        {/* Title */}
        <div className="flex items-center gap-2">
          <h2
            className={`text-sm font-semibold ${titleStyle} truncate`}
            style={{ maxWidth: "calc(100% - 24px)" }}
          >
            {event.title}
          </h2>
        </div>

        {/* Date */}
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaCalendarAlt className="h-4 w-4" />
          <span>
            {new Date(event.dueDate).toLocaleDateString(
              language === "ar" ? "ar-EG" : "en-GB",
              {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
              },
            )}
          </span>
        </div>

        {/* Amount */}
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaMoneyBillAlt className={titleStyle} />
          <span className={titleStyle}>{formatCurrency(event.amount)}</span>
        </div>

        {/* Status with Dropdown */}
        <div className="relative flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaInfoCircle className="h-4 w-4" />
          <button
            type="button"
            className={`flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium focus:outline-none ${
              currentStatus === "pending"
                ? "bg-yellow-100 text-yellow-600 dark:bg-yellow-800 dark:text-yellow-300"
                : currentStatus === "completed"
                  ? "bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-300"
                  : "bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-300"
            }`}
            onClick={(e) => {
              e.stopPropagation();
              setShowStatusDropdown((prev) => !prev);
            }}
          >
            {t(currentStatus)}
            <FaChevronDown className="ml-1 h-3 w-3" />
          </button>
          {showStatusDropdown && (
            <div className="absolute left-0 z-20 mt-1 min-w-[200px] rounded border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-900">
              {statusOptions.map((status) => (
                <div key={status} className="block">
                  <button
                    className={`block w-full px-3 py-2 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-800 ${
                      currentStatus === status
                        ? "font-bold text-blue-600 dark:text-blue-400"
                        : "text-gray-700 dark:text-gray-200"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (status === "completed") {
                        // Don't close dropdown yet, show date input
                        setCurrentStatus(status);
                      } else {
                        handleStatusChange(status);
                      }
                    }}
                  >
                    {t(status)}
                  </button>
                  {/* Date input for completed status */}
                  {currentStatus === "completed" && status === "completed" && (
                    <div className="px-3 pb-3">
                      <label className="mb-1 block text-xs font-medium text-gray-700 dark:text-gray-300">
                        {event.category === "income"
                          ? t("Received Date")
                          : t("Paid Date")}{" "}
                        *
                      </label>
                      <input
                        type="date"
                        value={dateValue}
                        onChange={(e) => setDateValue(e.target.value)}
                        className="w-full rounded border border-gray-300 px-2 py-1 text-xs focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                        onClick={(e) => e.stopPropagation()}
                      />
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStatusChange("completed");
                        }}
                        className="mt-2 w-full rounded bg-primary px-2 py-1 text-xs text-white hover:bg-primary/90"
                      >
                        {t("Update Status")}
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Priority */}
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <span
            className={`rounded-full px-2 py-1 text-xs font-medium ${
              event.priority === "low"
                ? "bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-300"
                : event.priority === "medium"
                  ? "bg-yellow-100 text-yellow-600 dark:bg-yellow-800 dark:text-yellow-300"
                  : "bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-300"
            }`}
          >
            {t(event.priority)}
          </span>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end gap-4">
          {(() => {
            const canEdit =
              event.category === "income"
                ? canEditIncome(event)
                : canEditExpense(event);
            const isLinked = event.reservation_id || event.contract_id;

            return (
              <div className="group relative">
                <button
                  className={`transition-colors ${
                    canEdit
                      ? "text-gray-500 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                      : "cursor-not-allowed text-gray-300 dark:text-gray-600"
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (canEdit) {
                      onEdit(event.id);
                    }
                  }}
                  disabled={!canEdit}
                  title={
                    !canEdit
                      ? isLinked
                        ? event.reservation_id
                          ? t("Cannot edit: linked to reservation")
                          : t("Cannot edit: linked to contract")
                        : t("Cannot edit this event")
                      : t("Edit event")
                  }
                >
                  {canEdit ? (
                    <FaEdit className="h-5 w-5" />
                  ) : (
                    <FaLock className="h-5 w-5" />
                  )}
                </button>

                {/* Tooltip for disabled state */}
                {!canEdit && (
                  <div className="absolute bottom-full left-1/2 z-10 mb-2 -translate-x-1/2 transform whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-white opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                    {isLinked
                      ? event.reservation_id
                        ? t("Linked to reservation")
                        : t("Linked to contract")
                      : t("Cannot edit")}
                  </div>
                )}
              </div>
            );
          })()}
        </div>
      </div>

      {/* Divider */}
      <hr className="my-4 border-gray-200 dark:border-gray-700" />

      {/* Contact Row */}
      <div className="mt-4">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaUser className="h-4 w-4" />
          <span className="font-medium">{t("Contact")}:</span>
          {event.contact ? (
            <span className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300">
              {event.contact.name}
            </span>
          ) : (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {t("No contact available")}
            </span>
          )}
        </div>
      </div>

      {/* Divider */}
      <hr className="my-4 border-gray-200 dark:border-gray-700" />

      {/* Location Row */}
      <div className="mt-2">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaMapMarkerAlt className="h-4 w-4" />
          <span className="font-medium">{t("Location")}:</span>
          {event.location ? (
            <span className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300">
              {event.location.name}
            </span>
          ) : (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {t("No location available")}
            </span>
          )}
        </div>
      </div>

      <hr className="my-4 border-gray-200 dark:border-gray-700" />

      {/* Created At Row */}
      {event.created_at && (
        <div className="mt-2">
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <FaCalendarAlt className="h-4 w-4" />
            <span className="font-medium">{t("Created at")}:</span>
            <span className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300">
              {new Date(event.created_at).toLocaleDateString(
                language === "ar" ? "ar-EG" : "en-US",
              )}
            </span>
          </div>
        </div>
      )}

      {/* Payment Date Row - Only show when completed */}
      {currentStatus === "completed" && (
        <>
          <hr className="my-4 border-gray-200 dark:border-gray-700" />
          <div className="mt-2">
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <FaCalendarAlt className="h-4 w-4" />
              <span className="font-medium">
                {event.category === "income"
                  ? t("Received Date")
                  : t("Paid Date")}
                :
              </span>
              {event.category === "income" && event.received_date ? (
                <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-700 dark:bg-green-700 dark:text-green-300">
                  {new Date(event.received_date).toLocaleDateString(
                    language === "ar" ? "ar-EG" : "en-US",
                  )}
                </span>
              ) : event.category === "expense" && event.paid_date ? (
                <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-700 dark:bg-green-700 dark:text-green-300">
                  {new Date(event.paid_date).toLocaleDateString(
                    language === "ar" ? "ar-EG" : "en-US",
                  )}
                </span>
              ) : (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t("Date not available")}
                </span>
              )}
            </div>
          </div>
        </>
      )}

      {/* Popups */}
      {showSuccess && (
        <SuccessPopup
          message={successMessage || t("Status updated successfully!")}
          onClose={() => setShowSuccess(false)}
        />
      )}
      {showError && (
        <ErrorPopup
          message={errorMessage || t("Failed to update status.")}
          onClose={() => setShowError(false)}
        />
      )}
    </div>
  );
};

export default EventCard;

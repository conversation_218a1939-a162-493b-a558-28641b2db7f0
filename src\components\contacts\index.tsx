"use client";

import React, { useState } from "react";
import { Plus, Users, UserPlus } from "lucide-react";
import { Card, CardContent } from "../cards/card";
import ContactForm from "./ContactForm";
import ContactList from "./ContactList";
import ContactDetails from "./ContactDetails";
import InlineEditForm from "./InlineEditForm";
import Breadcrumb from "../Breadcrumbs/Breadcrumb";
import { Contact } from "@/lib/types/contacts";
import useLanguage from "@/hooks/useLanguage";
import { useContactServices } from "@/hooks/useContact";
import { LoadingComp } from "../common/Loading";
import { SuccessPopup } from "../common/successPopUp";
import { ErrorPopup } from "../common/errorPopUp";
import { usePermissions } from "@/hooks/usePermissions";

const ContactPage = () => {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [isContactLoading, setIsContactLoading] = useState(false);
  const contactService = useContactServices();
  const [message, setMessage] = useState("");
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [showErrorPopup, setShowErrorPopup] = useState(false);
  const { hasPermission, permissionsLoaded } = usePermissions();

  // Check permissions loading state
  if (!permissionsLoaded) {
    return (
      <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }

  // Check if user has view permission for contacts
  if (!hasPermission("contacts", "view")) {
    return (
      <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
        <div className="flex items-center justify-center h-full">
          <Card className="w-full max-w-md">
            <CardContent className="text-center py-12">
              <Users size={48} className="mx-auto mb-4 text-gray-400" />
              <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                {t("accessDenied")}
              </h2>
              <p className="text-gray-500 dark:text-gray-400">
                {t("noPermissionToViewContacts")}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingComp />
      </div>
    );
  }
  const handleSaveContact = async (data: Partial<Contact>) => {
    setIsLoading(true);
    try {
      if (isEditing && selectedContact) {
        console.log("Updating contact:", selectedContact.id, data);

        const updated = await contactService.updateContact(selectedContact.id, {
          name: data.name,
          email: data.email,
          phone: data.phone,
          address: data.address,
          company: data.company,
          type: data.type,
        });
        setSelectedContact({
          id: String(updated.contact_id),
          name: updated.name,
          email: updated.email,
          phone: updated.phone,
          address: updated.address,
          company: updated.company,
          type: updated.type?.filter(
            (t: string): t is "client" | "agency" | "other" =>
              ["client", "agency", "other"].includes(t),
          ),
          sharedLocations: updated.sharedLocations
            ? updated.sharedLocations
            : [],
          ownedLocations: [],
          createdAt: updated.createdAt ?? "",
          createdBy: updated.createdBy ?? "",
          balance: updated.balance ?? 0,
        });
      } else {
        const created = await contactService.createContact({
          name: data.name ?? "",
          email: data.email ?? "",
          phone: data.phone ?? "",
          address: data.address ?? "",
          company: data.company ?? "",
          type: data.type ?? [],
        });
        setSelectedContact({
          id: String(created.contact_id),
          name: created.name,
          email: created.email,
          phone: created.phone,
          address: created.address,
          company: created.company,
          type: created.type?.filter(
            (t: string): t is "client" | "agency" | "other" =>
              ["client", "agency", "other"].includes(t),
          ),
          sharedLocations: created.sharedLocations
            ? created.sharedLocations
            : [],
          ownedLocations:
            created.ownedLocations?.map((location) => ({
              id: location.id,
              name: location.name,
              address: location.address ?? "", // Ensure address is provided
              capacity: location.capacity ?? 0, // Ensure capacity is provided
              is_active: location.is_active ?? false, // Ensure is_active is provided
            })) ?? [],
          createdAt: created.createdAt ?? "",
          createdBy: created.createdBy ?? "",
          balance: created.balance ?? 0,
        });
      }
      setIsLoading(false);
      setIsFormOpen(false);
      setIsEditing(false);
      setMessage(t("contactSavedSuccessfully"));
      setShowSuccessPopup(true);
      // set time out and refresh the contact list
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } catch (error) {
      console.error("Failed to save contact:", error);
      setIsLoading(false);
      setShowErrorPopup(true);
      setMessage(t("errorSavingContact"));
    }
  };

  const handleContactSelect = (contact: Contact) => {
    setSelectedContact(contact);
    setIsEditing(false);
    setIsFormOpen(false);
  };

  const handleDeleteContact = () => {
    setTimeout(() => {
      window.location.reload();
    }, 2000);
    setSelectedContact(null);
  };

  return (
    <>
      {showSuccessPopup && (
        <SuccessPopup
          message={message}
          onClose={() => setShowSuccessPopup(false)}
        />
      )}
      {showErrorPopup && (
        <ErrorPopup
          message={message}
          onClose={() => setShowErrorPopup(false)}
        />
      )}

      {selectedContact && !isEditing ? (
        // Fullscreen Contact Details View
        <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
          <ContactDetails
            contact={selectedContact}
            onEdit={() => {
              if (hasPermission("contacts", "edit")) {
                setIsEditing(true);
                setIsFormOpen(true);
              }
            }}
            onDelete={handleDeleteContact}
            onClose={() => setSelectedContact(null)}
            canEdit={hasPermission("contacts", "edit")}
            canDelete={hasPermission("contacts", "delete")}
            canExport={hasPermission("contacts", "export")}
          />
        </div>
      ) : (
        // Full Contact Management UI
        <div className="space-y-4 sm:space-y-6 lg:space-y-8">
          <Breadcrumb pageName={t("contacts")} />
          <div className="min-h-screen bg-gray-50 p-3 dark:bg-boxdark sm:p-4 lg:p-6">
            <div className="mb-4 sm:mb-6">
              <div className="flex items-center gap-3">
                <Users size={20} className="text-blue-600 sm:size-6" />
                <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100 sm:text-2xl lg:text-3xl">
                  {t("contactsManagement")}
                </h1>
              </div>
            </div>

            {/* Add New Contact Section */}
            {!isFormOpen && hasPermission("contacts", "create") && (
              <div className="mb-4 sm:mb-6">
                <Card className="w-full border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/20">
                  <CardContent className="p-4 sm:p-6">
                    <div className="flex flex-col items-center justify-between space-y-4 sm:flex-row sm:space-y-0">
                      <div className="text-center sm:text-left">
                        <h2 className="text-lg font-bold text-blue-800 dark:text-blue-300 sm:text-xl">
                          {t("addNewContact")}
                        </h2>
                        <p className="mt-1 text-sm text-blue-600 dark:text-blue-400 sm:text-base">
                          {t("createNewContactDescription")}
                        </p>
                      </div>
                      <button
                        onClick={() => {
                          setIsFormOpen(true);
                          setIsEditing(false);
                        }}
                        className="flex w-full items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2.5 text-sm font-medium text-white shadow-md transition-all hover:bg-blue-700 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto sm:gap-3 sm:px-6 sm:py-3 sm:text-base"
                      >
                        <Plus size={18} className="sm:size-5" />
                        {t("addContact")}
                      </button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Contact Form Section */}
            {isFormOpen && !isEditing && hasPermission("contacts", "create") && (
              <div className="mb-4 sm:mb-6">
                <Card className="w-full border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/20">
                  <CardContent className="p-4 sm:p-6">
                    <div className="mb-4 sm:mb-6">
                      <h2 className="text-center text-xl font-bold text-blue-800 dark:text-blue-300 sm:text-2xl">
                        {t("addNewContact")}
                      </h2>
                      <p className="mt-1 text-center text-sm text-blue-600 dark:text-blue-400 sm:text-base">
                        {t("fillContactDetails")}
                      </p>
                    </div>
                    <div className="mx-auto max-w-4xl">
                      <ContactForm
                        isOpen={isFormOpen}
                        onClose={() => setIsFormOpen(false)}
                        onSubmit={handleSaveContact}
                        view="sidebar"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Contact Details/Edit Section */}
            <div className="mb-4 sm:mb-6">
              {selectedContact && isEditing && hasPermission("contacts", "edit") ? (
                <InlineEditForm
                  contact={selectedContact}
                  onSave={handleSaveContact}
                  onCancel={() => setIsEditing(false)}
                />
              ) : (
                <Card className="flex w-full items-center justify-center dark:border-gray-700 dark:bg-gray-800">
                  <CardContent className="py-8 text-center sm:py-12">
                    <UserPlus
                      size={40}
                      className="mx-auto mb-4 text-gray-400 sm:size-12"
                    />
                    <h3 className="mb-2 text-lg font-medium text-gray-700 dark:text-gray-300 sm:text-xl">
                      {t("selectAContactToViewDetails")}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 sm:text-base">
                      {t("Choose a contact from the list below to see their details")}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Contact List Section */}
            <div className="space-y-4">
              <ContactList
                onSelect={handleContactSelect}
                selectedId={selectedContact?.id}
                canEdit={hasPermission("contacts", "edit")}
                canDelete={hasPermission("contacts", "delete")}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ContactPage;

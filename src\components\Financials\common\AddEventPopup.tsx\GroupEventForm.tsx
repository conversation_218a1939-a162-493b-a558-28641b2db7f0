import React, { useState, useEffect } from "react";
import { FaPlus, FaTrash } from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import { toast } from "react-toastify";

interface ChildEvent {
  amount: number;
  due_date: string;
  notification_view_date: string;
}

interface GroupEventFormProps {
  priority: string;
  setPriority: (value: string) => void;
  type_id: string;
  setTypeId: (value: string) => void;
  parentTitle: string;
  setParentTitle: (value: string) => void;
  parentAmount: number;
  setParentAmount: (value: number) => void;
  parentDescription: string;
  setParentDescription: (value: string) => void;
  children: ChildEvent[];
  setChildren: (value: ChildEvent[]) => void;
  types: { id: string; name: string }[];
}

const GroupEventForm: React.FC<GroupEventFormProps> = ({
  priority,
  setPriority,
  type_id,
  setTypeId,
  parentTitle,
  setParentTitle,
  parentAmount,
  setParentAmount,
  parentDescription,
  setParentDescription,
  children,
  setChildren,
  types
}) => {
  const { t, language } = useLanguage();
  const locale = language === "ar" ? arSA : enUS;
  const [amountError, setAmountError] = useState<string | null>(null);

  useEffect(() => {
    validateAmounts();
  }, [parentAmount, children]);

  const validateAmounts = () => {
    if (children.length === 0) {
      setAmountError(null);
      return;
    }

    const childrenSum = children.reduce((sum, child) => sum + child.amount, 0);
    
    if (parentAmount !== childrenSum) {
      setAmountError(t("Parent amount must equal the sum of all child amounts"));
    } else {
      setAmountError(null);
    }
  };

  const handleParentAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    setParentAmount(Number(value));
  };

  const handleChildAmountChange = (index: number, value: string) => {
    const updatedChildren = [...children];
    updatedChildren[index].amount = Number(value.replace(/\D/g, ''));
    setChildren(updatedChildren);
  };

  const addChildEvent = () => {
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() + 7);
    
    const notificationDate = new Date(defaultDate);
    notificationDate.setDate(notificationDate.getDate() - 5);
    
    setChildren([
      ...children,
      {
        amount: 0,
        due_date: defaultDate.toISOString(),
        notification_view_date: notificationDate.toISOString()
      }
    ]);
  };

  const removeChildEvent = (index: number) => {
    const updatedChildren = [...children];
    updatedChildren.splice(index, 1);
    setChildren(updatedChildren);
  };

  const handleChildDateChange = (
    index: number, 
    field: 'due_date' | 'notification_view_date', 
    date: Date | null
  ) => {
    if (!date) return;
    
    const updatedChildren = [...children];
    
    updatedChildren[index] = {
      ...updatedChildren[index],
      [field]: date.toISOString()
    };
    
    if (field === 'due_date') {
      const notificationDate = new Date(date);
      notificationDate.setDate(notificationDate.getDate() - 5);
      updatedChildren[index].notification_view_date = notificationDate.toISOString();
    }
    
    setChildren(updatedChildren);
  };

  return (
    <div>
      <div className="mb-6 p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">{t("Parent Event")}</h3>
        
        <div className="grid grid-cols-1 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Title")}</label>
            <input
              type="text"
              value={parentTitle}
              onChange={(e) => setParentTitle(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Amount")}</label>
            <input
              type="text"
              value={parentAmount}
              onChange={handleParentAmountChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              placeholder={t("0")}
            />
            {amountError && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{amountError}</p>
            )}
            <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {t("Sum of child amounts")}: {children.reduce((sum, child) => sum + child.amount, 0)}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Description")}</label>
            <textarea
              value={parentDescription}
              onChange={(e) => setParentDescription(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              rows={3}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Priority")}</label>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              >
                <option value="low">{t("Low")}</option>
                <option value="medium">{t("Medium")}</option>
                <option value="high">{t("High")}</option>
                <option value="critical">{t("Critical")}</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Type")}</label>
              <select
                value={type_id}
                onChange={(e) => setTypeId(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              >
                <option value="">{t("Select Type")}</option>
                {types.map((type) => (
                  <option key={type.id} value={type.id}>{type.name}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100">{t("Child Events")}</h3>
          <button
            type="button"
            onClick={addChildEvent}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center gap-2"
          >
            <FaPlus /> {t("Add Child Event")}
          </button>
        </div>

        {children.length === 0 ? (
          <div className="text-center py-6 text-gray-500 dark:text-gray-400">
            {t("No child events added yet")}
          </div>
        ) : (
          <div className="space-y-4">
            {children.map((child, index) => (
              <div key={index} className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
                <div className="flex justify-between items-start mb-3">
                  <h4 className="font-medium text-gray-700 dark:text-gray-200">{t("Child Event")} #{index + 1}</h4>
                  <button
                    type="button"
                    onClick={() => removeChildEvent(index)}
                    className="p-2 text-red-500 hover:text-red-700"
                    title={t("Remove")}
                  >
                    <FaTrash className="w-4 h-4" />
                  </button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Amount")}</label>
                    <input
                      type="text"
                      value={child.amount}
                      onChange={(e) => handleChildAmountChange(index, e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
                      placeholder={t("0")}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Due Date")}</label>
                    <DatePicker
                      selected={child.due_date ? new Date(child.due_date) : null}
                      onChange={(date) => handleChildDateChange(index, 'due_date', date)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
                      dateFormat="yyyy-MM-dd"
                      locale={locale}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Notification Date")}</label>
                    <DatePicker
                      selected={child.notification_view_date ? new Date(child.notification_view_date) : null}
                      onChange={(date) => handleChildDateChange(index, 'notification_view_date', date)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
                      dateFormat="yyyy-MM-dd"
                      locale={locale}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default GroupEventForm;
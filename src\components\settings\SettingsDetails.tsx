"use client";
import React, { useState, useEffect } from "react";
import { 
  User, 
  Phone, 
  Mail, 
  AtSign, 
  Edit2, 
  Save, 
  X, 
  LogOut,
  Shield,
  MenuSquare
} from "lucide-react";
import { useAuth } from "@/app/context/AuthContext";
import useLanguage from "@/hooks/useLanguage";
import { User as UserType } from "@/lib/interfaces/User";
import { useSession } from "next-auth/react";

const SettingsDetails: React.FC = () => {
  const { data: session } = useSession();
  const { logout } = useAuth();
  const { t } = useLanguage();

  const [currentUser, setCurrentUser] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [showPrivileges, setShowPrivileges] = useState(false);

  const [formData, setFormData] = useState({
    fullName: "",
    phoneNumber: "",
    emailAddress: "",
    username: "",
    bio: ""
  });

  useEffect(() => {
    console.log("Session data:", session);
    
    if (session?.user) {
      const userData: UserType = {
        username: session.user.name || "",
        first_name: "",
        last_name: "",
        email: session.user.email || "",
        role: (session.user as any).role || "USER",
        created_at: (session.user as any).created_at || new Date().toISOString(),
        updated_at: (session.user as any).updated_at || new Date().toISOString(),
        permissions: (session as any).permissions || null,
      };

      setCurrentUser(userData);
      setFormData({
        fullName: userData.username,
        phoneNumber: "",
        emailAddress: userData.email,
        username: userData.username,
        bio: "",
      });

      setLoading(false);
    }
  }, [session]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = () => {
    setIsEditing(false);
  };

  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="bg-white shadow-xl rounded-2xl overflow-hidden">
      <div className="p-4 sm:p-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-3 sm:gap-0">
          <h3 className="text-xl sm:text-2xl font-semibold text-gray-800">
            {t("personalInformation")}
          </h3>
          <div className="flex flex-wrap gap-2 w-full sm:w-auto justify-end">
            {isEditing ? (
              <>
                <button 
                  onClick={() => setIsEditing(false)}
                  className="flex items-center bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition"
                >
                  <X size={20} className="mr-2" /> {t("cancel")}
                </button>
                <button 
                  onClick={handleSave}
                  className="flex items-center bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition"
                >
                  <Save size={20} className="mr-2" /> {t("save")}
                </button>
              </>
            ) : (
              <button 
                onClick={() => setIsEditing(true)}
                className="flex items-center bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition"
              >
                <Edit2 size={20} className="mr-2" />{t("editProfile")}
              </button>
            )}
          </div>
        </div>

        {/* Form */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
          <div className="flex flex-col">
            <label className="flex items-center text-gray-700 mb-2">
              <User className="mr-2 text-primary" size={20} /> {t("email")}
            </label>
            <input
              type="text"
              name="email"
              value={formData.emailAddress}
              onChange={handleInputChange}
              disabled={!isEditing}
              className="w-full px-4 py-2 border rounded-lg"
            />
          </div>

        </div>

        {/* Privileges Section */}
        {currentUser?.permissions && (
          <div className="mt-6 sm:mt-8 border-t border-gray-200 pt-4 sm:pt-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2 sm:gap-0">
              <h3 className="text-xl font-semibold text-gray-800 flex items-center">
                <Shield className="mr-2 text-primary" size={20} /> {t("myPrivileges")}
              </h3>
              <button 
                onClick={() => setShowPrivileges(!showPrivileges)}
                className="text-sm text-primary hover:underline"
              >
                {showPrivileges ? t("hideDetails") : t("showDetails")}
              </button>
            </div>

            {showPrivileges && (
        <div className="mt-4">
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(currentUser?.permissions).map(([module, modulePermissions]) => (
                <div key={module} className="bg-gray-50 p-3 sm:p-4 rounded-lg border border-gray-200">
                  <h5 className="text-sm font-medium text-gray-700 mb-3 flex items-center justify-between">
                    <span className="capitalize">
                      {t(module)} 
                    </span>
      
                  </h5>
                  <div className="space-y-2 max-h-60 overflow-y-auto pr-1">
                    {Object.entries(modulePermissions).map(([permission, value]) => (
                      <div key={permission} className="flex items-center justify-between">
                        <span className="text-sm capitalize text-gray-600">
                          {t(permission)}
                        </span>
                        <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'
                        }`}>
                          {value ? t("allowed") : t("denied")}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
                    </div>
                  </div>
                </div>
            )}
          </div>
        )}

        {/* Logout Button */}
        <div className="mt-6 sm:mt-8 flex justify-center sm:justify-end">
          <button
            onClick={logout}
            className="flex items-center bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition"
          >
            <LogOut size={20} className="mr-2" /> {t("logout")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsDetails;

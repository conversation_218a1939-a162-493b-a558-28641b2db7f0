import React, { useState, use<PERSON>emo, useCallback } from "react";
import <PERSON> from "papaparse";
import * as XLSX from "xlsx";
import { useDropzone } from "react-dropzone";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
// Note: Using native HTML elements since checkbox and scroll-area components are not available
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import useLanguage from "@/hooks/useLanguage";
import { Eye, EyeOff, Settings, Download, Upload, AlertCircle, CheckCircle } from "lucide-react";

// Enhanced types for better organization
export type ColumnType = 'text' | 'date' | 'dropdown' | 'modal';

export type ColumnDefinition = {
  key: string;
  label: string;
  type: ColumnType;
  required?: boolean;
  options?: string[];
  component?: React.FC<any>;
};

export type ColumnMapping = {
  targetField: string;
  sourceField: string | null;
  defaultValue?: string;
};

export type ValidationError = {
  row: number;
  column: string;
  message: string;
};

export type FileData = {
  id: string;
  name: string;
  rawData: any[];
  sourceColumns: string[];
  mappings: ColumnMapping[];
  validationErrors: ValidationError[];
  isValid: boolean;
};
interface DataImporterProps<T> {
  columns: ColumnDefinition[];
  onImport: (data: T[]) => void;
  maxPreviewRows?: number;
  allowedFileTypes?: string[];
  onValidationChange?: (isValid: boolean, errors: ValidationError[]) => void;
}

export function DataImporter<T>({
  columns,
  onImport,
  maxPreviewRows = 100,
  allowedFileTypes = ['.csv', '.xlsx'],
  onValidationChange,
}: DataImporterProps<T>) {
  const [filesData, setFilesData] = useState<FileData[]>([]);
  const [activeFileId, setActiveFileId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'mapping' | 'preview'>('mapping');
  const [visibleColumns, setVisibleColumns] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const { t, language } = useLanguage();

  const [modalState, setModalState] = useState<{
    fileId: string;
    rowIndex: number;
    field: string;
    data: any;
  } | null>(null);

  // Initialize visible columns when columns change
  React.useEffect(() => {
    if (columns && columns.length > 0) {
      setVisibleColumns(new Set(columns.map(col => col.key)));
    }
  }, [columns]);

  // Memoized active file
  const activeFile = useMemo(() =>
    filesData.find(file => file.id === activeFileId),
    [filesData, activeFileId]
  );

  // Validation logic
  const validateFile = useCallback((file: FileData): ValidationError[] => {
    const errors: ValidationError[] = [];

    if (!columns || !file.rawData) {
      return errors;
    }

    file.rawData.forEach((row, rowIndex) => {
      columns.forEach(column => {
        if (column.required) {
          const mapping = file.mappings.find(m => m.targetField === column.key);
          const value = mapping?.sourceField ? row[mapping.sourceField] : row[column.key];

          if (!value || value.toString().trim() === '') {
            errors.push({
              row: rowIndex,
              column: column.key,
              message: `${column.label} is required`
            });
          }
        }
      });
    });

    return errors;
  }, [columns]);

  // Update file validation
  const updateFileValidation = useCallback((fileId: string) => {
    setFilesData(prev => prev.map(file => {
      if (file.id === fileId) {
        const errors = validateFile(file);
        const updatedFile = {
          ...file,
          validationErrors: errors,
          isValid: errors.length === 0
        };

        if (onValidationChange) {
          onValidationChange(updatedFile.isValid, errors);
        }

        return updatedFile;
      }
      return file;
    }));
  }, [validateFile, onValidationChange]);

  const processFile = useCallback(async (file: File): Promise<FileData> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      const fileId = `${file.name}-${Date.now()}`;

      reader.onload = (e) => {
        try {
          const content = e.target?.result;
          if (!content) throw new Error('Failed to read file content');

          let rawData: any[] = [];
          let sourceColumns: string[] = [];

          if (file.name.endsWith('.csv')) {
            const parsed = Papa.parse(content as string, { header: true });
            rawData = parsed.data.filter((row) =>
              Object.values(row as Record<string, unknown>).some((value) =>
                value?.toString().trim()
              )
            );
            sourceColumns = parsed.meta.fields || [];
          } else if (file.name.endsWith('.xlsx')) {
            const workbook = XLSX.read(content, { type: 'binary' });
            const sheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = XLSX.utils.sheet_to_json(sheet as XLSX.WorkSheet) as Record<string, unknown>[];

            rawData = jsonData.filter((row) =>
              Object.values(row).some((value) => value?.toString().trim())
            );
            sourceColumns = rawData.length > 0 ? Object.keys(rawData[0]) : [];
          }

          // Initialize mappings with smart matching
          const mappings: ColumnMapping[] = (columns || []).map(column => {
            const matchingSource = sourceColumns.find(source =>
              source.toLowerCase().includes(column.key.toLowerCase()) ||
              column.key.toLowerCase().includes(source.toLowerCase()) ||
              source.toLowerCase() === column.label.toLowerCase()
            );

            return {
              targetField: column.key,
              sourceField: matchingSource || null,
              defaultValue: ''
            };
          });

          const fileData: FileData = {
            id: fileId,
            name: file.name,
            rawData,
            sourceColumns,
            mappings,
            validationErrors: [],
            isValid: false
          };

          resolve(fileData);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error('Failed to read file'));

      if (file.name.endsWith('.xlsx')) {
        reader.readAsBinaryString(file);
      } else {
        reader.readAsText(file);
      }
    });
  }, [columns]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    try {
      const processedFiles = await Promise.all(
        acceptedFiles.map(file => processFile(file))
      );

      setFilesData(prev => [...prev, ...processedFiles]);

      if (processedFiles.length > 0) {
        setActiveFileId(processedFiles[0].id);
        // Validate the first file
        setTimeout(() => updateFileValidation(processedFiles[0].id), 0);
      }
    } catch (error) {
      console.error('Error processing files:', error);
    }
  }, [processFile, updateFileValidation]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: allowedFileTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {})
  });

  const updateMapping = useCallback((fileId: string, targetField: string, sourceField: string | null, defaultValue?: string) => {
    setFilesData(prev => prev.map(file => {
      if (file.id === fileId) {
        const updatedMappings = file.mappings.map(mapping =>
          mapping.targetField === targetField
            ? { ...mapping, sourceField, defaultValue: defaultValue || mapping.defaultValue }
            : mapping
        );

        const updatedFile = { ...file, mappings: updatedMappings };
        setTimeout(() => updateFileValidation(fileId), 0);
        return updatedFile;
      }
      return file;
    }));
  }, [updateFileValidation]);

  const updateCellValue = useCallback((fileId: string, rowIndex: number, field: string, value: any) => {
    setFilesData(prev => prev.map(file => {
      if (file.id === fileId) {
        const updatedData = file.rawData.map((row, i) =>
          i === rowIndex ? { ...row, [field]: value } : row
        );

        const updatedFile = { ...file, rawData: updatedData };
        setTimeout(() => updateFileValidation(fileId), 0);
        return updatedFile;
      }
      return file;
    }));
  }, [updateFileValidation]);

  const applyDefaultValue = useCallback((fileId: string, targetField: string, defaultValue: string) => {
    const file = filesData.find(f => f.id === fileId);
    if (!file) return;

    const mapping = file.mappings.find(m => m.targetField === targetField);
    const sourceField = mapping?.sourceField;

    setFilesData(prev => prev.map(f => {
      if (f.id === fileId) {
        const updatedData = f.rawData.map(row => {
          const currentValue = sourceField ? row[sourceField] : row[targetField];
          if (!currentValue || currentValue.toString().trim() === '') {
            return { ...row, [sourceField || targetField]: defaultValue };
          }
          return row;
        });

        const updatedFile = { ...f, rawData: updatedData };
        setTimeout(() => updateFileValidation(fileId), 0);
        return updatedFile;
      }
      return f;
    }));
  }, [filesData, updateFileValidation]);

  const handleImport = useCallback(() => {
    if (!activeFile || !activeFile.isValid || !columns) return;

    const mappedData = activeFile.rawData.map((row) => {
      const result: any = {};

      activeFile.mappings.forEach((mapping) => {
        const column = columns.find(col => col.key === mapping.targetField);
        if (!column) return;

        let value = mapping.sourceField ? row[mapping.sourceField] : row[mapping.targetField];

        // Apply default value if empty
        if ((!value || value.toString().trim() === '') && mapping.defaultValue) {
          value = mapping.defaultValue;
        }

        // Process based on column type
        switch (column.type) {
          case 'date':
            result[mapping.targetField] = value ? new Date(value).toISOString() : '';
            break;
          case 'dropdown':
          case 'modal':
          case 'text':
          default:
            result[mapping.targetField] = value || '';
            break;
        }
      });

      return result;
    });

    onImport(mappedData as T[]);
  }, [activeFile, columns, onImport]);

  // Filtered and paginated data for preview
  const filteredData = useMemo(() => {
    if (!activeFile) return [];

    let data = activeFile.rawData;

    if (searchTerm) {
      data = data.filter(row =>
        Object.values(row).some(value =>
          value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    return data.slice(0, maxPreviewRows);
  }, [activeFile, searchTerm, maxPreviewRows]);

  // Get visible column definitions
  const visibleColumnDefs = useMemo(() =>
    (columns || []).filter(col => visibleColumns.has(col.key)),
    [columns, visibleColumns]
  );

  const currentModalColumn = modalState
    ? (columns || []).find((col) => col.key === modalState.field && col.type === 'modal')
    : null;

  // Early return if no columns provided
  if (!columns || columns.length === 0) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">No columns defined for import</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className="cursor-pointer rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8 text-center transition-colors hover:border-gray-400 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-gray-500 dark:hover:bg-gray-700"
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {t("Drop files here or click to browse")}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t("Supports")} {allowedFileTypes.join(', ')} {t("files")}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* File Tabs */}
      {filesData.length > 0 && (
        <Tabs
          value={activeFileId ?? filesData[0].id}
          onValueChange={setActiveFileId}
          className="space-y-6"
        >
          <div className="flex items-center justify-between">
            <TabsList className="grid w-full grid-cols-1 lg:w-auto lg:grid-cols-none">
              {filesData.map((file) => (
                <TabsTrigger key={file.id} value={file.id} className="flex items-center gap-2">
                  <span className="truncate max-w-[150px]">{file.name}</span>
                  {file.isValid ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                </TabsTrigger>
              ))}
            </TabsList>

            {activeFile && (
              <div className="flex items-center gap-2">
                <Badge variant={activeFile.isValid ? "default" : "destructive"}>
                  {activeFile.validationErrors.length} {t("errors")}
                </Badge>
                <div className="flex items-center gap-1">
                  <Button
                    variant={viewMode === 'mapping' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('mapping')}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    {t("Mapping")}
                  </Button>
                  <Button
                    variant={viewMode === 'preview' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('preview')}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    {t("Preview")}
                  </Button>
                </div>
              </div>
            )}
          </div>

          {filesData.map((file) => (
            <TabsContent key={file.id} value={file.id} className="space-y-6">
              {viewMode === 'mapping' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      {t("Column Mapping")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {(columns || []).map((column) => {
                        const mapping = file.mappings.find(m => m.targetField === column.key);
                        const hasErrors = file.validationErrors.some(e => e.column === column.key);

                        return (
                          <div key={column.key} className={`space-y-2 p-3 rounded-lg border ${hasErrors ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950' : 'border-gray-200 dark:border-gray-700'}`}>
                            <div className="flex items-center gap-2">
                              <label className="text-sm font-medium">
                                {t(column.label)}
                              </label>
                              {column.required && <Badge variant="secondary" className="text-xs">Required</Badge>}
                              <Badge variant="outline" className="text-xs">{column.type}</Badge>
                            </div>

                            <Select
                              value={mapping?.sourceField || ''}
                              onValueChange={(value) => updateMapping(file.id, column.key, value || null)}
                            >
                              <SelectTrigger className={hasErrors ? 'border-red-300' : ''}>
                                <SelectValue placeholder={t("Select source column")} />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="">{t("-- No mapping --")}</SelectItem>
                                {file.sourceColumns.map((sourceCol) => (
                                  <SelectItem key={sourceCol} value={sourceCol}>
                                    {sourceCol}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>

                            {column.type === 'dropdown' && column.options && (
                              <Select
                                value={mapping?.defaultValue || ''}
                                onValueChange={(value) => updateMapping(file.id, column.key, mapping?.sourceField || null, value)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder={t("Default value")} />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="">{t("-- No default --")}</SelectItem>
                                  {column.options.map((option) => (
                                    <SelectItem key={option} value={option}>
                                      {t(option)}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}

                            {column.type !== 'dropdown' && (
                              <Input
                                placeholder={t("Default value (optional)")}
                                value={mapping?.defaultValue || ''}
                                onChange={(e) => updateMapping(file.id, column.key, mapping?.sourceField || null, e.target.value)}
                              />
                            )}

                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full"
                              onClick={() => applyDefaultValue(file.id, column.key, mapping?.defaultValue || '')}
                              disabled={!mapping?.defaultValue}
                            >
                              {t("Apply to Empty Cells")}
                            </Button>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}

              {viewMode === 'preview' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      {t("Data Preview")}
                    </CardTitle>
                    <div className="flex items-center gap-4 mt-4">
                      <Input
                        placeholder={t("Search data...")}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-sm"
                      />
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">{t("Show columns")}:</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setVisibleColumns(new Set((columns || []).map(c => c.key)))}
                        >
                          {t("All")}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setVisibleColumns(new Set())}
                        >
                          {t("None")}
                        </Button>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {(columns || []).map((column) => (
                        <label key={column.key} className="flex items-center gap-2 text-sm cursor-pointer">
                          <input
                            type="checkbox"
                            checked={visibleColumns.has(column.key)}
                            onChange={(e) => {
                              const newVisible = new Set(visibleColumns);
                              if (e.target.checked) {
                                newVisible.add(column.key);
                              } else {
                                newVisible.delete(column.key);
                              }
                              setVisibleColumns(newVisible);
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="truncate max-w-[120px]" title={column.label}>
                            {t(column.label)}
                          </span>
                        </label>
                      ))}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-lg overflow-hidden">
                      <div className="h-[500px] w-full overflow-auto">
                        <div className="overflow-x-auto">
                          <table className="min-w-full table-auto border-collapse">
                            <thead className="sticky top-0 bg-gray-100 dark:bg-gray-700 z-10">
                              <tr>
                                {visibleColumnDefs.map((column) => (
                                  <th key={column.key} className="border px-3 py-2 text-left text-xs font-medium min-w-[120px] max-w-[200px]">
                                    <div className="flex items-center gap-2">
                                      <span className="truncate" title={column.label}>
                                        {t(column.label)}
                                      </span>
                                      <Badge variant="outline" className="text-xs">
                                        {column.type}
                                      </Badge>
                                      {column.required && (
                                        <span className="text-red-500">*</span>
                                      )}
                                    </div>
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {filteredData.map((row, rowIndex) => {
                                const hasRowErrors = file.validationErrors.some(e => e.row === rowIndex);

                                return (
                                  <tr key={rowIndex} className={`hover:bg-gray-50 dark:hover:bg-gray-800 ${hasRowErrors ? 'bg-red-50 dark:bg-red-950' : ''}`}>
                                    {visibleColumnDefs.map((column) => {
                                      const mapping = file.mappings.find(m => m.targetField === column.key);
                                      const sourceField = mapping?.sourceField;
                                      const cellValue = sourceField ? (row[sourceField] ?? "") : (row[column.key] ?? "");
                                      const hasError = file.validationErrors.some(e => e.row === rowIndex && e.column === column.key);

                                      return (
                                        <td
                                          key={`${rowIndex}-${column.key}`}
                                          className={`border px-2 py-1 min-w-[120px] max-w-[200px] ${hasError ? 'bg-red-100 dark:bg-red-900' : ''}`}
                                        >
                                          {column.type === 'date' ? (
                                            <DatePicker
                                              selected={cellValue ? new Date(cellValue) : null}
                                              onChange={(date) =>
                                                updateCellValue(
                                                  file.id,
                                                  rowIndex,
                                                  sourceField || column.key,
                                                  date ? date.toISOString() : "",
                                                )
                                              }
                                              className="w-full p-1 border rounded bg-white dark:bg-gray-800 text-xs"
                                              placeholderText={t("Select date")}
                                            />
                                          ) : column.type === 'dropdown' && column.options ? (
                                            <Select
                                              value={cellValue}
                                              onValueChange={(value) =>
                                                updateCellValue(
                                                  file.id,
                                                  rowIndex,
                                                  sourceField || column.key,
                                                  value,
                                                )
                                              }
                                            >
                                              <SelectTrigger className="h-8 text-xs">
                                                <SelectValue placeholder={t("Select")} />
                                              </SelectTrigger>
                                              <SelectContent>
                                                {column.options.map((option) => (
                                                  <SelectItem key={option} value={option}>
                                                    {t(option)}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                          ) : column.type === 'modal' && column.component ? (
                                            <Button
                                              variant="outline"
                                              size="sm"
                                              className="w-full text-xs"
                                              onClick={() =>
                                                setModalState({
                                                  fileId: file.id,
                                                  rowIndex,
                                                  field: column.key,
                                                  data: cellValue,
                                                })
                                              }
                                            >
                                              {cellValue ? t("Edit") : t("Add")}
                                            </Button>
                                          ) : (
                                            <Input
                                              className={`h-8 text-xs ${
                                                hasError
                                                  ? "border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-950"
                                                  : ""
                                              }`}
                                              placeholder={!sourceField ? t("Manual entry") : ""}
                                              value={cellValue}
                                              title={cellValue}
                                              onChange={(e) =>
                                                updateCellValue(
                                                  file.id,
                                                  rowIndex,
                                                  sourceField || column.key,
                                                  e.target.value,
                                                )
                                              }
                                            />
                                          )}
                                        </td>
                                      );
                                    })}
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <span>
                          {t("Showing")} {Math.min(filteredData.length, maxPreviewRows)} {t("of")} {file.rawData.length} {t("rows")}
                        </span>
                        {file.validationErrors.length > 0 && (
                          <Badge variant="destructive">
                            {file.validationErrors.length} {t("validation errors")}
                          </Badge>
                        )}
                      </div>

                      <Button
                        className="bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-700 dark:text-white dark:hover:bg-blue-800"
                        onClick={handleImport}
                        disabled={!file.isValid}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        {t("Import Data")} ({file.rawData.length} {t("rows")})
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          ))}
        </Tabs>
      )}

      {/* Modal Rendering */}
      {modalState && currentModalColumn && currentModalColumn.component && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-[90%] max-w-xl rounded-md bg-white p-4 dark:bg-gray-900">
            <currentModalColumn.component
              onSubmit={(data: any) => {
                updateCellValue(
                  modalState.fileId,
                  modalState.rowIndex,
                  modalState.field,
                  data,
                );
                setModalState(null);
              }}
              onClose={() => setModalState(null)}
              data={modalState.data}
            />
          </div>
        </div>
      )}
    </div>
  );
}

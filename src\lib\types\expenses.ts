import { EventDetails } from "../interfaces/finaces";
import apiClient from "../api/apiClient";

// This is a simplified version - you'll need to update it based on your actual API
const ExpensesServices = () => ({
  getExpenses: async (signal?: AbortSignal): Promise<EventDetails[]> => {
    try {
      console.log("Making API call to fetch expenses...");
      const response = await apiClient.get(`/expense/api/listall/`, {
        signal: signal
      });
      
      if (!response.data) {
        console.error("Expense API response has no data property");
        return [];
      }
      
      // Check for 'expenses' (plural) first, then fall back to 'expense' (singular)
      if (response.data.expenses) {
        return response.data.expenses as EventDetails[];
      } else if (response.data.expense) {
        return response.data.expense as EventDetails[];
      }
      
      // If neither property exists, check if response.data is an array directly
      if (Array.isArray(response.data)) {
        return response.data;
      }
      
      console.error("Could not find expense data in the response:", response.data);
      return [];
    } catch (error) {
      if (error instanceof Error && (error.name === 'AbortError' || (error as any).code === 'ECONNABORTED')) {
        console.log('Expenses API request was cancelled');
        return [];
      }
      console.error("Error fetching expenses:", error);
      throw error;
    }
  },
});

export default ExpensesServices;

import { FC, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/solid';

interface ErrorPopupProps {
  message: string;
  onClose: () => void;
  duration?: number;
}

export const ErrorPopup: FC<ErrorPopupProps> = ({ 
  message, 
  onClose, 
  duration = 5000 
}) => {
  useEffect(() => {
    const timer = setTimeout(onClose, duration);
    return () => clearTimeout(timer);
  }, [duration, onClose]);

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="flex items-center bg-red-50 border-l-4 border-red-500 rounded-lg p-4 shadow-lg animate-fade-in-up">
        <div className="flex-shrink-0">
          <XMarkIcon className="h-5 w-5 text-red-500" />
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-red-800">{message}</p>
        </div>
        <button
          onClick={onClose}
          className="ml-auto -mx-1.5 -my-1.5 p-1.5 rounded-lg inline-flex items-center justify-center hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400"
        >
          <XMarkIcon className="h-5 w-5 text-red-500" />
        </button>
      </div>
    </div>
  );
};
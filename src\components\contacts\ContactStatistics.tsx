"use client";

import React, { useState, useMemo } from 'react';
import { 
  Card, CardHeader, Card<PERSON><PERSON>le, CardContent, CardDescription 
} from '@/components/cards/card';
import { BarChart3 } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from '@/components/ui/button';
import { Separator } from "@/components/ui/separator";
import useLanguage from "@/hooks/useLanguage";
import { DateRange } from "react-day-picker";

// Import Recharts for shared types and active shape renderer
import { Sector } from 'recharts';

// Import modular components
import ContactStatisticsOverview from './statistics/ContactStatisticsOverview';
import ContactRevenueDashboard from './statistics/ContactRevenueDashboard';
import ContactReservationsDashboard from './statistics/ContactReservationsDashboard';
import ContactLocationsDashboard from './statistics/ContactLocationsDashboard';
import ContactPaymentsDashboard from './statistics/ContactPaymentsDashboard';

// Define interfaces for the component
interface ContactStatisticsProps {
  contactId: string;
  contactName?: string;
}

// Data interfaces for the statistics
interface RevenueData {
  period: string;
  revenue: number;
  projectedRevenue: number;
  lastYearRevenue: number;
}

interface ReservationData {
  period: string;
  activeCount: number;
  completedCount: number;
  totalValue: number;
}

interface LocationData {
  name: string;
  reservations: number;
  revenue: number;
  percentage: number;
}

interface AdTypeData {
  type: string;
  count: number;
  revenue: number;
  percentage: number;
}

const ContactStatistics: React.FC<ContactStatisticsProps> = ({ contactId, contactName }) => {
  const { t } = useLanguage();
  
  // State hooks for filtering and view options
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1),
    to: new Date()
  });
  const [activeTab, setActiveTab] = useState("revenue");
  const [timeframe, setTimeframe] = useState("monthly");
  const [activePieIndex, setActivePieIndex] = useState(0);
  
  // Mock data generation - in a real app, this would be API calls
  const revenueData = useMemo(() => {
    // Generate 12 months of data with reasonable patterns
    const data: RevenueData[] = [];
    const today = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthName = month.toLocaleString('default', { month: 'short' });
      
      // Create realistic revenue patterns
      const baseRevenue = 5000 + Math.random() * 3000;
      // Higher revenue in Q4 (Oct-Dec) and Q2 (Apr-Jun)
      const monthFactor = month.getMonth();
      let seasonalMultiplier = 1;
      
      // Q4 boost
      if (monthFactor >= 9 && monthFactor <= 11) {
        seasonalMultiplier = 1.4;
      }
      // Q2 boost
      else if (monthFactor >= 3 && monthFactor <= 5) {
        seasonalMultiplier = 1.2;
      }
      // Q1 and Q3 lower
      else {
        seasonalMultiplier = 0.9;
      }
      
      const revenue = Math.round(baseRevenue * seasonalMultiplier);
      const lastYearRevenue = Math.round(revenue * (0.8 + Math.random() * 0.1));
      const projectedRevenue = i < 2 ? Math.round(revenue * 1.15) : revenue;
      
      data.push({
        period: `${monthName} ${month.getFullYear()}`,
        revenue: revenue,
        projectedRevenue: projectedRevenue,
        lastYearRevenue: lastYearRevenue
      });
    }
    
    return data;
  }, []);

  const reservationData = useMemo(() => {
    // Generate reservation data with reasonable patterns
    const data: ReservationData[] = [];
    const today = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthName = month.toLocaleString('default', { month: 'short' });
      
      // Create realistic reservation patterns
      const baseActive = Math.round(2 + Math.random() * 3);
      const baseCompleted = Math.round(3 + Math.random() * 5);
      const monthFactor = month.getMonth();
      
      // More activity in certain months
      let activeMultiplier = 1;
      let completedMultiplier = 1;
      
      // More activity in Q4 and Q2
      if (monthFactor >= 9 && monthFactor <= 11) {
        activeMultiplier = 1.3;
        completedMultiplier = 1.5;
      } else if (monthFactor >= 3 && monthFactor <= 5) {
        activeMultiplier = 1.2;
        completedMultiplier = 1.3;
      }
      
      const activeCount = Math.round(baseActive * activeMultiplier);
      const completedCount = Math.round(baseCompleted * completedMultiplier);
      const avgReservationValue = 2000 + Math.random() * 1000;
      const totalValue = Math.round((activeCount + completedCount) * avgReservationValue);
      
      data.push({
        period: `${monthName} ${month.getFullYear()}`,
        activeCount,
        completedCount,
        totalValue
      });
    }
    
    return data;
  }, []);

  const locationData = useMemo(() => {
    // Sample location preference data
    return [
      { name: "Downtown Billboard", reservations: 12, revenue: 48000, percentage: 32 },
      { name: "Shopping District", reservations: 8, revenue: 35000, percentage: 23 },
      { name: "Airport Digital", reservations: 7, revenue: 28000, percentage: 19 },
      { name: "Business Park", reservations: 6, revenue: 24000, percentage: 16 },
      { name: "Residential Area", reservations: 4, revenue: 15000, percentage: 10 }
    ];
  }, []);

  const adTypeData = useMemo(() => {
    // Sample ad type preference data
    return [
      { type: "Billboard", count: 15, revenue: 60000, percentage: 40 },
      { type: "Digital Display", count: 12, revenue: 45000, percentage: 30 },
      { type: "Street Banner", count: 8, revenue: 24000, percentage: 16 },
      { type: "Bus Stop", count: 6, revenue: 18000, percentage: 12 },
      { type: "Interactive", count: 2, revenue: 6000, percentage: 4 }
    ];
  }, []);

  // Calculate aggregate metrics
  const totalRevenue = useMemo(() => 
    revenueData.reduce((sum, item) => sum + item.revenue, 0), 
  [revenueData]);

  const totalReservations = useMemo(() => 
    reservationData.reduce((sum, item) => sum + item.activeCount + item.completedCount, 0), 
  [reservationData]);

  const activeReservations = useMemo(() => 
    reservationData[reservationData.length - 1]?.activeCount || 0, 
  [reservationData]);

  const revenueGrowth = useMemo(() => {
    if (revenueData.length < 2) return 0;
    const currentRevenue = revenueData[revenueData.length - 1].revenue;
    const previousRevenue = revenueData[revenueData.length - 2].revenue;
    return previousRevenue !== 0 ? Math.round((currentRevenue - previousRevenue) / previousRevenue * 100) : 0;
  }, [revenueData]);

  // Format chart data
  const locationPieData = useMemo(() => 
    locationData.map(loc => ({
      name: loc.name,
      value: loc.revenue
    })),
  [locationData]);
  
  const adTypePieData = useMemo(() => 
    adTypeData.map(ad => ({
      name: ad.type,
      value: ad.revenue
    })),
  [adTypeData]);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#e06666', '#66e066', '#6681e0'];

  // Weekly data generation for charts
  const weeklyRevenueData = useMemo(() => {
    const data: RevenueData[] = [];
    
    // Generate weekly data from the last 3 months of revenueData
    revenueData.slice(-3).forEach(monthData => {
      const [month, year] = monthData.period.split(' ');
      for (let week = 1; week <= 4; week++) {
        const weekFactor = week === 3 ? 1.2 : week === 2 ? 1.1 : week === 4 ? 0.9 : 0.8;
        
        const weeklyRevenue = Math.round(monthData.revenue / 4 * weekFactor);
        const weeklyProjected = Math.round(monthData.projectedRevenue / 4 * weekFactor);
        const weeklyLastYear = Math.round(monthData.lastYearRevenue / 4 * weekFactor);
        
        data.push({
          period: `${month} W${week}`,
          revenue: weeklyRevenue,
          projectedRevenue: weeklyProjected,
          lastYearRevenue: weeklyLastYear
        });
      }
    });
    
    return data;
  }, [revenueData]);

  // Get data based on selected timeframe
  const getRevenueDataByTimeframe = () => {
    switch(timeframe) {
      case "weekly": return weeklyRevenueData;
      default: return revenueData;
    }
  };

  // Create an interactive tooltip for pie charts
  const renderActiveShape = (props: any) => {
    const RADIAN = Math.PI / 180;
    const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle,
      fill, payload, percent, value } = props;
    const sin = Math.sin(-RADIAN * midAngle);
    const cos = Math.cos(-RADIAN * midAngle);
    const sx = cx + (outerRadius + 10) * cos;
    const sy = cy + (outerRadius + 10) * sin;
    const mx = cx + (outerRadius + 30) * cos;
    const my = cy + (outerRadius + 30) * sin;
    const ex = mx + (cos >= 0 ? 1 : -1) * 22;
    const ey = my;
    const textAnchor = cos >= 0 ? 'start' : 'end';
  
    return (
      <g>
        <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill}>
          {payload.name}
        </text>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
        <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
        <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
        <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#333">{`$${value.toLocaleString()}`}</text>
        <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={18} textAnchor={textAnchor} fill="#999">
          {`(${(percent * 100).toFixed(2)}%)`}
        </text>
      </g>
    );
  };

  const paymentData = useMemo(() => {
    // Generate payment history data
    const data = [];
    const today = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthName = month.toLocaleString('default', { month: 'short' });
      
      // Create realistic payment patterns
      const baseOnTime = Math.round(3 + Math.random() * 2);
      const baseLate = Math.round(Math.random() * 2);
      const baseOutstanding = i < 3 ? Math.round(Math.random() * 1) : 0; // Only recent months have outstanding
      
      data.push({
        period: `${monthName} ${month.getFullYear()}`,
        onTime: baseOnTime,
        late: baseLate,
        outstanding: baseOutstanding
      });
    }
    
    return data;
  }, []);
  
  // Add reservation trend data
  const reservationTrendData = useMemo(() => {
    // Transform reservation data into trend visualization format
    return reservationData.map(item => ({
      period: item.period,
      active: item.activeCount,
      completed: item.completedCount,
      total: item.activeCount + item.completedCount
    }));
  }, [reservationData]);
  
  // Calculate reservation status distribution
  const reservationStatusData = useMemo(() => {
    const active = reservationData.reduce((sum, item) => sum + item.activeCount, 0);
    const completed = reservationData.reduce((sum, item) => sum + item.completedCount, 0);
    const total = active + completed;
    
    return [
      { name: t("active"), value: active, percentage: Math.round((active / total) * 100) },
      { name: t("completed"), value: completed, percentage: Math.round((completed / total) * 100) }
    ];
  }, [reservationData, t]);
  
  // Generate reservation by location data
  const reservationsByLocation = useMemo(() => {
    return locationData.map(location => ({
      name: location.name,
      reservations: location.reservations,
      percentage: Math.round((location.reservations / totalReservations) * 100)
    })).sort((a, b) => b.reservations - a.reservations);
  }, [locationData, totalReservations]);
  
  // Generate average revenue per location
  const revenuePerLocationData = useMemo(() => {
    return locationData.map(location => ({
      name: location.name,
      revenue: Math.round(location.revenue / location.reservations)
    })).sort((a, b) => b.revenue - a.revenue);
  }, [locationData]);
  
  // Generate individual payment transaction data
  const paymentTransactions = useMemo(() => {
    // Create a list of payment transactions from payment data
    const transactions = [];
    
    // Current year and month for reference
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    // Generate payment transactions across the time period
    for (let i = 0; i < paymentData.length; i++) {
      const month = paymentData[i];
      
      // Generate on-time payment records
      for (let j = 0; j < month.onTime; j++) {
        // Create a date within that month
        const paymentDate = new Date(currentYear, currentMonth - (paymentData.length - 1 - i), 
          Math.floor(Math.random() * 28) + 1);
        
        transactions.push({
          id: `pmt-${transactions.length + 1}`,
          date: paymentDate,
          amount: 2000 + Math.floor(Math.random() * 3000),
          status: 'paid',
          method: ['credit_card', 'bank_transfer', 'check'][Math.floor(Math.random() * 3)],
          description: `Payment for ${locationData[Math.floor(Math.random() * locationData.length)].name}`,
          dueDate: new Date(paymentDate.getTime() - 1000 * 60 * 60 * 24 * 3) // 3 days before payment
        });
      }
      
      // Generate late payment records
      for (let j = 0; j < month.late; j++) {
        // Create a date within that month
        const dueDate = new Date(currentYear, currentMonth - (paymentData.length - 1 - i), 
          Math.floor(Math.random() * 14) + 1);
        const paymentDate = new Date(dueDate.getTime() + 1000 * 60 * 60 * 24 * (Math.floor(Math.random() * 10) + 3));
        
        transactions.push({
          id: `pmt-${transactions.length + 1}`,
          date: paymentDate,
          amount: 2000 + Math.floor(Math.random() * 3000),
          status: 'paid-late',
          method: ['credit_card', 'bank_transfer', 'check'][Math.floor(Math.random() * 3)],
          description: `Late payment for ${locationData[Math.floor(Math.random() * locationData.length)].name}`,
          dueDate: dueDate
        });
      }
      
      // Generate outstanding payment records for recent months
      for (let j = 0; j < month.outstanding; j++) {
        // Create a due date within that month
        const dueDate = new Date(currentYear, currentMonth - (paymentData.length - 1 - i), 
          Math.floor(Math.random() * 28) + 1);
        
        transactions.push({
          id: `pmt-${transactions.length + 1}`,
          date: null,
          amount: 2000 + Math.floor(Math.random() * 3000),
          status: 'pending',
          method: null,
          description: `Pending payment for ${locationData[Math.floor(Math.random() * locationData.length)].name}`,
          dueDate: dueDate
        });
      }
    }
    
    // Sort transactions by date (newest first)
    return transactions.sort((a, b) => {
      // Handle null dates (pending payments)
      if (!a.date && !b.date) return 0;
      if (!a.date) return -1; // Pending payments at the top
      if (!b.date) return 1;
      
      return b.date.getTime() - a.date.getTime();
    });
  }, [paymentData, locationData]);

  // Calculate payment metrics
  const paymentMetrics = useMemo(() => {
    const totalPaid = paymentData.reduce((sum, item) => sum + item.onTime + item.late, 0);
    const totalOnTime = paymentData.reduce((sum, item) => sum + item.onTime, 0);
    const totalLate = paymentData.reduce((sum, item) => sum + item.late, 0);
    const totalOutstanding = paymentData.reduce((sum, item) => sum + item.outstanding, 0);
    
    const onTimeRate = totalPaid > 0 ? Math.round((totalOnTime / totalPaid) * 100) : 0;
    
    // Calculate total amount paid and pending
    const paidAmount = paymentTransactions
      .filter(t => t.status === 'paid' || t.status === 'paid-late')
      .reduce((sum, t) => sum + t.amount, 0);
      
    const pendingAmount = paymentTransactions
      .filter(t => t.status === 'pending')
      .reduce((sum, t) => sum + t.amount, 0);
    
    return {
      totalPaid,
      totalOnTime,
      totalLate,
      totalOutstanding,
      onTimeRate,
      paidAmount,
      pendingAmount,
      totalTransactions: paymentTransactions.length
    };
  }, [paymentData, paymentTransactions]);
  
  // Payment status data for pie chart
  const paymentStatusData = useMemo(() => {
    return [
      { name: t("onTime"), value: paymentMetrics.totalOnTime, color: '#10b981' },
      { name: t("late"), value: paymentMetrics.totalLate, color: '#f59e0b' },
      { name: t("pending"), value: paymentMetrics.totalOutstanding, color: '#3b82f6' }
    ];
  }, [paymentMetrics, t]);
  
  // Get payment method distribution
  const paymentMethodData = useMemo(() => {
    const methods: Record<string, number> = {};
    
    paymentTransactions
      .filter(t => t.status === 'paid' || t.status === 'paid-late')
      .forEach(transaction => {
        if (transaction.method) {
          methods[transaction.method] = (methods[transaction.method] || 0) + 1;
        }
      });
      
    return Object.entries(methods).map(([method, count]) => ({
      name: method === 'credit_card' ? t("creditCard") : 
            method === 'bank_transfer' ? t("bankTransfer") : 
            method === 'check' ? t("check") : method,
      value: count
    }));
  }, [paymentTransactions, t]);

  // Now let's calculate the average reservation value
  const avgReservationValue = useMemo(() => 
    Math.round(totalRevenue / totalReservations),
  [totalRevenue, totalReservations]);
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="text-xl md:text-2xl font-bold flex items-center gap-2">
              <BarChart3 className="h-5 w-5 md:h-6 md:w-6" />
              {contactName ? `${contactName} - ${t("statistics")}` : t("contactStatistics")}
            </CardTitle>
            
            <div className="flex flex-wrap gap-3 items-center">
              <Select
                value={timeframe}
                onValueChange={setTimeframe}
              >
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder={t("timeframe")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="weekly">{t("weekly")}</SelectItem>
                  <SelectItem value="monthly">{t("monthly")}</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="sm" className="gap-1">
                <span>{t("filters")}</span>
              </Button>
            </div>
          </div>
          
          <CardDescription className="mt-2">
            {t("statisticsForContact")}
          </CardDescription>
        </CardHeader>
        
        <Separator className="mb-4" />
        
        <CardContent className="p-4 sm:p-6 pt-0 space-y-8">
          {/* Overview Cards using the ContactStatisticsOverview component */}
          <ContactStatisticsOverview
            totalRevenue={totalRevenue}
            revenueGrowth={revenueGrowth}
            totalReservations={totalReservations}
            activeReservations={activeReservations}
            locationData={locationData.map(loc => ({ name: loc.name, percentage: loc.percentage }))}
            adTypeData={adTypeData.map(ad => ({ type: ad.type, percentage: ad.percentage }))}
          />
          
          {/* Tabs Navigation */}
          <Tabs 
            defaultValue="revenue" 
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="revenue">{t("revenue")}</TabsTrigger>
              <TabsTrigger value="reservations">{t("reservations")}</TabsTrigger>
              <TabsTrigger value="locations">{t("locations")}</TabsTrigger>
              <TabsTrigger value="payments">{t("paymentHistory")}</TabsTrigger>
            </TabsList>
            
            {/* Tab Content using modular components */}
            <TabsContent value="revenue">
              <ContactRevenueDashboard
                revenueData={revenueData}
                locationPieData={locationPieData}
                adTypePieData={adTypePieData}
                locationData={locationData}
                totalRevenue={totalRevenue}
                revenueGrowth={revenueGrowth}
                renderActiveShape={renderActiveShape}
                COLORS={COLORS}
                getRevenueDataByTimeframe={getRevenueDataByTimeframe}
              />
            </TabsContent>
            
            <TabsContent value="reservations">
              <ContactReservationsDashboard
                reservationTrendData={reservationTrendData}
                reservationStatusData={reservationStatusData}
                revenuePerLocationData={revenuePerLocationData}
                totalReservations={totalReservations}
                activeReservations={activeReservations}
                avgReservationValue={avgReservationValue}
                COLORS={COLORS}
              />
            </TabsContent>
            
            <TabsContent value="locations">
              <ContactLocationsDashboard
                reservationsByLocation={reservationsByLocation}
                COLORS={COLORS}
                locationData={locationData}
              />
            </TabsContent>
            
            <TabsContent value="payments">
              <ContactPaymentsDashboard
                paymentData={paymentData}
                paymentStatusData={paymentStatusData}
                paymentMethodData={paymentMethodData}
                paymentTransactions={paymentTransactions}
                paymentMetrics={paymentMetrics}
                COLORS={COLORS}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContactStatistics;

import React, { useState } from "react";
import { FaCalendarAlt, FaMoneyBillAlt } from "react-icons/fa";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";
import UpcomingEventPopup from "@/components/Financials/common/UpcommingEventPopup.tsx";
import { formatCurrency } from "@/utils/analyticsUtils";

interface UpcomingEventsSectionProps {
  events: EventDetails[];
  filterStatus: string;
  filterPriority: string;
  onFilterStatusChange: (status: string) => void;
  onFilterPriorityChange: (priority: string) => void;
  onSave: (updatedEvent: EventDetails) => void; // Add this prop for saving changes
}

const UpcomingEventsSection: React.FC<UpcomingEventsSectionProps> = ({
  events,
  filterStatus,
  filterPriority,
  onFilterStatusChange,
  onFilterPriorityChange,
  onSave,
}) => {
    const { t, language } = useLanguage();
    const [selectedEvent, setSelectedEvent] = useState<EventDetails | null>(null);

  const filteredEvents = events.filter((event) => {
    const statusMatch = filterStatus === "all" || event.status === filterStatus;
    const priorityMatch =
      filterPriority === "all" || event.priority === filterPriority;
    return statusMatch && priorityMatch;
  });

  // Show only the first 4 events
  const displayedEvents = filteredEvents.slice(0, 4);

  const handleEventClick = (event: EventDetails) => {
    const page =
      event.category === "income" ? "/finances/income" : "/finances/expenses";
    window.location.href = `${page}?eventId=${event.id}`;
  };

  return (
    <div className="rounded-lg border bg-white p-3 shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:p-4 lg:p-6">
      <h2 className="mb-4 text-center text-lg font-semibold dark:text-white sm:mb-6 sm:text-xl">
        {t("upcoming events")}
      </h2>
      <div className="space-y-3 sm:space-y-4">
        {displayedEvents.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
              <FaCalendarAlt className="h-6 w-6 text-gray-400" />
                {displayedEvents.map((event) => {
                    const amountStyle = event.category === "income"
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400";

                    return (
                        <div
                            key={event.id}
                            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow cursor-pointer"
                            onClick={() => handleEventClick(event)}
                        >
                            <div className="flex flex-col gap-2">
                                {/* Event Name */}
                                <h2 className="text-sm font-semibold dark:text-white">
                                    {event.title}
                                </h2>

                                {/* Event Date and Amount */}
                                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                                    <div className="flex items-center gap-2">
                                        <FaCalendarAlt className="w-4 h-4" />
                                        <span>{new Date(event.dueDate).toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US')}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <FaMoneyBillAlt className={amountStyle} />
                                        <span className={amountStyle}>{formatCurrency(Number(event.amount) || 0, language)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 sm:text-base">
              {t("No upcoming events")}
            </p>
          </div>
        ) : (
          displayedEvents.map((event) => {
            const amountStyle =
              event.category === "income"
                ? "text-green-600 dark:text-green-400"
                : "text-red-600 dark:text-red-400";

            return (
              <div
                key={event.id}
                className="cursor-pointer rounded-lg border border-gray-200 bg-gray-50 p-3 transition-all duration-200 hover:bg-gray-100 hover:shadow-md dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600 sm:p-4"
                onClick={() => handleEventClick(event)}
              >
                <div className="flex flex-col gap-2 sm:gap-3">
                  {/* Event Name */}
                  <h3
                    className="truncate text-sm font-semibold dark:text-white sm:text-base"
                    title={event.title}
                  >
                    {event.title}
                  </h3>

                  {/* Event Date and Amount */}
                  <div className="flex flex-col gap-2 text-xs text-gray-600 dark:text-gray-400 sm:flex-row sm:items-center sm:justify-between sm:text-sm">
                    <div className="flex items-center gap-2">
                      <FaCalendarAlt className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span>
                        {new Date(event.dueDate).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FaMoneyBillAlt
                        className={`h-3 w-3 sm:h-4 sm:w-4 ${amountStyle}`}
                      />
                      <span className={`font-medium ${amountStyle}`}>
                        ${event.amount}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
      {/* View All Button */}
      {filteredEvents.length > 4 && (
        <a href="/finances" className="block w-full">
          <button className="mt-3 w-full rounded-lg bg-blue-500 px-4 py-2 text-center text-sm font-medium text-white shadow transition-colors hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-4 sm:py-2.5 sm:text-base">
            {t("view all")}
          </button>
        </a>
      )}

      {/* Event Popup */}
      {selectedEvent && (
        <UpcomingEventPopup
          event={selectedEvent}
          onClose={() => setSelectedEvent(null)}
          onSave={onSave} // Pass the onSave function to handle updates
        />
      )}
    </div>
  );
};

export default UpcomingEventsSection;

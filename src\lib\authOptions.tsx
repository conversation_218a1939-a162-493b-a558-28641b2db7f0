import NextAuth, { AuthOptions } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { authService } from "@/lib/api/auth";

declare module "next-auth" {
    interface Session {
      accessToken?: string;
      refreshToken?: string;
      accessTokenExpiry?: number;
      refreshTokenExpiry?: number;
      error?: string;
      permissions?: any;
    }
  }
  
  export const authOptions: AuthOptions = {
    secret: process.env.NEXTAUTH_SECRET,
    providers: [
      CredentialsProvider({
        name: "Credentials",
        credentials: {
          email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
          password: { label: "Password", type: "password" },
        },
        async authorize(credentials) {
          if (!credentials?.email || !credentials?.password) {
            throw new Error("Both email and password are required");
          }
  
          try {
            const { user, accessToken, refreshToken, access_exp, refresh_exp, permissions } = 
              await authService.login(credentials.email, credentials.password);
  
            if (user && accessToken && refreshToken) {
              return {
                id: user.id,
                email: user.email,
                role: user.role,
                token: accessToken,
                refreshToken: refreshToken,
                permissions: permissions,
                accessTokenExpiry: Date.now() + access_exp * 1000, 
                refreshTokenExpiry: Date.now() + refresh_exp * 1000,
              };
            } else {
              throw new Error("Invalid API response");
            }
          } catch (error) {
            console.error("Error during authorization:", error);
            throw new Error("Invalid credentials");
          }
        },
      }),
    ],
    callbacks: {
      async jwt({ token, user, trigger }: { token: any; user?: any; trigger?: string }) {
        // Initial login
        if (user) {
          return {
            id: user.id,
            email: user.email,
            accessToken: user.token,
            refreshToken: user.refreshToken,
            accessTokenExpiry: user.accessTokenExpiry,
            refreshTokenExpiry: user.refreshTokenExpiry,
            role: user.role,
            permissions: user.permissions,
          };
        }
  
        if (Date.now() > token.accessTokenExpiry) {
          console.log("Access token expired, refreshing...");
          try {
            const response = await authService.refreshToken(token.refreshToken);
            return {
              ...token,
              
              accessToken: response.accessToken,
              refreshToken: response.refreshToken || token.refreshToken,
              accessTokenExpiry: Date.now() + response.access_exp * 1000, 
              permissions: response.permission,
            };
          } catch (error) {
            console.error("Refresh token error:", error);
            return { ...token, error: "RefreshTokenError" };
          }
        }
  
        return token;
      },
  
      async session({ session, token }) {
  
        session.user = { ...session.user, role: token.role as "ADMIN" | "USER" | undefined };
        session.accessToken = token.accessToken as string | undefined;
        session.refreshToken = token.refreshToken as string | undefined;
        session.error = token.error as string | undefined;
        session.permissions = token.permissions as Permissions;
        return session;
      },
    },
    session: {
      strategy: "jwt",
    },
    pages: {
      signIn: "/auth/signin",
    },
  };
  
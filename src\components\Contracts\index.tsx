"use client";
import React, { use, useState , useEffect } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import ContractList from "./ContractList";
import ContractDetails from "./ContractDetails/index";
import ContractForm from "./ContractForm/index";
import { Contract } from "@/lib/interfaces/contract";
import useLanguage from "@/hooks/useLanguage";
import { PlusCircle, FileText } from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import ContractServices from "@/lib/contracts";
import { LoadingComp } from "../common/Loading";
import { Card, CardContent } from "../cards/card";

const ContractsOverview: React.FC = () => {
  const { t } = useLanguage();
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isViewingDetails, setIsViewingDetails] = useState(false);
  const [loading, setLoading] = useState(true);
  const { hasPermission, permissionsLoaded } = usePermissions();
  const { getContracts } = ContractServices();

  useEffect(() => {
    const fetchContracts = async () => {
      setLoading(true);
      try {
        const result = await getContracts();
        console.log("Fetched contracts1111:", result);
        setContracts(result || []);
      } catch (err) {
        console.error("Failed to fetch contracts:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchContracts();
  },
   []);

  // Handle contract click to view details
  const handleContractClick = (id: string, contract?: Contract) => {
    console.log("Received contract click for ID:", id);
    
    if (contract) {
      // If the contract object was passed directly, use it
      console.log("Using directly passed contract object");
      setSelectedContract(contract);
      setIsViewingDetails(true);
      setIsCreating(false);
    } else {
      // Fallback to finding by ID if object wasn't passed
      console.log("Falling back to find contract by ID");
      const foundContract = contracts.find((c) => c.id === id);
      if (foundContract) {
        setSelectedContract(foundContract);
        setIsViewingDetails(true);
        setIsCreating(false);
      } else {
        console.error("Contract not found for ID:", id);
      }
    }
    
    // Scroll to the top of the page to show the details
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Close details view
  const handleCloseDetails = () => {
    setIsViewingDetails(false);
    setSelectedContract(null);
  };

  // Delete a contract
  const handleDeleteContract = (id: string) => {
    setContracts(contracts.filter((contract) => contract.id !== id));
    if (selectedContract?.id === id) {
      setSelectedContract(null);
      setIsViewingDetails(false);
    }
  };

  // Handle creating new contract
  const handleCreateContract = (newContract: Contract) => {
    setContracts([...contracts, newContract]);
    setIsCreating(false);
  };

  // Handle updating contract
  const handleUpdateContract = (updatedContract: Contract) => {
    setContracts(
      contracts.map((contract) =>
        contract.id === updatedContract.id ? updatedContract : contract
      )
    );
    setIsViewingDetails(false);
    setSelectedContract(null);
  };

  // Check permissions loading state
  if (!permissionsLoaded) {
    return (
      <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }

  // Check if user has view permission for contracts
  if (!hasPermission("contracts", "view")) {
    return (
      <>
        <Breadcrumb pageName={t("contracts")} />
        <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
          <div className="flex items-center justify-center h-full">
            <Card className="w-full max-w-md">
              <CardContent className="text-center py-12">
                <FileText size={48} className="mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  {t("accessDenied")}
                </h2>
                <p className="text-gray-500 dark:text-gray-400">
                  {t("noPermissionToViewContracts")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Breadcrumb pageName={t("contracts")} />

      <div className="flex flex-col gap-6">
        {!isViewingDetails && !isCreating && (
          <div className="flex justify-between items-center mb-4 px-2">
            <h1 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-white">
              {t("propertyContracts")}
            </h1>
            {hasPermission("contracts", "create") && (
              <button
                onClick={() => setIsCreating(true)}
                className="px-3 py-2 sm:px-4 sm:py-2 bg-primary text-white rounded-lg flex items-center gap-2 hover:bg-primary-dark transition-colors"
              >
                <PlusCircle size={18} />
                <span className="hidden sm:inline">{t("newContract")}</span>
                <span className="sm:hidden">{t("new")}</span>
              </button>
            )}
          </div>
        )}

        {isCreating && hasPermission("contracts", "create") ? (
          <ContractForm
            onCancel={() => setIsCreating(false)}
          />
        ) : isViewingDetails && selectedContract ? (
          <ContractDetails
            contract={selectedContract}
            onClose={handleCloseDetails}
            onDelete={() => handleDeleteContract(selectedContract.id)}
            onUpdate={handleUpdateContract}
            canEdit={hasPermission("contracts", "edit")}
            canDelete={hasPermission("contracts", "delete")}
            canExport={hasPermission("contracts", "export")}
          />
        ) : (
          <ContractList
            contracts={contracts}
            loading={loading}
            onContractClick={handleContractClick}
            onDeleteContract={handleDeleteContract}
            canEdit={hasPermission("contracts", "edit")}
            canDelete={hasPermission("contracts", "delete")}
          />
        )}
      </div>
    </>
  );
};

export default ContractsOverview;

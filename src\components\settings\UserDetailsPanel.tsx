import React, { useState } from "react";
import { UserDetails, UserPermissions } from "@/lib/interfaces/userDetails";
import { X, Calendar, Mail, User as UserIcon, Clock, Shield, ChevronDown, ChevronUp, Edit2, Trash2 } from "lucide-react";
import Image from "next/image";
import useLanguage from "@/hooks/useLanguage";

interface UserDetailsPanelProps {
  user: UserDetails;
  onClose: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

const UserDetailsPanel: React.FC<UserDetailsPanelProps> = ({ user, onClose, onEdit, onDelete }) => {
  const { t } = useLanguage();
  const [permissionsSectionOpen, setPermissionsSectionOpen] = useState(true);

  // Count active permissions
  const countActivePermissions = () => {
    if (!user?.permissions) return { 
      total: 0,
      possible: 0,
      byModule: {}
    };
    
    let totalActive = 0;
    let totalPossible = 0;
    const byModule: Record<string, {active: number, total: number}> = {};

    Object.entries(user?.permissions).forEach(([module, permissions]) => {
      const active = Object.values(permissions).filter(Boolean).length;
      const total = Object.values(permissions).length;
      
      byModule[module] = { active, total };
      totalActive += active;
      totalPossible += total;
    });
    
    return {
      total: totalActive,
      possible: totalPossible,
      byModule
    };
  };
  
  const permissions = countActivePermissions();
  const isAdmin = user?.role === 'ADMIN';
  
  const formatDate = (dateString: string) => {
    if (!dateString) return t("never") || "Never";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-4 sm:p-6 border-b border-gray-200">
      <div className="flex flex-col sm:flex-row justify-between items-start gap-4 sm:gap-0">
        <div className="flex flex-col sm:flex-row items-start gap-4 sm:gap-6 w-full sm:w-auto">


          {/* User Details */}
          <div className="space-y-3 sm:space-y-4 flex-grow">
            <div>
              <div className="flex flex-wrap items-center gap-2">
                <h3 className="text-xl sm:text-2xl font-bold text-gray-800">
                  {user?.first_name} {user?.last_name}
                </h3>
                {isAdmin && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    <Shield size={12} className="mr-1" /> {t("administrator")}
                  </span>
                )}
          
              </div>
              <p className="text-gray-600">@{user?.username}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-4">
              <div className="flex items-center space-x-2">
                <Mail size={18} className="text-primary" />
                <span className="text-gray-700 text-sm sm:text-base truncate">{user?.email}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar size={18} className="text-primary" />
                <span className="text-gray-700 text-sm sm:text-base">
                  {t("joined")}: {formatDate(user?.created_at)}
                </span>
              </div>
              
            </div>
          </div>
        </div>
        
        {/* Close Button */}
        <button 
          onClick={onClose}
          className="p-2 hover:bg-gray-200 rounded-full transition-colors absolute top-4 right-4 sm:static"
          aria-label={t("closeUserDetails")}
        >
          <X size={20} className="text-gray-600" />
        </button>
      </div>

      {/* Permissions Summary Card */}
      {user?.permissions && (
        <div className="mt-6">
          <div className="bg-white p-4 rounded-lg shadow-md border border-gray-200">
            <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2 sm:gap-0">
              <div className="flex items-center">
                <Shield size={22} className="text-primary mr-2" />
                <h4 className="font-medium text-gray-800">{t("userPermissions")}</h4>
              </div>
              <div className="bg-primary/10 px-3 py-1 rounded-full self-start sm:self-auto">
                <span className="text-sm font-medium text-primary">
                  {permissions.total} {t("of")} {permissions.possible} ({Math.round((permissions.total / permissions.possible) * 100)}%)
                </span>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="h-2 bg-gray-200 rounded-full mt-3 overflow-hidden">
              <div 
                className="h-2 bg-primary rounded-full" 
                style={{width: `${Math.round((permissions.total / permissions.possible) * 100)}%`}}
              />
            </div>

            <button 
              onClick={() => setPermissionsSectionOpen(!permissionsSectionOpen)}
              className="w-full mt-3 flex justify-center items-center text-sm text-gray-600 hover:text-gray-800"
            >
              {permissionsSectionOpen ? t("hideDetails") : t("showDetails")}
              {permissionsSectionOpen ? <ChevronUp size={16} className="ml-1" /> : <ChevronDown size={16} className="ml-1" />}
            </button>
          </div>
        </div>
      )}

      {/* Permissions Detailed Section */}
      {user?.permissions && permissionsSectionOpen && (
        <div className="mt-4">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(user?.permissions).map(([module, modulePermissions]) => (
                <div key={module} className="bg-gray-50 p-3 sm:p-4 rounded-lg border border-gray-200">
                  <h5 className="text-sm font-medium text-gray-700 mb-3 flex items-center justify-between">
                    <span className="capitalize">
                      {t(module)} 
                    </span>
                    <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                      {permissions.byModule[module]?.active || 0}/{permissions.byModule[module]?.total || 0}
                    </span>
                  </h5>
                  <div className="space-y-2 max-h-60 overflow-y-auto pr-1">
                    {Object.entries(modulePermissions).map(([permission, value]) => (
                      <div key={permission} className="flex items-center justify-between">
                        <span className="text-sm capitalize text-gray-600">
                          {t(permission)}
                        </span>
                        <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'
                        }`}>
                          {value ? t("allowed") : t("denied")}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
      
      {/* Action Buttons */}
      <div className="mt-6 flex flex-col-reverse sm:flex-row justify-end gap-3">
        {onDelete && (
          <button 
            onClick={onDelete}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition flex items-center justify-center"
          >
            <Trash2 size={18} className="mr-2" />
            {t("deleteUser")}
          </button>
        )}
        {onEdit && (
          <button 
            onClick={onEdit}
            className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90 transition flex items-center justify-center"
          >
            <Edit2 size={18} className="mr-2" />
            {t("editUser")}
          </button>
        )}
      </div>
    </div>
  );
};

export default UserDetailsPanel;
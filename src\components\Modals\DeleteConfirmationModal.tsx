import React from "react";
import { createPortal } from "react-dom";
import { AlertTriangle, X, Loader2 } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  itemName: string;
  message?: string;
  isLoading?: boolean;
  error?: string | null;
  conditions?: string[]; 
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  itemName,
  message,
  isLoading = false,
  error = null,
  conditions ,
}) => {
  const { t , language} = useLanguage();

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      ></div>
      <div className="relative mx-4 w-full max-w-md overflow-hidden rounded-lg bg-white shadow-xl dark:bg-boxdark">
        <div className="flex items-center justify-between border-b border-gray-200 p-5 dark:border-gray-700">
          <h3 className="flex items-center text-xl font-semibold text-gray-900 dark:text-white">
            <AlertTriangle className="mr-2 text-red-500" size={24} />
            {title}
          </h3>
          <button
            onClick={onClose}
            className="rounded-full p-1 hover:bg-gray-200 dark:hover:bg-gray-700"
          >
            <X className="text-gray-500" size={20} />
          </button>
        </div>
        <div className="p-5">
          <p className="mb-4 text-gray-600 dark:text-gray-300">
            {message || t("deleteConfirmationMessage")}
            
            {conditions && conditions.length > 0 && (
              <ul className="mt-3 space-y-1 text-sm text-gray-600 dark:text-gray-300">
                {conditions.map((condition, index) => (
                  <li
                    key={index}
                    className={`flex items-start ${
                      language === "ar" ? "flex-row-reverse text-right" : ""
                    }`}
                  >
                    <span className="mt-1 mr-2 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-gray-500 dark:bg-gray-300" />
                    <span className="flex-1">{condition}</span>
                  </li>
                ))}
              </ul>
            )}

          </p>

          {/* Display Error Message */}
          {error && (
            <div className="mb-4 rounded-md bg-red-50 p-3 dark:bg-red-900/20">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
              disabled={isLoading}
            >
              {t("cancel")}
            </button>
            <button
              onClick={onConfirm}
              disabled={isLoading}
              className="flex items-center rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700 focus:outline-none disabled:bg-red-400"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("deleting")}
                </>
              ) : (
                t("delete")
              )}
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body,
  );
};

export default DeleteConfirmationModal;

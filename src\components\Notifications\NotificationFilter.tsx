import React from 'react';
import { Filter, CheckCircle, Info, AlertCircle, XCircle, Clock } from 'lucide-react';
import { NotificationFilterOptions } from '@/lib/types/notification';
import useLanguage from '@/hooks/useLanguage';

interface NotificationFilterProps {
  filterOptions: NotificationFilterOptions;
  onFilterChange: (filters: NotificationFilterOptions) => void;
}

const NotificationFilter: React.FC<NotificationFilterProps> = ({
  filterOptions,
  onFilterChange,
}) => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = React.useState(false);

  const handleTypeChange = (type: NotificationFilterOptions['type']) => {
    onFilterChange({ ...filterOptions, type });
    setIsOpen(false);
  };

  const handleReadStatusChange = (read: NotificationFilterOptions['read']) => {
    onFilterChange({ ...filterOptions, read });
    setIsOpen(false);
  };

  const handleTimeframeChange = (timeframe: NotificationFilterOptions['timeframe']) => {
    onFilterChange({ ...filterOptions, timeframe });
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center text-xs font-medium text-gray-600 dark:text-gray-300"
      >
        <Filter className="h-4 w-4 mr-1" />
        {t('filter')}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-20 border border-gray-200 dark:border-gray-700">
          <div className="p-3">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('notificationType')}</h3>
            <div className="space-y-2">
              <button
                onClick={() => handleTypeChange('all')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.type === 'all' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <span className="h-4 w-4 mr-2 flex-shrink-0" />
                <span className="text-sm">{t('all')}</span>
              </button>
              <button
                onClick={() => handleTypeChange('info')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.type === 'info' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <Info className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0" />
                <span className="text-sm">{t('information')}</span>
              </button>
              <button
                onClick={() => handleTypeChange('success')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.type === 'success' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                <span className="text-sm">{t('success')}</span>
              </button>
              <button
                onClick={() => handleTypeChange('warning')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.type === 'warning' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <AlertCircle className="h-4 w-4 text-amber-500 mr-2 flex-shrink-0" />
                <span className="text-sm">{t('warning')}</span>
              </button>
              <button
                onClick={() => handleTypeChange('error')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.type === 'error' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <XCircle className="h-4 w-4 text-red-500 mr-2 flex-shrink-0" />
                <span className="text-sm">{t('error')}</span>
              </button>
            </div>
          </div>

          <hr className="border-gray-200 dark:border-gray-700" />

          <div className="p-3">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('status')}</h3>
            <div className="space-y-2">
              <button
                onClick={() => handleReadStatusChange(null)}
                className={`w-full text-left px-2 py-1 rounded-md text-sm ${
                  filterOptions.read === null ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                {t('all')}
              </button>
              <button
                onClick={() => handleReadStatusChange(false)}
                className={`w-full text-left px-2 py-1 rounded-md text-sm ${
                  filterOptions.read === false ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                {t('unread')}
              </button>
              <button
                onClick={() => handleReadStatusChange(true)}
                className={`w-full text-left px-2 py-1 rounded-md text-sm ${
                  filterOptions.read === true ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                {t('read')}
              </button>
            </div>
          </div>

          <hr className="border-gray-200 dark:border-gray-700" />

          <div className="p-3">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('timeframe')}</h3>
            <div className="space-y-2">
              <button
                onClick={() => handleTimeframeChange('all')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.timeframe === 'all' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <Clock className="h-4 w-4 mr-2 text-gray-400" />
                <span className="text-sm">{t('allTime')}</span>
              </button>
              <button
                onClick={() => handleTimeframeChange('today')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.timeframe === 'today' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <Clock className="h-4 w-4 mr-2 text-gray-400" />
                <span className="text-sm">{t('today')}</span>
              </button>
              <button
                onClick={() => handleTimeframeChange('week')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.timeframe === 'week' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <Clock className="h-4 w-4 mr-2 text-gray-400" />
                <span className="text-sm">{t('thisWeek')}</span>
              </button>
              <button
                onClick={() => handleTimeframeChange('month')}
                className={`w-full flex items-center text-left px-2 py-1 rounded-md ${
                  filterOptions.timeframe === 'month' ? 'bg-gray-100 dark:bg-gray-700' : ''
                }`}
              >
                <Clock className="h-4 w-4 mr-2 text-gray-400" />
                <span className="text-sm">{t('thisMonth')}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationFilter;

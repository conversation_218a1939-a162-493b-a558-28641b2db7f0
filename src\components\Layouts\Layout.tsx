"use client";
import React, { useState, ReactNode } from "react";
import Sidebar from "@/components/Sidebar";
import Header from "@/components/Header";
import useLanguage from "@/hooks/useLanguage";

export default function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { language, toggleLanguage } = useLanguage();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <>
      <div className="flex" dir={language === "ar" ? "rtl" : "ltr"}>
        <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        <div
          className={`relative flex flex-1 flex-col ${
            language === "ar" ? "lg:mr-72.5" : "lg:ml-72.5"
          }`}
        >
          <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
          <main>
            <div
              className="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10"
              dir={language === "ar" ? "rtl" : "ltr"}
            >
              {children}
            </div>
          </main>
        </div>
      </div>
      <button
        onClick={scrollToTop}
        className="fixed bottom-4 right-4 z-50 bg-blue-500 text-white p-3 rounded-full shadow-lg hover:bg-blue-600 focus:outline-none"
      >
        ↑
      </button>
    </>
  );
}

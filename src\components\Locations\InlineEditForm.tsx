import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/cards/card';
import { Button } from '@/components/ui/button';
import { X, Check } from 'lucide-react';
import { Location } from '@/lib/types/location';
import useLanguage from "@/hooks/useLanguage";
import LocationForm from './LocationForm';

interface InlineEditFormProps {
  location: Location;
  onSave: (data: Partial<Location>) => void;
  onCancel: () => void;
}

const InlineEditForm: React.FC<InlineEditFormProps> = ({
  location,
  onSave,
  onCancel
}) => {
  const { t } = useLanguage();
  
  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-6 border-b dark:border-gray-700">
        <div>
          <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("editLocationDetails")}
          </CardTitle>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onCancel}
            className="text-gray-600 hover:text-gray-700 hover:bg-gray-50 dark:hover:bg-gray-900/20 border-gray-200 dark:border-gray-800"
          >
            <X className="h-4 w-4 mr-1.5" />
            {t("cancel")}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <LocationForm 
          isOpen={true}
          onClose={onCancel}
          location={location}
          onSubmit={onSave}
          view="sidebar"
        />
      </CardContent>
    </Card>
  );
};

export default InlineEditForm;

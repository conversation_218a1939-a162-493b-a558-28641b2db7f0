"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { signIn, signOut, useSession, getSession } from "next-auth/react";

type User = {
  name: string;
  email: string;
  role: "ADMIN" | "USER";
  token: string;
};

declare module "next-auth" {
  interface Session {
    accessToken?: string;
    refreshToken?: string;
    accessTokenExpiry?: number;
    refreshTokenExpiry?: number;
    user?: {
      name?: string | null;
      email?: string | null;
      role?: "ADMIN" | "USER";
    };
    error?: string;
    permissions?: any;
  }
}

type AuthContextType = {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export async function updateAuthSession(newTokens: { accessToken: string; refreshToken?: string }) {
  const session = await getSession();
  if (!session) return false;

  const updatedSession = {
    ...session,
    accessToken: newTokens.accessToken,
    refreshToken: newTokens.refreshToken || session.refreshToken,
  };

  document.cookie = `next-auth.session-token=${newTokens.accessToken}; path=/`;
  document.cookie = `next-auth.refresh-token=${newTokens.refreshToken || session.refreshToken}; path=/`;

  return true;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    if (session) {
      setUser((prevUser) => {
        if (!prevUser || prevUser.email !== session.user?.email) {
          return {
            name: session.user?.name ?? "Unknown",
            email: session.user?.email ?? "Unknown",
            role: session.user?.role ?? "USER",
            token: session.accessToken ?? "",
          };
        }
        return prevUser; // Prevent unnecessary state updates
      });
    } else {
      setUser(null);
    }
  }, [session]);
  

  // Auto-refresh the token before it expires
  useEffect(() => {
    if (!session?.accessTokenExpiry || !session?.refreshToken) return;

    const refreshTime = session.accessTokenExpiry - Date.now() - 60000; // Refresh 1 minute before expiry
    if (refreshTime <= 0) return;

    const interval = setTimeout(async () => {
      console.log("Refreshing token...");
      try {
        const response = await fetch("/api/auth/session", {
          method: "POST",
          body: JSON.stringify({ trigger: "update" }),
        });

        if (!response.ok) {
          throw new Error("Failed to refresh token");
        }

        await updateAuthSession(await response.json());
        console.log("Token refreshed successfully");
      } catch (error) {
        console.error("Failed to refresh token:", error);
        logout();
      }
    }, refreshTime);

    return () => clearTimeout(interval);
  }, [session?.accessTokenExpiry, session?.refreshToken]);

  const login = async (email: string, password: string) => {
    const result = await signIn("credentials", {
      redirect: false,
      email,
      password,
    });

    if (result?.error) {
      return false;
    } else {
      router.push("/dashboard");
      return true;
    }
  };

  const logout = () => {
    signOut({ callbackUrl: "/auth/signin" });
  };

  return <AuthContext.Provider value={{ user, login, logout }}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) throw new Error("useAuth must be used within an AuthProvider");
  return context;
}

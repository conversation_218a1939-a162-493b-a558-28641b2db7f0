"use client";

import React, { useState, useRef, useEffect, useMemo } from "react";
import Breadcrumb from "../Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import CalendarHeader from "./CalendarHeader";
import CalendarBody from "./CalendarBody";
import { getMonthDays, getYear } from "@/utils/dateUtils";
import { EventDetails } from "@/lib/interfaces/finaces";
import { ReservationDetails } from "@/lib/interfaces/ReservationDetails";
import IncomeServices from "@/lib/income";
import ExpensesServices from "@/lib/expenses";
import UpcomingEventsPage from "../Dashboard/UpcommingEventsPage/UpcommingEvents";

const Calendar = () => {
  const { t } = useLanguage();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState("events");
  const [selectedDay, setSelectedDay] = useState<Date | null>(null);
  const [events, setEvents] = useState<EventDetails[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [reservations, setReservations] = useState<ReservationDetails[]>([]);
  const [selectedLocation, setSelectedLocation] = useState("");
  const eventsSectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchEventsData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const incomeService = IncomeServices();
        const expensesService = ExpensesServices();

        // Fetch both income and expense events
        const [incomeEvents, expenseEvents] = await Promise.all([
          incomeService.getIncomes(),
          expensesService.getExpenses(),
        ]);

        console.log("Fetched income events:", incomeEvents);
        console.log("Fetched expense events:", expenseEvents);

        // Combine and set the events
        const allEvents = [...incomeEvents, ...expenseEvents];
        setEvents(allEvents);
      } catch (err) {
        console.error("Error fetching events data:", err);
        setError("Failed to load events data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchEventsData();
  }, []);

  // Filter events by the selected month/year
  const filteredEvents = useMemo(() => {
    return events.filter((event) => {
      if (!event.dueDate) return false;

      const eventDate = new Date(event.dueDate);
      return (
        eventDate.getFullYear() === currentDate.getFullYear() &&
        eventDate.getMonth() === currentDate.getMonth()
      );
    });
  }, [events, currentDate]);

  useEffect(() => {
    if (reservations.length > 0) {
      setSelectedLocation(reservations[0].location);
    }
  }, [reservations]);

  const handlePrevMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1),
    );
  };

  const handleNextMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1),
    );
  };

  const handleYearChange = (year: number) => {
    setCurrentDate(new Date(year, currentDate.getMonth(), 1));
  };

  const handleDayClick = (day: number) => {
    setSelectedDay(
      new Date(currentDate.getFullYear(), currentDate.getMonth(), day),
    );
    if (eventsSectionRef.current) {
      eventsSectionRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const days = getMonthDays(currentDate);
  const year = getYear(currentDate);
  const month = currentDate.getMonth();

  const selectedDayEvents = selectedDay
    ? filteredEvents.filter((event) => {
        const eventDate = new Date(event.dueDate);
        return eventDate.toDateString() === selectedDay.toDateString();
      })
    : [];

  return (
    <div className="mx-auto max-w-7xl space-y-4 p-2 sm:space-y-6 sm:p-4 lg:space-y-8 lg:p-6">
      <Breadcrumb pageName={t("calendar")} />

      {isLoading ? (
        <div className="flex min-h-[400px] items-center justify-center rounded-lg bg-white dark:bg-gray-800">
          <div className="flex flex-col items-center space-y-4">
            <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t("Loading calendar...")}
            </p>
          </div>
        </div>
      ) : error ? (
        <div
          className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20"
          role="alert"
        >
          <div className="flex flex-col sm:flex-row sm:items-center">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  {t("Error!")}
                </h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>{t(error)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4 sm:space-y-6">
          {/* Calendar Container */}
          <div className="w-full overflow-hidden rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <CalendarHeader
              currentDate={currentDate}
              onPrevMonth={handlePrevMonth}
              onNextMonth={handleNextMonth}
              onYearChange={handleYearChange}
            />
            <div className="overflow-x-auto">
              <CalendarBody
                days={days}
                year={year}
                month={month}
                viewType={viewType}
                events={filteredEvents}
                onDayClick={handleDayClick}
              />
            </div>
          </div>

          {/* Selected Day Events */}
          {selectedDay && (
            <div className="space-y-4" ref={eventsSectionRef}>
              <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white sm:text-xl lg:text-2xl">
                  {t("Events")} {t("of")} {selectedDay.toLocaleDateString()}
                </h2>
              </div>
              {selectedDayEvents.length > 0 ? (
                <UpcomingEventsPage
                  events={selectedDayEvents}
                  setEvents={setEvents}
                />
              ) : (
                <div className="rounded-lg bg-gray-50 p-6 text-center dark:bg-gray-800 sm:p-8">
                  <div className="mx-auto max-w-md">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
                      {t("No events scheduled")}
                    </h3>
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      {t("No events scheduled for this day.")}
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Calendar;

{"locations": "Locations", "searchLocations": "Search locations...", "filters": "Filters", "clearFilters": "Clear filters", "locationType": "Location Type", "status": "Status", "noLocationsFound": "No locations found", "office": "Office", "warehouse": "Warehouse", "retail": "Retail", "other": "Other", "active": "Active", "inactive": "Inactive", "locationDetails": "Location Details", "noLocationSelected": "No location selected. Please select a location to view details.", "locationId": "Location ID", "edit": "Edit", "delete": "Delete", "contactInformation": "Contact Information", "contactPerson": "Contact Person", "email": "Email", "phone": "Phone", "created": "Created", "by": "by", "capacity": "Capacity", "notReserved": "Not Reserved", "activeLocations": "Active Locations", "ourProperties": "Our Properties", "highCapacity": "High Capacity", "warehousesOnly": "Warehouses Only", "ourLocation": "Our Location", "reservation": "Reservation", "noActiveLocations": "No active locations found", "noActiveReservations": "No active reservations found", "ownership": "Ownership", "ownedBy": "Owned by", "sharedWith": "Shared with", "primaryContact": "Primary Contact", "createNewLocationDescription": "Create a new location by filling out the form below. Make sure to provide accurate information to ensure smooth operations.", "capacityTrend": "Capacity Trend", "reservedCapacity": "Reserved Capacity", "availableCapacity": "Available Capacity", "clickOnMonthToViewDailyCapacity": "Click on a month to view daily capacity", "currentReservations": "Current Reservations", "capacityAllocation": "Capacity Allocation", "totalCapacity": "Total Capacity", "capacityUtilization": "Capacity Utilization", "usedCapacity": "Used Capacity", "ownershipDetails": "Ownership Details", "enterLocationDescription": "Enter location description", "generalInfo": "General Information", "ownershipFinancial": "Ownership & Financial", "selectPrimaryOwner": "Select Primary Owner", "egyCommIsPrimaryOwner": "<PERSON><PERSON> is the primary owner", "primaryOwner": "Primary Owner", "primaryOwnerDescription": "The primary owner is the main entity responsible for the location. This entity will be the first point of contact for any inquiries or issues related to the location.", "egyCommPercentage": "<PERSON><PERSON>", "egyCommPercentageDescription": "The percentage of ownership that Saray Vera holds in the location. This percentage is used to determine the share of profits or losses associated with the location.", "percentageAllocation": "Percentage Allocation", "remainingForPartners": "Remaining for Partners", "percentageMustEqual100": "The total percentage allocation must equal 100%.", "additionalPartners": "Additional Partners", "addPartner": "Add Partner", "partnerName": "Partner Name", "partnerPercentage": "Partner Percentage", "partnerPercentageDescription": "The percentage of ownership that the partner holds in the location. This percentage is used to determine the share of profits or losses associated with the location.", "partnerPercentageMustEqual100": "The total partner percentage allocation must equal 100%.", "partnerNameRequired": "Partner name is required.", "partnerPercentageRequired": "Partner percentage is required.", "selectSharedWith": "Select Shared With", "sharedWithDescription": "The entities or individuals that the location is shared with. This can include partners, tenants, or any other concerned parties.", "selectPrimaryContact": "Select Primary Contact", "primaryContactDescription": "The person responsible for communication with the primary owner. This person will be the main point of contact for any inquiries or issues related to the location.", "selectLocationType": "Select Location Type", "locationTypeDescription": "The type of location, such as office, warehouse, or retail. This helps in categorizing the location based on its function.", "notSet": "Not Set", "summary": "Summary", "totalPartnersPercentage": "Total Partners Percentage", "combinedTotal": "Combined Total", "partners": "Partners", "egyCommIsMainOwner": "<PERSON><PERSON> is the main owner", "editLocationDetails": "Edit Location Details", "allPartnersSecondary": "All partners are secondary", "selectLocation": "Select Location", "noPartnersAdded": "No partners added", "availablePercentage": "Available Percentage", "dailyCapacity": "Daily Capacity", "deleteLocationWarning": "Are you sure you want to delete this location? This action cannot be undone.", "deleteLocationConditions": "Note: You cannot delete this location if it has: • Active reservations (future bookings) • Incomplete income events (not completed/cancelled) • Incomplete expense events (not completed/cancelled). Any active contracts associated with this location will be automatically marked as deleted.", "partiallySharesForUs": "We have some shares in this location", "OwnedByUs": "Owned by us", "fullyOwnedByUS": "Fully owned by us", "WeDontHaveShares": "We do not have shares in this location", "clearAllFilters": "Clear all filters", "reservationContacts": "Reservation Contacts", "units": "Units", "importLocations": "Import Locations", "importLocationsDescription": "Import locations from a CSV file. Ensure the file is formatted correctly to avoid errors during import.", "startImport": "Start Import", "cancelImport": "Cancel Import", "Drag and drop one or more files (CSV or Excel)": "Drag and drop one or more files (CSV or Excel)", "Import Locations": "Import Locations", "fillLocationDetails": "Fill in the location details below to create a new location. Ensure all required fields are completed accurately.", "daysRemaining": "Days Remaining", "duration": "Duration", "Complete overview of all financial records for this location": "Complete overview of all financial records for this location", "Income Events": "Income Events", "totalPercentageMustBe100": "The total percentage must equal 100%.", "weDontHaveShares": "We don't have shares in this location", "enterCapacity": "Enter capacity", "selectType": "Select Type"}
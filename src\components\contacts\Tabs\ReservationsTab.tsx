import React, { useState, useEffect } from "react";
import { Reservation } from "@/lib/interfaces/reservation";
import ReservationServices from "@/lib/reservations";
import { Loader2 } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";
import {
  FaEye,
  FaTimes,
  FaSearch,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaUser,
  FaMoneyBillAlt,
} from "react-icons/fa";

interface ReservationsTabProps {
  ContactId: string;
}

const ReservationsTab: React.FC<ReservationsTabProps> = ({ ContactId }) => {
  const { t } = useLanguage();
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState<{ start: string; end: string }>({
    start: "",
    end: "",
  });

  // Function to determine if we should use card view (mobile) or table view
  const [isCardView, setIsCardView] = useState(window.innerWidth < 768);

  // Fixed fetch to avoid infinite loop - get service inside useEffect
  useEffect(() => {
    const fetchReservations = async () => {
      setIsLoading(true);
      try {
        const { getReservationsByContact } = ReservationServices();
        const response = await getReservationsByContact(ContactId);
        setReservations(response.reservations || []);
        setError(null);
      } catch (err) {
        console.error("Error fetching reservations:", err);
        setError("Failed to load reservation data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchReservations();
  }, [ContactId]);

  // Listen for window resizes to toggle between card and table view
  useEffect(() => {
    const handleResize = () => {
      setIsCardView(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Filter reservations based on search term and filters
  const filteredReservations = reservations.filter((reservation) => {
    const matchesSearch =
      reservation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.contact?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      false ||
      reservation.location?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      false;

    const matchesStatus =
      statusFilter === "all" || reservation.status === statusFilter;

    const startDate = dateFilter.start ? new Date(dateFilter.start) : null;
    const endDate = dateFilter.end ? new Date(dateFilter.end) : null;
    const reservationStart = new Date(reservation.start_date);
    const reservationEnd = new Date(reservation.end_date);

    const matchesDate =
      (!startDate ||
        reservationStart >= startDate ||
        reservationEnd >= startDate) &&
      (!endDate || reservationStart <= endDate);

    return matchesSearch && matchesStatus && matchesDate;
  });

  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setDateFilter({ start: "", end: "" });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
      case "completed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
      case "cancelled":
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  // Handle reservation click - readonly view
  const handleReservationClick = (id: number) => {
    console.log(`View reservation details for: ${id}`);
    // You could implement a details modal here in the future
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return <div className="py-8 text-center text-red-500">{error}</div>;
  }

  return (
    <div className="rounded-lg bg-white p-3 dark:bg-gray-800 sm:p-6">
      {/* Filters - Improved for mobile */}
      <div className="mb-4 sm:mb-6">
        <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:hidden">
          {t("Search & Filters")}
        </h3>
        <div className="flex flex-col space-y-3 sm:flex-row sm:flex-wrap sm:gap-4 sm:space-y-0">
          {/* Search - Full width on mobile */}
          <div className="w-full">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t(
                  "Search reservations... (Title, Client, Location)",
                )}
                className="w-full rounded-lg border py-2 pl-10 pr-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
              />
              <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
                >
                  <FaTimes />
                </button>
              )}
            </div>
          </div>

          {/* Filter Controls */}
          <div className="flex w-full flex-col gap-3 sm:flex-row">
            {/* Status Filter */}
            <div className="w-full sm:w-1/3">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
              >
                <option value="all">{t("All Statuses")}</option>
                <option value="active">{t("Active")}</option>
                <option value="pending">{t("Pending")}</option>
                <option value="completed">{t("Completed")}</option>
                <option value="cancelled">{t("Cancelled")}</option>
              </select>
            </div>

            {/* Date Range */}
            <div className="flex w-full flex-col gap-2 sm:w-1/3 sm:flex-row sm:items-center">
              <div className="relative w-full">
                <FaCalendarAlt className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
                <input
                  type="date"
                  value={dateFilter.start}
                  onChange={(e) =>
                    setDateFilter({ ...dateFilter, start: e.target.value })
                  }
                  className="w-full rounded-lg border py-2 pl-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
                  placeholder={t("Start date")}
                />
              </div>
              <div className="hidden items-center justify-center sm:flex">
                <span className="px-1 text-gray-400">{t("to")}</span>
              </div>
              <div className="flex items-center sm:hidden">
                <span className="text-gray-400">{t("to")}</span>
              </div>
              <div className="relative w-full">
                <input
                  type="date"
                  value={dateFilter.end}
                  onChange={(e) =>
                    setDateFilter({ ...dateFilter, end: e.target.value })
                  }
                  className="w-full rounded-lg border py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
                  placeholder={t("End date")}
                />
              </div>
            </div>

            {/* Reset Button */}
            <button
              onClick={resetFilters}
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-red-500 px-4 py-2 text-white transition-colors hover:bg-red-600 sm:w-auto sm:py-3"
            >
              <FaTimes />
              <span>{t("Reset Filters")}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Card View for Mobile */}
      {isCardView ? (
        <div className="space-y-4">
          {filteredReservations.length > 0 ? (
            filteredReservations.map((reservation) => (
              <div
                key={reservation.id}
                className="cursor-pointer rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
                onClick={() => handleReservationClick(Number(reservation.id))}
              >
                <div className="mb-2 flex items-start justify-between">
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    {reservation.title}
                  </h3>
                  <span
                    className={`rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(reservation.status)}`}
                  >
                    {reservation.status.charAt(0).toUpperCase() +
                      reservation.status.slice(1)}
                  </span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center text-gray-500 dark:text-gray-400">
                    <FaUser className="mr-2" size={14} />{" "}
                    {reservation.contact?.name || "N/A"}
                  </div>
                  <div className="flex items-center text-gray-500 dark:text-gray-400">
                    <FaMapMarkerAlt className="mr-2" size={14} />{" "}
                    {reservation.location?.name || "N/A"}
                  </div>
                  <div className="flex items-center text-gray-500 dark:text-gray-400">
                    <FaCalendarAlt className="mr-2" size={14} />
                    {formatDate(reservation.start_date)} -{" "}
                    {formatDate(reservation.end_date)}
                  </div>
                  <div className="flex items-center font-medium text-gray-900 dark:text-white">
                    <FaMoneyBillAlt className="mr-2" size={14} />$
                    {(reservation.total_amount || 0).toLocaleString()}
                  </div>
                </div>

                <div className="mt-3 flex justify-end space-x-3 border-t border-gray-200 pt-3 dark:border-gray-700">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleReservationClick(Number(reservation.id));
                    }}
                    className="p-2 text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                  >
                    <FaEye size={18} />
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="py-8 text-center text-gray-500 dark:text-gray-400">
              {t("No reservations found")}
            </div>
          )}
        </div>
      ) : (
        /* Table View for Tablets and Larger */
        <div className="overflow-x-auto rounded-lg">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("ID")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Title")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Client")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Location")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Period")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Amount")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Status")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Actions")}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {filteredReservations.length > 0 ? (
                filteredReservations.map((reservation) => (
                  <tr
                    key={reservation.id}
                    className="cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700"
                    onClick={() =>
                      handleReservationClick(Number(reservation.id))
                    }
                  >
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {reservation.id}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                      {reservation.title}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {reservation.contact?.name || "N/A"}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {reservation.location?.name || "N/A"}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(reservation.start_date)} -{" "}
                      {formatDate(reservation.end_date)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                      ${(reservation.total_amount || 0).toLocaleString()}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <span
                        className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(reservation.status)}`}
                      >
                        {reservation.status.charAt(0).toUpperCase() +
                          reservation.status.slice(1)}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                      <div className="flex space-x-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleReservationClick(Number(reservation.id));
                          }}
                          className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                        >
                          <FaEye size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={8}
                    className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                  >
                    {t("No reservations found")}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ReservationsTab;

export interface contactLoaction {
  id: string;
  name: string;
  address: string;
  capacity: number;
  is_active: boolean;
}

export type ContactType = string;

export interface Contact {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  address?: string;
  type: string[] | any[]; // Allow any[] to account for different type structures
  sharedLocations: contactLoaction[];
  ownedLocations: contactLoaction[];
  createdAt: string;
  createdBy: string;
  balance: number;
  totalRevenue?: number;
  activeReservations?: number;
  completedReservations?: number;
}
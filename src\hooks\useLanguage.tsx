import { useState } from "react";
import { translate } from "@/utils/translator";
import useLocalStorage from "@/hooks/useLocalStorage"; 

const useLanguage = () => {
  const [language, setLanguage] = useLocalStorage<"en" | "ar">("language", "en"); 

  const toggleLanguage = () => {
    setLanguage((prevLanguage) => (prevLanguage === "en" ? "ar" : "en"));
    window.location.reload();
  };

  const t = (key: string): string => translate(key, language);

  return { language, toggleLanguage, t };
};

export default useLanguage;

import React, { Component, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import useLanguage from '@/hooks/useLanguage';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class AnalyticsErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Analytics Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <DefaultErrorFallback onRetry={() => this.setState({ hasError: false })} />;
    }

    return this.props.children;
  }
}

const DefaultErrorFallback: React.FC<{ onRetry: () => void }> = ({ onRetry }) => {
  // Note: This is a workaround since we can't use hooks in class components
  // In a real implementation, you might want to use a function component wrapper
  return (
    <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
      <div className="text-center py-12">
        <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Analytics Error
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          Something went wrong while loading the analytics. Please try again.
        </p>
        <button
          onClick={onRetry}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </button>
      </div>
    </div>
  );
};

export default AnalyticsErrorBoundary;

// Loading component for analytics
export const AnalyticsLoading: React.FC = () => {
  return (
    <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
      <div className="animate-pulse">
        {/* Header skeleton */}
        <div className="flex justify-between items-center mb-6">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
        </div>
        
        {/* Stats skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20 mb-2"></div>
              <div className="h-6 bg-gray-200 dark:bg-gray-600 rounded w-24"></div>
            </div>
          ))}
        </div>
        
        {/* Chart skeleton */}
        <div className="h-96 bg-gray-100 dark:bg-gray-700 rounded-lg"></div>
      </div>
    </div>
  );
};

// Empty state component for analytics
export const AnalyticsEmptyState: React.FC<{
  title?: string;
  description?: string;
  icon?: ReactNode;
  action?: ReactNode;
}> = ({ 
  title = "No Data Available", 
  description = "Add some financial events to see analytics",
  icon,
  action 
}) => {
  return (
    <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
      <div className="text-center py-12">
        {icon || (
          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <div className="w-8 h-8 bg-gray-400 rounded"></div>
          </div>
        )}
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          {description}
        </p>
        {action}
      </div>
    </div>
  );
};

// Data validation utilities
export const validateAnalyticsData = (data: any[]): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!Array.isArray(data)) {
    errors.push('Data must be an array');
    return { isValid: false, errors };
  }
  
  if (data.length === 0) {
    errors.push('No data provided');
    return { isValid: false, errors };
  }
  
  // Check for required fields
  const requiredFields = ['amount', 'dueDate', 'category'];
  data.forEach((item, index) => {
    requiredFields.forEach(field => {
      if (!item[field]) {
        errors.push(`Missing ${field} in item ${index + 1}`);
      }
    });
    
    // Validate amount is a number
    if (item.amount && isNaN(Number(item.amount))) {
      errors.push(`Invalid amount in item ${index + 1}`);
    }
    
    // Validate date
    if (item.dueDate && isNaN(new Date(item.dueDate).getTime())) {
      errors.push(`Invalid date in item ${index + 1}`);
    }
  });
  
  return { isValid: errors.length === 0, errors };
};

// Safe analytics wrapper component
export const SafeAnalyticsWrapper: React.FC<{
  data: any[];
  children: ReactNode;
  loadingComponent?: ReactNode;
  emptyComponent?: ReactNode;
  errorComponent?: ReactNode;
}> = ({ 
  data, 
  children, 
  loadingComponent = <AnalyticsLoading />,
  emptyComponent = <AnalyticsEmptyState />,
  errorComponent 
}) => {
  // Validate data
  const validation = validateAnalyticsData(data);
  
  if (!validation.isValid) {
    if (data.length === 0) {
      return <>{emptyComponent}</>;
    }
    
    if (errorComponent) {
      return <>{errorComponent}</>;
    }
    
    return (
      <AnalyticsEmptyState
        title="Data Validation Error"
        description={`Issues found: ${validation.errors.join(', ')}`}
        icon={<AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />}
      />
    );
  }
  
  return (
    <AnalyticsErrorBoundary fallback={errorComponent}>
      {children}
    </AnalyticsErrorBoundary>
  );
};

// Hook for analytics error handling
export const useAnalyticsErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  
  const handleError = React.useCallback((error: Error) => {
    console.error('Analytics Error:', error);
    setError(error);
  }, []);
  
  const clearError = React.useCallback(() => {
    setError(null);
  }, []);
  
  const withErrorHandling = React.useCallback(async <T,>(
    asyncFn: () => Promise<T>
  ): Promise<T | null> => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await asyncFn();
      return result;
    } catch (err) {
      handleError(err as Error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);
  
  return {
    error,
    isLoading,
    handleError,
    clearError,
    withErrorHandling
  };
};

// Performance monitoring for analytics
export const withAnalyticsPerformance = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  return React.memo((props: P) => {
    React.useEffect(() => {
      const startTime = performance.now();
      
      return () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        
        if (renderTime > 100) { // Log slow renders
          console.warn(`Slow analytics render: ${componentName} took ${renderTime.toFixed(2)}ms`);
        }
      };
    });
    
    return <WrappedComponent {...props} />;
  });
};

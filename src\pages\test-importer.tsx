import React from 'react';
import { DataImporter } from '../components/Importer/DataImporter';
import type { ColumnDefinition } from '../components/Importer/DataImporter';

export default function TestImporter() {
  // Simple test columns
  const columns: ColumnDefinition[] = [
    {
      key: 'name',
      label: 'Name',
      type: 'text',
      required: true,
    },
    {
      key: 'email',
      label: 'Email',
      type: 'text',
      required: true,
    },
    {
      key: 'age',
      label: 'Age',
      type: 'text',
      required: false,
    },
  ];

  const handleImport = (data: any[]) => {
    console.log('Imported data:', data);
    alert(`Imported ${data.length} rows successfully!`);
  };

  const handleValidationChange = (isValid: boolean, errors: any[]) => {
    console.log('Validation status:', isValid, 'Errors:', errors);
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">DataImporter Test</h1>

      <DataImporter
        columns={columns}
        onImport={handleImport}
        maxPreviewRows={50}
        allowedFileTypes={['.csv', '.xlsx']}
        onValidationChange={handleValidationChange}
      />
    </div>
  );
}

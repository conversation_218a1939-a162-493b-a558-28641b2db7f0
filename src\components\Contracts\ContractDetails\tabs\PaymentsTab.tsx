import React, { useState } from "react";
import { Contract } from "@/lib/interfaces/contract";
import { EventDetails } from "@/lib/interfaces/finaces";
import { FaChevronDown, FaExclamationCircle } from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import { formatCurrency, formatDate, getStatusBadgeClass } from "../utils";
import { SuccessPopup } from "@/components/common/successPopUp";
import { ErrorPopup } from "@/components/common/errorPopUp";
import IncomeServices from "@/lib/income";
import ExpenseServices from "@/lib/expenses";
const statusOptions = [
  "completed",
  "pending",
  "upcoming",
  "overdue",
] as const;

interface PaymentsTabProps {
  contract: Contract;
  incomeData: EventDetails[];
  expenseData: EventDetails[];
  isLoading: boolean;
  error: string | null;
  totalPaid: number;
  totalRemaining: number;
  isFinancialDataReady: boolean;
  onEventStatusUpdate?: (
    eventId: string,
    newStatus: "completed" | "pending" | "cancelled" | "upcoming" | "overdue",
    type: "income" | "expense",
  ) => void;
}

const PaymentsTab: React.FC<PaymentsTabProps> = ({
  contract,
  incomeData,
  expenseData,
  isLoading,
  error,
  totalPaid,
  totalRemaining,
  isFinancialDataReady,
  onEventStatusUpdate,
}) => {
  const { t, language } = useLanguage();

  const [activeTab, setActiveTab] = useState<"income" | "expense">("income");
  const [dropdownOpenId, setDropdownOpenId] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [updatingEventId, setUpdatingEventId] = useState<string | null>(null);
  const [updateBuffer, setUpdateBuffer] = useState<{
    eventId: string;
    newStatus: string;
    type: "income" | "expense";
  } | null>(null);
  const [selectedCompletedEvent, setSelectedCompletedEvent] = useState<
    string | null
  >(null);
  const [dateValue, setDateValue] = useState<string>("");

  const IncomeService = IncomeServices();
  const ExpenseService = ExpenseServices();

  // Buffer timeout ref
  const bufferTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Clear buffer timeout on unmount
  React.useEffect(() => {
    return () => {
      if (bufferTimeoutRef.current) {
        clearTimeout(bufferTimeoutRef.current);
      }
    };
  }, []);

  const handleEventStatusChange = async (
    eventId: string,
    newStatus: "completed" | "pending" | "cancelled" | "upcoming" | "overdue",
    type: "income" | "expense",
    customDate?: string,
  ) => {
    // Clear any existing timeout
    if (bufferTimeoutRef.current) {
      clearTimeout(bufferTimeoutRef.current);
    }

    // Set updating state immediately
    setUpdatingEventId(eventId);

    // Update local state immediately for better UX
    if (onEventStatusUpdate) {
      onEventStatusUpdate(eventId, newStatus, type);
    }

    // Store the update in buffer
    setUpdateBuffer({ eventId, newStatus, type });

    // Set a buffer timeout (e.g., 1 second)
    bufferTimeoutRef.current = setTimeout(async () => {
      try {
        // Prepare update data
        const updateData: any = { status: newStatus };

        // If status is being set to completed, add the appropriate date field
        if (newStatus === "completed" && customDate) {
          // Convert the date string to ISO format for the API
          const dateToUse = new Date(
            customDate + "T00:00:00.000Z",
          ).toISOString();
          if (type === "income") {
            updateData.received_date = dateToUse;
          } else {
            updateData.paid_date = dateToUse;
          }
        }

        // API call to update status
        if (type === "income") {
          await IncomeService.updateIncomeStatus(eventId, updateData);
        } else {
          await ExpenseService.updateExpenseStatus(eventId, updateData);
        }

        setSuccessMessage(t("Status updated successfully!"));
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 2000);
      } catch (err) {
        // Revert the local state change on error
        const originalEvent =
          type === "income"
            ? incomeData.find((e) => e.id === eventId)
            : expenseData.find((e) => e.id === eventId);

        if (originalEvent && onEventStatusUpdate) {
          onEventStatusUpdate(eventId, originalEvent.status, type);
        }

        setErrorMessage(t("Failed to update status."));
        setShowError(true);
        setTimeout(() => setShowError(false), 2500);
      } finally {
        setUpdatingEventId(null);
        setUpdateBuffer(null);
        setSelectedCompletedEvent(null);
        setDropdownOpenId(null);
        setDateValue(""); // Reset date value
      }
    }, 1000); // 1 second buffer
  };

  // Local format functions with Arabic support
  const formatCurrencyLocal = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDateLocal = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === "ar" ? "ar-EG" : "en-US",
    );
  };

  const renderEventRows = (data: EventDetails[], type: "income" | "expense") =>
    data.length > 0 ? (
      data.map((event) => (
        <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
          <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
            {event.title}
          </td>
          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
            {formatCurrencyLocal(event.amount)}
          </td>
          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
            {formatDateLocal(event.dueDate)}
          </td>
          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
            {event.type || t("Payment")}
          </td>
          <td className="relative whitespace-nowrap px-6 py-4">
            <button
              className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${
                updatingEventId === event.id
                  ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                  : getStatusBadgeClass(event.status || "completed")
              }`}
              onClick={(e) => {
                e.stopPropagation();
                setDropdownOpenId(
                  event.id === dropdownOpenId ? null : event.id,
                );
              }}
              type="button"
              disabled={updatingEventId === event.id}
            >
              {updatingEventId === event.id ? (
                <>
                  <div className="mr-1 h-3 w-3 animate-spin rounded-full border border-current border-t-transparent"></div>
                  {t("updating")}...
                </>
              ) : (
                <>
                  {t(
                    (event.status || "completed").charAt(0).toUpperCase() +
                      (event.status || "completed").slice(1),
                  )}
                  <FaChevronDown className="ml-1 h-3 w-3" />
                </>
              )}
            </button>{" "}
            {dropdownOpenId === event.id && updatingEventId !== event.id && (
              <div className="absolute left-0 z-10 mt-1 min-w-[280px] rounded border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-900">
                {statusOptions.map((status) => (
                  <div key={status} className="block">
                    <button
                      className={`block w-full px-3 py-2 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-800 ${
                        event.status === status
                          ? "font-bold text-blue-600 dark:text-blue-400"
                          : "text-gray-700 dark:text-gray-200"
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (status === "completed") {
                          // Don't close dropdown yet, show date input
                          setSelectedCompletedEvent(event.id);
                          // Reset date value when opening date input for new event
                          setDateValue(new Date().toISOString().split("T")[0]);
                        } else {
                          handleEventStatusChange(event.id, status, type);
                          setDropdownOpenId(null);
                        }
                      }}
                    >
                      {t(status)}
                    </button>
                    {/* Date input for completed status */}
                    {selectedCompletedEvent === event.id &&
                      status === "completed" && (
                        <div className="border-t border-gray-200 px-3 py-3 dark:border-gray-600">
                          <label className="mb-2 block text-xs font-medium text-gray-700 dark:text-gray-300">
                            {type === "income"
                              ? t("Received Date")
                              : t("Paid Date")}{" "}
                            *
                          </label>
                          <input
                            type="date"
                            value={dateValue}
                            onChange={(e) => setDateValue(e.target.value)}
                            className="mb-3 w-full rounded border border-gray-300 px-2 py-1.5 text-xs focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                            onClick={(e) => e.stopPropagation()}
                          />
                          <div className="flex gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEventStatusChange(
                                  event.id,
                                  "completed",
                                  type,
                                  dateValue,
                                );
                              }}
                              className="flex-1 rounded bg-primary px-2 py-1.5 text-xs text-white hover:bg-primary/90"
                            >
                              {t("Confirm")}
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedCompletedEvent(null);
                                setDateValue("");
                              }}
                              className="flex-1 rounded bg-gray-300 px-2 py-1.5 text-xs text-gray-700 hover:bg-gray-400 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                            >
                              {t("Cancel")}
                            </button>
                          </div>
                        </div>
                      )}
                  </div>
                ))}
              </div>
            )}
          </td>
        </tr>
      ))
    ) : (
      <tr>
        <td
          colSpan={5}
          className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
        >
          {t(`No ${type} data found for this contract`)}
        </td>
      </tr>
    );
  // Local calculation for expenses paid (completed expenses) with null checks
  const expensesPaid = expenseData
    .filter((expense) => expense.status === "completed")
    .reduce((sum, expense) => sum + (Number(expense.amount) || 0), 0);
  // Local calculation for income received (completed income) with null checks
  const incomeReceived = incomeData
    .filter((income) => income.status === "completed")
    .reduce((sum, income) => sum + (Number(income.amount) || 0), 0);

  // Total income amount (all income events) with null checks
  const totalIncomeAmount = incomeData.reduce(
    (sum, income) => sum + (Number(income.amount) || 0),
    0,
  );

  // Total expense amount (all expense events) with null checks
  const totalExpenseAmount = expenseData.reduce(
    (sum, expense) => sum + (Number(expense.amount) || 0),
    0,
  );

  // Calculate progress percentages with proper fallbacks
  const incomeProgress =
    totalIncomeAmount > 0 ? (incomeReceived / totalIncomeAmount) * 100 : 0;
  const expenseProgress =
    totalExpenseAmount > 0 ? (expensesPaid / totalExpenseAmount) * 100 : 0;

  // Show buffer status if there's a pending update
  return (
    <div>
      <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4">
        {t("Payment Information")}
      </h3>

      <div className="mb-4 flex gap-2">
        <button
          className={`rounded-t-lg px-4 py-2 text-sm font-medium transition-colors ${
            activeTab === "income"
              ? "bg-blue-500 text-white"
              : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200"
          }`}
          onClick={() => setActiveTab("income")}
        >
          {t("Income")}
        </button>
        <button
          className={`rounded-t-lg px-4 py-2 text-sm font-medium transition-colors ${
            activeTab === "expense"
              ? "bg-blue-500 text-white"
              : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200"
          }`}
          onClick={() => setActiveTab("expense")}
        >
          {t("Expense")}
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center p-8">
          <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="mb-4 flex items-center rounded-lg bg-red-100 p-4 text-red-700 dark:bg-red-900 dark:text-red-200">
          <FaExclamationCircle className="mr-2" />
          {error}
        </div>
      ) : (
        <div className="mb-6 hidden overflow-x-auto sm:block">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Title")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Amount")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Date")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Type")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  {t("Status")}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {activeTab === "income"
                ? renderEventRows(incomeData, "income")
                : renderEventRows(expenseData, "expense")}
            </tbody>
          </table>
        </div>
      )}

      <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700 sm:p-4">
        <h4 className="text-md mb-3 font-semibold text-gray-800 dark:text-gray-200">
          {activeTab === "income"
            ? t("Income Progress")
            : t("Expense Progress")}
        </h4>
        {!isFinancialDataReady ? (
          <div className="flex h-10 items-center justify-center">
            <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        ) : (
          <>
            <div className="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-600">
              <div
                className={`${
                  activeTab === "income" ? "bg-blue-600" : "bg-red-600"
                } h-2.5 rounded-full transition-all duration-300`}
                style={{
                  width: `${Math.min(100, Math.max(0, activeTab === "income" ? incomeProgress : expenseProgress))}%`,
                }}
              ></div>
            </div>
            <div className="mt-2 flex flex-wrap justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>
                {activeTab === "income" ? t("Received") : t("Paid")}:{" "}
                {Number(
                  Math.max(
                    0,
                    activeTab === "income" ? incomeProgress : expenseProgress,
                  ),
                ).toLocaleString(language === "ar" ? "ar-EG" : "en-US", {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 1,
                })}
                %
              </span>
              <span>
                {activeTab === "income"
                  ? `${formatCurrencyLocal(incomeReceived)} / ${formatCurrencyLocal(totalIncomeAmount)}`
                  : `${formatCurrencyLocal(expensesPaid)} / ${formatCurrencyLocal(totalExpenseAmount)}`}
              </span>
            </div>
            {/* Show additional info when no data */}
            {((activeTab === "income" && incomeData.length === 0) ||
              (activeTab === "expense" && expenseData.length === 0)) && (
              <div className="mt-2 text-center text-xs text-gray-500 dark:text-gray-400">
                {t(`No ${activeTab} events found for this contract`)}
              </div>
            )}
          </>
        )}
      </div>

      {showSuccess && (
        <SuccessPopup
          message={successMessage || t("Status updated successfully!")}
          onClose={() => setShowSuccess(false)}
        />
      )}
      {showError && (
        <ErrorPopup
          message={errorMessage || t("Failed to update status.")}
          onClose={() => setShowError(false)}
        />
      )}

      {updateBuffer && (
        <div className="mb-4 rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
          <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-400">
            <div className="h-4 w-4 animate-spin rounded-full border border-current border-t-transparent"></div>
            {t("Status update pending")}... {t("Will be saved automatically")}
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentsTab;

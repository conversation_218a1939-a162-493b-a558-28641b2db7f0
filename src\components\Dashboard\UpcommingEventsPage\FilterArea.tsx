import React from "react";
import { FiSearch } from "react-icons/fi";
import { FaTimes, FaFilter } from "react-icons/fa";
import Select from "react-select";
import useLanguage from "@/hooks/useLanguage";

interface FilterAreaProps {
  filterType: "All" | "income" | "expense";
  setFilterType: (value: "All" | "income" | "expense") => void;
  filterStatusOptions: string[];
  filterStatus: string;
  setFilterStatus: (value: string | null) => void;
  searchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  clearFilters: () => void;
  filterContact: string;
  setFilterContact: (value: string) => void;
  filterLocation: string;
  setFilterLocation: (value: string) => void;
  filterPriceFrom: string;
  setFilterPriceFrom: (value: string) => void;
  filterPriceTo: string;
  setFilterPriceTo: (value: string) => void;
  contactOptions: { value: string; label: string }[];
  locationOptions: { value: string; label: string }[];
}

const FilterArea: React.FC<FilterAreaProps> = ({
  filterType,
  setFilterType,
  filterStatus,
  filterStatusOptions,
  setFilterStatus,
  searchQuery,
  handleSearch,
  clearFilters,
  filterContact,
  setFilterContact,
  filterLocation,
  setFilterLocation,
  filterPriceFrom,
  setFilterPriceFrom,
  filterPriceTo,
  setFilterPriceTo,
  contactOptions,
  locationOptions,
}) => {
  const { t } = useLanguage();

  return (
    <div className="mb-4 sm:mb-6">
      <div className="rounded-lg bg-white p-3 shadow-sm dark:bg-gray-800 sm:p-4 lg:p-6">
        {/* Filter Header */}
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FaFilter className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <h3 className="text-sm font-medium text-gray-900 dark:text-white sm:text-base">
              {t("Filter Events")}
            </h3>
          </div>
          <button
            onClick={clearFilters}
            className="rounded-lg bg-red-500 px-3 py-1.5 text-xs font-medium text-white transition-colors hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 sm:px-4 sm:py-2 sm:text-sm"
          >
            {t("clear all filters")}
          </button>
        </div>

        <div className="grid gap-3 sm:gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {/* Search */}
          <div className="md:col-span-2 lg:col-span-3 xl:col-span-4">
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 sm:text-sm">
              {t("search")}
            </label>
            <div className="relative mt-1">
              <FiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-500 dark:text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={handleSearch}
                placeholder={t("search events, contacts, or locations")}
                className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-10 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-2.5"
              />
              {searchQuery && (
                <button
                  onClick={() =>
                    handleSearch({
                      target: { value: "" },
                    } as React.ChangeEvent<HTMLInputElement>)
                  }
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FaTimes className="h-3 w-3" />
                </button>
              )}
            </div>
          </div>

          {/* Filter by Type */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 sm:text-sm">
              {t("type")}
            </label>
            <div className="relative mt-1">
              <select
                value={filterType}
                onChange={(e) =>
                  setFilterType(e.target.value as "All" | "income" | "expense")
                }
                className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-2.5"
              >
                <option value="All">{t("all")}</option>
                <option value="income">{t("income")}</option>
                <option value="expense">{t("expense")}</option>
              </select>
              {filterType !== "All" && (
                <button
                  onClick={() => setFilterType("All")}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FaTimes className="h-3 w-3" />
                </button>
              )}
            </div>
          </div>

          {/* Filter by Status */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 sm:text-sm">
              {t("status")}
            </label>
            <div className="relative mt-1">
              <select
                value={filterStatus}
                onChange={(e) =>
                  setFilterStatus(
                    e.target.value as
                      | "All"
                      | "pending"
                      | "completed"
                      | "cancelled",
                  )
                }
                className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-2.5"
              >
                {filterStatusOptions.map((status) => (
                  <option key={status} value={status}>
                    {t(status)}
                  </option>
                ))}
              </select>
              {filterStatus !== "All" && (
                <button
                  onClick={() => setFilterStatus("All")}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FaTimes className="h-3 w-3" />
                </button>
              )}
            </div>
          </div>

          {/* Filter by Contact */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 sm:text-sm">
              {t("contact")}
            </label>
            <div className="relative mt-1">
              <Select
                placeholder={t("select...")}
                options={[
                  { value: "all", label: t("All") },
                  ...Array.from(
                    new Map(
                      contactOptions.map((option) => [option.value, option]),
                    ).values(),
                  ),
                ]}
                value={
                  filterContact === "all"
                    ? { value: "all", label: t("All") }
                    : contactOptions.find(
                        (option) => option.value === filterContact,
                      ) || null
                }
                onChange={(selectedOption) =>
                  setFilterContact(
                    selectedOption ? selectedOption.value : "all",
                  )
                }
                className="text-sm"
                classNamePrefix="react-select"
                isSearchable={true}
                isClearable={filterContact !== "all"}
                styles={{
                  control: (base) => ({
                    ...base,
                    minHeight: "38px",
                    fontSize: "14px",
                  }),
                }}
              />
            </div>
          </div>

          {/* Filter by Location */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 sm:text-sm">
              {t("location")}
            </label>
            <div className="relative mt-1">
              <Select
                placeholder={t("select...")}
                options={[
                  { value: "all", label: t("All") },
                  ...Array.from(
                    new Map(
                      locationOptions.map((option) => [option.value, option]),
                    ).values(),
                  ),
                ]}
                value={
                  filterLocation === "all"
                    ? { value: "all", label: t("All") }
                    : locationOptions.find(
                        (option) => option.value === filterLocation,
                      ) || null
                }
                onChange={(selectedOption) =>
                  setFilterLocation(
                    selectedOption ? selectedOption.value : "all",
                  )
                }
                className="text-sm"
                classNamePrefix="react-select"
                isSearchable={true}
                isClearable={filterLocation !== "all"}
                styles={{
                  control: (base) => ({
                    ...base,
                    minHeight: "38px",
                    fontSize: "14px",
                  }),
                }}
              />
            </div>
          </div>

          {/* Filter by Price From */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 sm:text-sm">
              {t("price from")}
            </label>
            <div className="relative mt-1">
              <input
                type="number"
                value={filterPriceFrom}
                onChange={(e) => setFilterPriceFrom(e.target.value)}
                className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-2.5"
                placeholder={t("min price")}
              />
              {filterPriceFrom && (
                <button
                  onClick={() => setFilterPriceFrom("")}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FaTimes className="h-3 w-3" />
                </button>
              )}
            </div>
          </div>

          {/* Filter by Price To */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 sm:text-sm">
              {t("price to")}
            </label>
            <div className="relative mt-1">
              <input
                type="number"
                value={filterPriceTo}
                onChange={(e) => setFilterPriceTo(e.target.value)}
                className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-2.5"
                placeholder={t("max price")}
              />
              {filterPriceTo && (
                <button
                  onClick={() => setFilterPriceTo("")}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FaTimes className="h-3 w-3" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterArea;

import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { EventDetails } from "@/lib/interfaces/finaces";
import { Contact } from "@/lib/types/contacts";
import { Location } from "@/lib/types/location";
import { Reservation } from "@/lib/interfaces/reservation";
import { Contract } from "@/lib/interfaces/contract";

const convertToArabicNumerals = (num: number | string | undefined | null) => {
    if (num === undefined || num === null) return "";
    const numStr = Math.floor(Number(num)).toString();
    return numStr.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[parseInt(d)]);
};

// Helper function to format currency in Arabic
const formatArabicCurrency = (amount: number | string | undefined | null, currency: string = "جنيه") => {
    if (amount === undefined || amount === null) return "";
    const arabicAmount = convertToArabicNumerals(amount);
    return `${arabicAmount} ${currency}`;
};

// Helper function to properly format Arabic text
const formatArabicText = (text: string) => {
    if (!text) return '';
    // Keep Arabic characters, spaces, digits, common punctuation, and currency symbols
    return text.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\.\,\-\+\%\$\€\£\/\:]/g, '').trim();
};

// Helper function to format dates properly in Arabic
const formatArabicDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const day = convertToArabicNumerals(date.getDate().toString().padStart(2, '0'));
    const month = convertToArabicNumerals((date.getMonth() + 1).toString().padStart(2, '0'));
    const year = convertToArabicNumerals(date.getFullYear());
    return `${day}/${month}/${year}`;
};

const formatLine = (label: string, value: string, language: string, pageWidth: number, doc: jsPDF, currentY: number) => {
    if (language === "ar") {
        // Clean and format Arabic text properly
        const cleanLabel = formatArabicText(label);
        const cleanValue = formatArabicText(value);

        // Format as "Value :Label" in Arabic (right-to-left reading)
        const formattedText = `${cleanValue} :${cleanLabel}`;
        doc.text(formattedText, pageWidth - 14, currentY, { align: "right" });
    } else {
        doc.text(`${label}: ${value}`, 14, currentY);
    }
};


const loadFont = async (fontUrl: string) => {
    const response = await fetch(fontUrl);
    const fontData = await response.arrayBuffer();
    const uint8Array = new Uint8Array(fontData);
    let binary = "";
    for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binary);
};

const loadLogo = async (logoUrl: string) => {
    try {
        const response = await fetch(logoUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch logo: ${response.status}`);
        }

        const logoData = await response.arrayBuffer();
        const uint8Array = new Uint8Array(logoData);

        // Validate PNG header
        if (uint8Array.length < 8 ||
            uint8Array[0] !== 0x89 || uint8Array[1] !== 0x50 ||
            uint8Array[2] !== 0x4E || uint8Array[3] !== 0x47) {
            throw new Error("Invalid PNG file format");
        }

        let binary = "";
        for (let i = 0; i < uint8Array.length; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }
        return btoa(binary);
    } catch (error) {
        console.error("Error loading logo:", error);
        return null;
    }
};

export const generatePDF = async (
  filteredEvents: EventDetails[],
  language: string,
  t: (key: string) => string,
  title: string,
  contact?: Contact,
  location?: Location,
  reservation?: Reservation,
  contract?: Contract,
  reservations?: Reservation[]
) => {
  await askLanguageAndGeneratePDF(filteredEvents, language, t, title, contact, location, reservation, contract, reservations);
};

// Function to open the PDF Generator Modal with advanced editing capabilities
export const openAdvancedPDFGenerator = (
  data: {
    events?: EventDetails[],
    contacts?: Contact[],
    locations?: Location[],
    reservations?: Reservation[],
    contracts?: Contract[]
  },
  title: string,
  mode: 'events' | 'contacts' | 'locations' | 'reservations' | 'contracts',
  onModalOpen: (modalProps: any) => void
) => {
  const modalProps = {
    isOpen: true,
    data,
    title,
    mode,
    onClose: () => onModalOpen({ isOpen: false })
  };
  onModalOpen(modalProps);
};

export const askLanguageAndGeneratePDF = async (
    filteredEvents: EventDetails[],
    language: string,
    t: (key: string) => string,
    title: string,
    contact?: Contact,
    location?: Location,
    reservation?: Reservation,
    contract?: Contract,
      reservations?: Reservation[]
) => {
    try {
        const doc = new jsPDF();
        const fontBase64 = await loadFont('/Amiri-Regular.ttf');
        doc.addFileToVFS('Amiri-Regular.ttf', fontBase64);
        doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
        doc.setFont('Amiri');

        const pageWidth = doc.internal.pageSize.getWidth();
        let currentY = 20;

        // Load and add company logo
        try {
            const logoBase64 = await loadLogo('/images/logo/saray-vera-logo.png');
            if (logoBase64) {
                const logoWidth = 40;
                const logoHeight = 20;
                const logoX = language === "ar" ? pageWidth - logoWidth - 14 : 14;
                doc.addImage(`data:image/png;base64,${logoBase64}`, 'PNG', logoX, currentY, logoWidth, logoHeight);
            }
        } catch (logoError) {
            console.warn("Could not load logo, continuing without it:", logoError);
            // Continue without logo if it fails to load
        }

        // Company name and document info
        doc.setFontSize(16);
        const companyName = "Saray Vera";
        const companyNameWidth = doc.getTextWidth(companyName);
        const companyNameX = language === "ar" ? 14 : pageWidth - companyNameWidth - 14;
        doc.text(companyName, companyNameX, currentY + 8);

        // Document creation date
        doc.setFontSize(10);
        const currentDate = new Date();
        const formattedDate = language === "ar"
            ? formatArabicDate(currentDate.toISOString())
            : currentDate.toLocaleDateString("en-US", {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              });
        const dateText = `${t("createdOn")}: ${formattedDate}`;
        const dateWidth = doc.getTextWidth(dateText);
        const dateX = language === "ar" ? 14 : pageWidth - dateWidth - 14;
        doc.text(dateText, dateX, currentY + 18);

        // Add a separator line
        currentY += 30;
        doc.setLineWidth(0.5);
        doc.line(14, currentY, pageWidth - 14, currentY);
        currentY += 10;

        // Report Title - Professional styling
        doc.setFontSize(20);
        doc.setFont('Amiri', 'bold');
        const reportTitle = title;
        const reportTitleWidth = doc.getTextWidth(reportTitle);
        const titleX = (pageWidth - reportTitleWidth) / 2; // Center the title
        doc.text(reportTitle, titleX, currentY);

        // Add underline to title
        doc.setLineWidth(0.3);
        doc.line(titleX, currentY + 2, titleX + reportTitleWidth, currentY + 2);

        // Reset font to normal
        doc.setFont('Amiri', 'normal');
        currentY += 15;

        // Contact
        if (contact) {
            doc.setFontSize(12);
            const entries = [
                [t("name"), contact.name],
                [t("email"), contact.email],
                [t("phone"), contact.phone],
                [t("company"), contact.company],
                contact.address && [t("address"), contact.address],
            ].filter(Boolean) as [string, string][];
            entries.forEach(([label, value]) => {
                formatLine(label, value, language, pageWidth, doc, currentY);
                currentY += 7;
            });
            currentY += 5;
        }

        // Location
        if (location) {
            doc.setFontSize(12);
            const entries = [
                [t("locationName"), location.name],
                [t("address"), location.address],
                [t("description"), location.description || "-"],
                [t("type"), t(location.type) || location.type],
                [t("status"), t(location.status) || location.status],
                [t("capacity"), language === "ar" ? convertToArabicNumerals(location.capacity) : String(location.capacity)],
                [t("ourPercentage"), language === "ar" ? `${convertToArabicNumerals(location.ourPercentage)}%` : `${location.ourPercentage}%`],
                [t("contactPerson"), location.contactPerson || "-"],
                [t("contactEmail"), location.contactEmail || "-"],
                [t("contactPhone"), location.contactPhone || "-"]
            ].filter(([, value]) => value && value !== "-");

            entries.forEach(([label, value]) => {
                formatLine(label, value, language, pageWidth, doc, currentY);
                currentY += 7;
            });
            currentY += 5;
        }

        // Reservation
        if (reservation) {
            doc.setFontSize(12);
            const entries = [
                reservation.title && [t("reservation title"), reservation.title],
                reservation.description && [t("description"), reservation.description],
                reservation.total_amount !== undefined && [t("total amount"), language === "ar" ? formatArabicCurrency(reservation.total_amount) : String(reservation.total_amount)],
                reservation.reservationDate && [t("reservation date"), language === "ar" ? formatArabicDate(reservation.reservationDate) : new Date(reservation.reservationDate).toLocaleDateString("en-US")],
                reservation.start_date && [t("start date"), language === "ar" ? formatArabicDate(reservation.start_date) : new Date(reservation.start_date).toLocaleDateString("en-US")],
                reservation.end_date && [t("end date"), language === "ar" ? formatArabicDate(reservation.end_date) : new Date(reservation.end_date).toLocaleDateString("en-US")],
                reservation.status && [t("status"), t(reservation.status)],
                reservation.adType && [t("ad type"), reservation.adType],
                reservation.adPlacement && [t("ad placement"), reservation.adPlacement],
                reservation.required_capacity !== undefined && [t("required capacity"), language === "ar" ? convertToArabicNumerals(reservation.required_capacity) : String(reservation.required_capacity)],
                reservation.notes && [t("notes"), reservation.notes],
            ].filter(Boolean) as [string, string][];
            entries.forEach(([label, value]) => {
                formatLine(label, value, language, pageWidth, doc, currentY);
                currentY += 7;
            });
            currentY += 5;
        }

        // Contract
        if (contract) {
            doc.setFontSize(12);
            const entries = [
                contract.contractNumber && [t("contract number"), contract.contractNumber],
                contract.title && [t("title"), contract.title],
                contract.description && [t("description"), contract.description],
                contract.start_date && [t("start date"), language === "ar" ? formatArabicDate(contract.start_date) : new Date(contract.start_date).toLocaleDateString("en-US")],
                contract.end_date && [t("end date"), language === "ar" ? formatArabicDate(contract.end_date) : new Date(contract.end_date).toLocaleDateString("en-US")],
                contract.status && [t("status"), t(contract.status)],
                contract.monthlyAmount !== undefined && [t("monthly amount"), language === "ar" ? formatArabicCurrency(contract.monthlyAmount) : String(contract.monthlyAmount)],
                contract.total_amount !== undefined && [t("total amount"), language === "ar" ? formatArabicCurrency(contract.total_amount) : String(contract.total_amount)],
                contract.paymentDay !== undefined && [t("payment day"), language === "ar" ? convertToArabicNumerals(contract.paymentDay) : String(contract.paymentDay)],
                contract.paymentStatus && [t("payment status"), t(contract.paymentStatus)],
                contract.notes && [t("notes"), contract.notes],
            ].filter(Boolean) as [string, string][];
            entries.forEach(([label, value]) => {
                formatLine(label, value, language, pageWidth, doc, currentY);
                currentY += 7;
            });
            currentY += 5;
        }
        
        if (reservations && reservations.length > 0) {
            currentY += 15;
            doc.setFontSize(14);
            const reservationTitle = t("Reservations List");
            const titleWidth = doc.getTextWidth(reservationTitle);
            doc.text(reservationTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
            currentY += 5;

            const reservationColumns = language === "ar"
                ? [t("title"), t("start date"), t("end date"), t("status"), t("amount")]
                : ["Title", "Start Date", "End Date", "Status", "Amount"];

            const reservationRows = reservations.map((res) => {
            //                 const formattedDate = language === "ar"
            // ? `${convertToArabicNumerals(dateObj.getDate())}/${convertToArabicNumerals(dateObj.getMonth() + 1)}/${convertToArabicNumerals(dateObj.getFullYear())}`
            // : dateObj.toLocaleDateString("en-US");
                const formatDate = (date: string) => {
                    return language === "ar" ? formatArabicDate(date) : new Date(date).toLocaleDateString("en-US");
                };
                return [
                    res.title || "-",
                    res.start_date ? formatDate(res.start_date) : "-",
                    res.end_date ? formatDate(res.end_date) : "-",
                    res.status ? t(res.status) : "-",
                    res.total_amount !== undefined
                        ? (language === "ar" ? formatArabicCurrency(res.total_amount) : res.total_amount.toString())
                        : "-"
                ];
            });

            autoTable(doc, {
                head: [reservationColumns],
                body: reservationRows,
                startY: currentY + 5,
                styles: {
                    halign: language === "ar" ? "right" : "left",
                    font: 'Amiri',
                    fontSize: 10,
                    cellPadding: 4,
                    lineColor: [200, 200, 200],
                    lineWidth: 0.1,
                },
                headStyles: {
                    font: 'Amiri',
                    fontStyle: 'bold',
                    halign: language === "ar" ? "right" : "left",
                    fillColor: [46, 204, 113], // Professional green for reservations
                    textColor: [255, 255, 255], // White text
                    fontSize: 11,
                },
                alternateRowStyles: {
                    fillColor: [248, 249, 250], // Light gray for alternate rows
                },
                columnStyles: {
                    0: { halign: language === "ar" ? "right" : "left" },
                    1: { halign: language === "ar" ? "right" : "center" },
                    2: { halign: language === "ar" ? "right" : "center" },
                    3: { halign: language === "ar" ? "right" : "center" },
                    4: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' }
                }
            });

            currentY = (doc as any).lastAutoTable.finalY + 10;
        }

        // Table Columns
        const tableColumn = language === "ar"
            ? [t("title"), t("date"), t("amount"), t("status"), t("priority")]
            : ["Title", "Date", "Amount", "Status", "Priority"];

        const incomeRows: any[] = [];
        const expenseRows: any[] = [];

        filteredEvents.forEach(event => {
            const formattedDate = language === "ar" ? formatArabicDate(event.dueDate) : new Date(event.dueDate).toLocaleDateString("en-US");
            const formattedAmount = language === "ar"
                ? formatArabicCurrency(Math.round(Number(event.amount) || 0))
                : Math.round(Number(event.amount) || 0).toString();

            const eventData = [
                event.title,
                formattedDate,
                formattedAmount,
                t(event.status),
                t(event.priority)
            ];
            if (event.category === "income") incomeRows.push(eventData);
            else if (event.category === "expense") expenseRows.push(eventData);
        });

        const totalIncome = Math.round(filteredEvents
            .filter(e => e.category === "income")
            .reduce((sum, e) => sum + (Number(e.amount) || 0), 0));

        const totalExpenses = Math.round(filteredEvents
            .filter(e => e.category === "expense")
            .reduce((sum, e) => sum + (Number(e.amount) || 0), 0));

        const totalIncomeText = language === "ar"
            ? `${formatArabicCurrency(totalIncome)} :${t("total income")}`
            : `${t("total income")}: ${totalIncome}`;
        const totalExpensesText = language === "ar"
            ? `${formatArabicCurrency(totalExpenses)} :${t("total expenses")}`
            : `${t("total expenses")}: ${totalExpenses}`;

        // Income Table
        if (incomeRows.length > 0) {
            doc.setFontSize(14);
            const incomeTitle = t("income events");
            const titleWidth = doc.getTextWidth(incomeTitle);
            doc.text(incomeTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
            currentY += 5;

            autoTable(doc, {
                head: [tableColumn],
                body: incomeRows,
                startY: currentY + 5,
                styles: {
                    halign: language === "ar" ? "right" : "left",
                    font: 'Amiri',
                    fontSize: 10,
                    cellPadding: 4,
                    lineColor: [200, 200, 200],
                    lineWidth: 0.1,
                },
                headStyles: {
                    font: 'Amiri',
                    fontStyle: 'bold',
                    halign: language === "ar" ? "right" : "left",
                    fillColor: [41, 128, 185], // Professional blue
                    textColor: [255, 255, 255], // White text
                    fontSize: 11,
                },
                alternateRowStyles: {
                    fillColor: [248, 249, 250], // Light gray for alternate rows
                },
                columnStyles: {
                    0: { halign: language === "ar" ? "right" : "left" },
                    1: { halign: language === "ar" ? "right" : "center" },
                    2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
                    3: { halign: language === "ar" ? "right" : "center" },
                    4: { halign: language === "ar" ? "right" : "center" }
                }
            });

            currentY = (doc as any).lastAutoTable.finalY + 10;
            const incomeTextWidth = doc.getTextWidth(totalIncomeText);
            doc.setFontSize(12);
            doc.text(totalIncomeText, language === "ar" ? pageWidth - incomeTextWidth - 14 : 14, currentY);
        }

        // Expense Table
        if (expenseRows.length > 0) {
            currentY += 15;
            doc.setFontSize(14);
            const expenseTitle = t("expense events");
            const titleWidth = doc.getTextWidth(expenseTitle);
            doc.text(expenseTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
            currentY += 5;

            autoTable(doc, {
                head: [tableColumn],
                body: expenseRows,
                startY: currentY + 5,
                styles: {
                    halign: language === "ar" ? "right" : "left",
                    font: 'Amiri',
                    fontSize: 10,
                    cellPadding: 4,
                    lineColor: [200, 200, 200],
                    lineWidth: 0.1,
                },
                headStyles: {
                    font: 'Amiri',
                    fontStyle: 'bold',
                    halign: language === "ar" ? "right" : "left",
                    fillColor: [231, 76, 60], // Professional red for expenses
                    textColor: [255, 255, 255], // White text
                    fontSize: 11,
                },
                alternateRowStyles: {
                    fillColor: [248, 249, 250], // Light gray for alternate rows
                },
                columnStyles: {
                    0: { halign: language === "ar" ? "right" : "left" },
                    1: { halign: language === "ar" ? "right" : "center" },
                    2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
                    3: { halign: language === "ar" ? "right" : "center" },
                    4: { halign: language === "ar" ? "right" : "center" }
                }
            });

            currentY = (doc as any).lastAutoTable.finalY + 10;
            const expenseTextWidth = doc.getTextWidth(totalExpensesText);
            doc.setFontSize(12);
            doc.text(totalExpensesText, language === "ar" ? pageWidth - expenseTextWidth - 14 : 14, currentY);
        }

        // Add professional footer
        const pageHeight = doc.internal.pageSize.getHeight();
        const footerY = pageHeight - 20;

        // Footer separator line
        doc.setLineWidth(0.3);
        doc.line(14, footerY - 5, pageWidth - 14, footerY - 5);

        // Footer content
        doc.setFontSize(8);
        const footerText = `© ${new Date().getFullYear()} Saray Vera - ${t("allRightsReserved")}`;
        const footerWidth = doc.getTextWidth(footerText);
        const footerX = (pageWidth - footerWidth) / 2;
        doc.text(footerText, footerX, footerY);

        // Page number
        const pageNumber = `${t("page")} 1`;
        const pageNumberWidth = doc.getTextWidth(pageNumber);
        const pageNumberX = language === "ar" ? 14 : pageWidth - pageNumberWidth - 14;
        doc.text(pageNumber, pageNumberX, footerY);

        doc.save(`${title}.pdf`);
    } catch (error) {
        console.error("Error generating PDF:", error);
    }
};

export async function batchTranslate(
  texts: string[], 
  to: string,
  options?: {
    chunkSize?: number,      // To handle massive texts
    fallbackServers?: string[] // Alternative servers
  }
) {
  const defaultServers = [
    'https://translate.argosopentech.com',
    'https://libretranslate.com'
  ];
  
  const servers = options?.fallbackServers || defaultServers;
  const chunkSize = options?.chunkSize || 500; // Characters per chunk

  try {
    // Chunk large texts to avoid server timeouts
    const chunks: string[][] = [];
    let currentChunk: string[] = [];
    let currentLength = 0;

    for (const text of texts) {
      if (currentLength + text.length > chunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk);
        currentChunk = [];
        currentLength = 0;
      }
      currentChunk.push(text);
      currentLength += text.length;
    }
    if (currentChunk.length > 0) chunks.push(currentChunk);

    // Try each server until one works
    for (const server of servers) {
      try {
        console.log(`Trying server: ${server}`);
        
        const results: string[] = [];
        for (const chunk of chunks) {
          const response = await fetch(`${server}/translate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              q: chunk.join('|||'),
              source: 'en',
              target: to,
              format: 'text',
            }),
          });

          if (!response.ok) throw new Error(`HTTP ${response.status}`);
          
          const data = await response.json();
          if (!data.translatedText) throw new Error('No translatedText in response');
          
          results.push(...data.translatedText.split('|||'));
        }
        
        return results;
      } catch (err) {
        console.warn(`Failed with server ${server}:`, err);
        continue; // Try next server
      }
    }

    throw new Error('All translation servers failed');
  } catch (error) {
    console.error('All translation attempts failed:', error);
    return texts; // Return original if all fails
  }
}
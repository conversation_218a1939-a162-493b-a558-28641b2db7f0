import React, { useState, useEffect } from "react";
import {
  Reservation,
  Installment,
  ReservationFormData,
} from "@/lib/interfaces/reservation";
import { EventDetails, Contact } from "@/lib/interfaces/finaces";
import { FaPlus, FaTrash, FaInfo<PERSON>ircle, Fa<PERSON>heck } from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import { useContacts } from "@/hooks/useContact";
import { usePermissions } from "@/hooks/usePermissions";
import { useLocations } from "@/hooks/useLocations";
import ReservationServices from "@/lib/reservations";
import { SuccessPopup } from "@/components/common/successPopUp";
import { ErrorPopup } from "@/components/common/errorPopUp";
import { LoadingComp } from "../common/Loading";
import { DataImporter } from "@/components/Importer/DataImporter";
import InstallmentsPopUp from "../Contracts/ContractForm/InstallmentsPopUp";

// Import separated components
import ReservationFormBasicInfo from "./ReservationFormBasicInfo";
import ReservationFormClientLocation from "./ReservationFormClientLocation";
import ReservationFormInstallments from "./ReservationFormInstallments";
import ReservationFormEnhancedInstallments from "./ReservationFormEnhancedInstallments";
import { useIncomeTypes } from "@/hooks/useIncomeTypes";
import { useExpenseTypes } from "@/hooks/useExpensesTypes";

interface ReservationFormProps {
  reservation?: Reservation; // Optional for editing existing reservation
  onSave: (reservation: Reservation, events: EventDetails[]) => void;
  onCancel: () => void;
}

const ReservationForm: React.FC<ReservationFormProps> = ({
  reservation,
  onSave,
  onCancel,
}) => {
  const { t, language } = useLanguage();
  const isEditing = !!reservation;
  const { hasPermission } = usePermissions();
  const reservationServices = ReservationServices();
  const { data: contacts, isLoading, isError } = useContacts();
  const {
    data: locationsData,
    isLoading: isLocationsLoading,
    isError: isLocationsError,
  } = useLocations();
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [showErrorPopup, setShowErrorPopup] = useState(false);
  const [message, setMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [isImporting, setIsImporting] = useState(false); // State for toggling import mode (only for new reservations)

  // Financial events state for local management
  const [incomeEvents, setIncomeEvents] = useState<EventDetails[]>([]);
  const [expenseEvents, setExpenseEvents] = useState<EventDetails[]>([]);

  // Fetch income and expense types for proper UUIDs
  const { data: incomeTypesData } = useIncomeTypes();
  const { data: expenseTypesData } = useExpenseTypes();

  // Helper functions to get type UUIDs
  const getIncomeTypeId = (typeName: string): string => {
    if (!incomeTypesData?.income_types) return "default-income-type-uuid";
    const type = incomeTypesData.income_types.find((t) => t.name === typeName);
    return (
      type?.id ||
      incomeTypesData.income_types[0]?.id ||
      "default-income-type-uuid"
    );
  };

  const getExpenseTypeId = (typeName: string): string => {
    if (!expenseTypesData?.event_types) return "default-expense-type-uuid";
    const type = expenseTypesData.event_types.find((t) => t.name === typeName);
    return (
      type?.id ||
      expenseTypesData.event_types[0]?.id ||
      "default-expense-type-uuid"
    );
  };

  // State for selected location details
  const [selectedLocation, setSelectedLocation] = useState<{
    id: string;
    totalCapacity: number;
    takenCapacity: number;
    availableCapacity: number;
  } | null>(null);

  const [formData, setFormData] = useState<ReservationFormData>({
    title: reservation?.title || "",
    description: reservation?.description || "",
    totalAmount: reservation?.total_amount || 0,
    startDate: reservation?.start_date
      ? reservation.start_date.split("T")[0]
      : "",
    endDate: reservation?.end_date ? reservation.end_date.split("T")[0] : "",
    contactId: reservation?.contactId || reservation?.contact?.id || "",
    locationId: reservation?.locationId || reservation?.location?.id || "",
    adType: reservation?.adType || "",
    adPlacement: reservation?.adPlacement || "",
    installmentCount: reservation?.installments?.length || 1,
    installmentType: "equal",
    installments:
      reservation?.installments?.map((inst) => ({
        amount: inst.amount,
        notificationDate: inst.notificationDate || "",
        priority: inst.priority || "medium",
        dueDate: inst.dueDate.split("T")[0],
        status: inst.status || "upcoming",
        received_date: inst.received_date
          ? inst.received_date.split("T")[0]
          : "",
      })) || [],
    notes: reservation?.notes || "",
    required_capacity: (() => {
      if (isEditing && reservation) {
        // When editing, use the reservation's required_capacity (which represents the capacity taken by this reservation)
        const requiredCapacity = reservation.required_capacity || 0;

        console.log("Capacity initialization for editing:", {
          requiredCapacity,
          reservationData: reservation,
        });

        return requiredCapacity;
      }
      // For new reservations, use required_capacity
      return (reservation as any)?.required_capacity || 0;
    })(),
  });

  // Enhanced useEffect to populate contact and location when editing
  useEffect(() => {
    if (isEditing && reservation) {
      console.log("Setting up editing data for reservation:", reservation);

      // Set contact data - try multiple sources
      let contactToSet = null;
      if (reservation.contact && reservation.contact.id) {
        // Use contact object directly from reservation
        contactToSet = reservation.contact;
      } else if (reservation.contactId && contacts?.contacts) {
        // Find contact by ID in contacts list
        contactToSet = contacts.contacts.find(
          (c) => c.id === reservation.contactId,
        );
      }

      if (contactToSet) {
        console.log("Setting selected contact:", contactToSet);
        setSelectedContact(contactToSet);
      }

      // Update formData with proper contact and location IDs
      setFormData((prev) => ({
        ...prev,
        contactId: reservation.contactId || reservation.contact?.id || "",
        locationId: reservation.locationId || reservation.location?.id || "",
      }));

      // Set location capacity info if we have location data
      if (reservation.location && formData.startDate && formData.endDate) {
        // Use reservation location data if available
        if (reservation.location.totalCapacity !== undefined) {
          updateLocationCapacityInfo(
            reservation.location.id,
            reservation.location.totalCapacity || 0,
            reservation.location.takenCapacity || 0,
          );
        }
      }

      // Initialize financial events if editing (we'll fetch them separately)
      // For now, initialize as empty arrays - they will be populated by the enhanced component
      setIncomeEvents([]);
      setExpenseEvents([]);
    }
  }, [isEditing, reservation, contacts?.contacts, locationsData?.locations]);

  // Initialize installments if editing
  useEffect(() => {
    if (
      !isEditing &&
      formData.installments.length === 0 &&
      formData.startDate &&
      formData.totalAmount > 0
    ) {
      generateEqualInstallments();
    }
  }, []);

  // Real-time validation effect
  useEffect(() => {
    if (
      formData.totalAmount > 0 &&
      (formData.installments.length > 0 || incomeEvents.length > 0)
    ) {
      const validation = validateTotalAmountWithIncomes();
      if (!validation.isValid) {
        console.warn("Total amount validation:", validation.message);
      }
    }
  }, [formData.totalAmount, formData.installments, incomeEvents]);

  // Update location capacity information when location changes
  const updateLocationCapacityInfo = (
    value: string,
    totalCapacity: number,
    takenCapacity: number,
  ) => {
    const availableCapacity = totalCapacity - takenCapacity;
    setSelectedLocation({
      id: value,
      totalCapacity: totalCapacity,
      takenCapacity: takenCapacity,
      availableCapacity,
    });

    // Reset required capacity when changing locations
    if (!isEditing) {
      setFormData((prev) => ({
        ...prev,
        requiredCapacity: 0,
      }));
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;

    // Note: Capacity validation removed - now only shows warnings, doesn't block input

    setFormData({
      ...formData,
      [name]: ["totalAmount", "installmentCount", "requiredCapacity"].includes(
        name,
      )
        ? Number(value)
        : value,
    });
  };

  // Validation function to ensure total amount equals sum of all incomes
  const validateTotalAmountWithIncomes = (): {
    isValid: boolean;
    message?: string;
  } => {
    // Calculate total from installments
    const installmentTotal = formData.installments.reduce(
      (sum, installment) => sum + installment.amount,
      0,
    );

    // Calculate total from additional income events (excluding deleted ones)
    const additionalIncomeTotal = incomeEvents
      .filter((event) => !event.is_deleted)
      .reduce((sum, event) => sum + event.amount, 0);

    const totalIncomeAmount = installmentTotal + additionalIncomeTotal;

    // Allow small floating point differences (0.01)
    const difference = Math.abs(formData.totalAmount - totalIncomeAmount);

    if (difference > 0.01) {
      return {
        isValid: false,
        message: t(
          `Total reservation amount (${formData.totalAmount}) must equal the sum of all income amounts (${totalIncomeAmount.toFixed(2)}). Difference: ${difference.toFixed(2)}`,
        ),
      };
    }

    return { isValid: true };
  };

  const generateEqualInstallments = () => {
    // Silent validation - simply return if values are invalid without showing alert
    if (
      formData.totalAmount <= 0 ||
      formData.installmentCount <= 0 ||
      !formData.startDate
    ) {
      return;
    }

    // Calculate the precise equal amount with decimals
    const exactAmount = formData.totalAmount / formData.installmentCount;

    // Format to 2 decimal places
    const formattedAmount = parseFloat(exactAmount.toFixed(2));

    // Calculate total after formatting to ensure we're not losing pennies due to rounding
    const totalAfterFormatting = formattedAmount * formData.installmentCount;

    // Calculate any remaining difference due to rounding
    const roundingDifference = parseFloat(
      (formData.totalAmount - totalAfterFormatting).toFixed(2),
    );

    const startDate = new Date(formData.startDate);

    const newInstallments = Array(formData.installmentCount)
      .fill(null)
      .map((_, index) => {
        const dueDate = new Date(startDate);
        dueDate.setMonth(dueDate.getMonth() + index);

        // Add the rounding difference to the last installment if needed
        const installmentAmount =
          index === formData.installmentCount - 1
            ? formattedAmount + roundingDifference
            : formattedAmount;

        return {
          amount: parseFloat(installmentAmount.toFixed(2)), // Ensure consistent decimal formatting
          dueDate: dueDate.toISOString().split("T")[0],
          status: "upcoming" as const,
          priority: "medium" as const,
          notificationDate: "",
          received_date: "", // Add received date field
        };
      });

    setFormData({
      ...formData,
      installments: newInstallments,
      installmentType: "equal",
    });
  };

  const handleInstallmentChange = (
    index: number,
    field: string,
    value: any,
  ) => {
    console.log(`Changing installment ${index}, field ${field} to:`, value);
    const updatedInstallments = [...formData.installments];
    updatedInstallments[index] = {
      ...updatedInstallments[index],
      [field]: field === "amount" ? Number(value) : value,
    };

    console.log("Updated installment:", updatedInstallments[index]);
    setFormData({
      ...formData,
      installments: updatedInstallments,
    });
  };

  const handleAddInstallment = () => {
    const lastInstallment =
      formData.installments[formData.installments.length - 1];
    const newDueDate = lastInstallment
      ? (() => {
          const date = new Date(lastInstallment.dueDate);
          date.setMonth(date.getMonth() + 1);
          return date.toISOString().split("T")[0];
        })()
      : formData.startDate;

    setFormData({
      ...formData,
      installments: [
        ...formData.installments,
        {
          amount: 0,
          dueDate: newDueDate,
          status: "upcoming",
          priority: "medium",
          notificationDate: "",
          received_date: "",
        },
      ],
      installmentType: "custom",
    });
  };

  const handleRemoveInstallment = (index: number) => {
    const updatedInstallments = formData.installments.filter(
      (_, i) => i !== index,
    );
    setFormData({
      ...formData,
      installments: updatedInstallments,
    });
  };

  const handleSaveNormal = async () => {
    const events: any[] = [];

    for (let i = 0; i < formData.installments.length; i++) {
      const installment = formData.installments[i];

      // Format due date as ISO string
      const dueDate = new Date(installment.dueDate);
      const formattedDueDate = dueDate.toISOString();

      // Format notification date
      let formattedNotificationDate = null;
      if (installment.notificationDate) {
        const notificationDate = new Date(installment.notificationDate);
        formattedNotificationDate = notificationDate.toISOString();
      }

      // Format received date if provided
      let formattedReceivedDate = undefined;
      if (installment.received_date) {
        const receivedDate = new Date(installment.received_date);
        formattedReceivedDate = receivedDate.toISOString();
      }

      const eventPayload: any = {
        amount: installment.amount,
        due_date: formattedDueDate,
        notification_view_date: formattedNotificationDate,
        status: installment.status,
        priority: installment.priority || "medium",
      };

      // Only add received_date if status is completed and date is provided
      if (installment.status === "completed" && formattedReceivedDate) {
        eventPayload.received_date = formattedReceivedDate;
      }

      events.push(eventPayload);
    }

    return events;
  };

  const handleSaveReservation = async (eventDetails: any[]) => {
    const response = await reservationServices.createReservation({
      contact_id: selectedContact?.id || "0",
      location_id: formData.locationId,
      reservation: {
        title: formData.title,
        description: formData.description,
        total_amount: formData.totalAmount,
        start_date: new Date(formData.startDate).toISOString(),
        end_date: new Date(formData.endDate).toISOString(),
        required_capacity: formData.required_capacity,
        status: reservation?.status || "active",
        notes: formData.notes,
      },
      income_events: eventDetails,
    });

    return response;
  };

  // Function to handle form submission for editing
  const handleSubmitEdit = async () => {
    if (!reservation?.id) return;

    // Check edit permission
    if (!hasPermission("reservations", "edit")) {
      console.error("User lacks edit permission for reservations");
      return;
    }

    setLoading(true);
    setErrorMessage("");
    setMessage("");

    try {
      // Create the update payload in the required format
      const updatePayload: any = {
        title: formData.title,
        description: formData.description,
        start_date: new Date(formData.startDate).toISOString(),
        end_date: new Date(formData.endDate).toISOString(),
        status: reservation.status,
        total_amount: formData.totalAmount,
        required_capacity: formData.required_capacity,
        contact_id: formData.contactId,
        location_id: formData.locationId,
        income_events: [],
        expense_events: [],
      };

      // Process income events from formData.installments
      const installmentIncomeEvents = formData.installments.map(
        (installment, index) => {
          const existingInstallment = reservation.installments?.[index];

          const incomeEvent: any = {
            title: `${formData.title} - Installment ${index + 1}`,
            description: `Payment installment ${index + 1}`,
            amount: installment.amount.toString(),
            due_date: new Date(installment.dueDate).toISOString(),
            status: installment.status,
            priority: installment.priority || "medium",
            type_id: getIncomeTypeId("Installment"),
            contact_id: formData.contactId,
            location_id: formData.locationId,
          };

          // If this is an existing installment, include the ID
          if (existingInstallment?.financialEventId) {
            incomeEvent.id = existingInstallment.financialEventId.toString();
          }

          // Add received_date if status is completed
          if (installment.status === "completed" && installment.received_date) {
            incomeEvent.received_date = new Date(
              installment.received_date,
            ).toISOString();
          }

          return incomeEvent;
        },
      );

      // Process additional income events from the financial events editor
      const additionalIncomeEvents = incomeEvents.map((event) => {
        const incomeEvent: any = {
          title: event.title,
          amount: event.amount.toString(),
          due_date: new Date(event.dueDate).toISOString(),
          status: event.status,
          priority: event.priority || "medium",
          description: event.description || "",
          type_id: getIncomeTypeId(event.type || "Payment"),
          contact_id: formData.contactId,
          location_id: formData.locationId,
        };

        // Only include ID if it's not a temporary ID (existing event)
        if (event.id && !event.id.startsWith("temp_")) {
          incomeEvent.id = event.id;
        }

        // Include received_date if the event is completed
        if (event.status === "completed") {
          incomeEvent.received_date = event.received_date
            ? new Date(event.received_date).toISOString()
            : new Date().toISOString();
        }

        // Include is_deleted if the event is marked for deletion
        if (event.is_deleted) {
          incomeEvent.is_deleted = true;
        }

        return incomeEvent;
      });

      // Combine installment income events with additional income events
      updatePayload.income_events = [
        ...installmentIncomeEvents,
        ...additionalIncomeEvents,
      ];

      // Process expense events from the financial events editor
      updatePayload.expense_events = expenseEvents.map((event) => {
        const expenseEvent: any = {
          title: event.title,
          amount: event.amount.toString(),
          due_date: new Date(event.dueDate).toISOString(),
          status: event.status,
          priority: event.priority || "medium",
          description: event.description || "",
          type_id: getExpenseTypeId(event.type || "Other"),
          contact_id: formData.contactId, // This could be vendor contact for expenses
          location_id: formData.locationId,
        };

        // Only include ID if it's not a temporary ID (existing event)
        if (event.id && !event.id.startsWith("temp_")) {
          expenseEvent.id = event.id;
        }

        // Include paid_date if the event is completed
        if (event.status === "completed") {
          expenseEvent.paid_date = event.paid_date
            ? new Date(event.paid_date).toISOString()
            : new Date().toISOString();
        }

        // Include is_deleted if the event is marked for deletion
        if (event.is_deleted) {
          expenseEvent.is_deleted = true;
        }

        // Include income_id if this expense is linked to an income
        if (event.income_id) {
          expenseEvent.income_id = event.income_id;
        }

        return expenseEvent;
      });

      // Mark removed installments as deleted
      if (
        reservation.installments &&
        reservation.installments.length > formData.installments.length
      ) {
        for (
          let i = formData.installments.length;
          i < reservation.installments.length;
          i++
        ) {
          const removedInstallment = reservation.installments[i];
          if (removedInstallment.financialEventId) {
            updatePayload.income_events.push({
              id: removedInstallment.financialEventId.toString(),
              is_deleted: true,
            });
          }
        }
      }

      console.log("Update payload:", JSON.stringify(updatePayload, null, 2));

      // Call the update API with the correct method
      const response = await reservationServices.updateReservationWithEvents(
        reservation.id,
        updatePayload,
      );

      setMessage(t("Reservation updated successfully!"));
      setShowSuccessPopup(true);
      setLoading(false);

      setTimeout(() => {
        onSave(response, []);
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error("Error updating reservation:", error);
      setErrorMessage(t("An error occurred while updating the reservation"));
      setShowErrorPopup(true);
    } finally {
      setLoading(false);
    }
  };

  // Function to check if capacity is available
  const isCapacityAvailable = () => {
    if (!selectedLocation || !formData.required_capacity) {
      return false;
    }
    return formData.required_capacity <= selectedLocation.availableCapacity;
  };

  // Function to check if form is valid for submission
  const isFormValid = () => {
    const basicValidation = formData.title &&
                           formData.startDate &&
                           formData.endDate &&
                           formData.locationId &&
                           formData.contactId &&
                           formData.required_capacity > 0;

    return basicValidation && isCapacityAvailable();
  };

  // Function to handle form submission
  const handleSubmit = async (reservation? : Reservation) => {
    // Check permissions before proceeding
    const requiredPermission = isEditing ? "edit" : "create";
    if (!hasPermission("reservations", requiredPermission)) {
      console.error(`User lacks ${requiredPermission} permission for reservations`);
      return;
    }

    if (isEditing) {
      await handleSubmitEdit();
    } else {
      setLoading(true);
      setErrorMessage("");
      setMessage("");

      try {
        // Validation
        if (!formData.title || !formData.startDate || !formData.endDate) {
          setErrorMessage(t("Please fill all required fields"));
          setLoading(false);
          setShowErrorPopup(true);
          return;
        }

        if (formData.installments.length === 0) {
          setErrorMessage(t("Please add at least one installment"));
          setLoading(false);
          setShowErrorPopup(true);
          return;
        }

        if (formData.installments.some((i) => i.amount <= 0)) {
          setErrorMessage(
            t("All installments must have an amount greater than zero"),
          );
          setLoading(false);
          setShowErrorPopup(true);
          return;
        }

        // Validate capacity selection
        if (formData.required_capacity <= 0) {
          setErrorMessage(t("Please specify the required capacity"));
          setLoading(false);
          setShowErrorPopup(true);
          return;
        }

        // Note: Capacity validation removed - now only shows warnings, doesn't block submission

        // Validate that total amount equals sum of all income amounts
        const validation = validateTotalAmountWithIncomes();
        if (!validation.isValid) {
          setErrorMessage(
            validation.message || t("Total amount validation failed"),
          );
          setLoading(false);
          setShowErrorPopup(true);
          return;
        }

        // Validate completed installments have received dates
        const completedInstallments = formData.installments.filter(
          (i) => i.status === "completed",
        );
        const missingReceivedDates = completedInstallments.some(
          (i) => !i.received_date,
        );

        if (missingReceivedDates) {
          setErrorMessage(
            t("Please provide received date for all completed installments"),
          );
          setLoading(false);
          setShowErrorPopup(true);
          return;
        }

        // Create financial events for installments
        const installmentEvents: EventDetails[] = formData.installments.map(
          (installment, index) => {
            const existingEventId =
              reservation?.installments[index].financialEventId;

            // Format dates as ISO strings
            const dueDate = new Date(installment.dueDate);
            const formattedDueDate = dueDate.toISOString();

            let formattedReceivedDate = undefined;
            if (installment.received_date) {
              const receivedDate = new Date(installment.received_date);
              formattedReceivedDate = receivedDate.toISOString();
            }

            // Map cancelled status to pending for EventDetails compatibility
            const eventStatus:
              | "completed"
              | "pending"
              | "upcoming"
              | "overdue" =
              installment.status === "cancelled"
                ? "pending"
                : installment.status;

            return {
              id: existingEventId
                ? String(existingEventId)
                : String(Date.now() + index),
              title: `${formData.title} - Installment ${index + 1}`,
              amount: installment.amount,
              dueDate: formattedDueDate,
              notificationDate: installment.notificationDate,
              status: eventStatus,
              received_date: formattedReceivedDate,
              category: "income",
              type: "Advertisement",
              priority: installment.priority || "medium",
              lastEdited: new Date().toISOString(),
              lastEditedBy: "Admin",
              editingHistory: [],
              contact: contacts?.contacts?.find(
                (c) => c.id === formData.contactId,
              ) || {
                id: "0",
                name: "",
                email: "",
                phone: "",
              },
              location: locationsData?.locations?.find(
                (l) => l.id === formData.locationId,
              ) || {
                id: "0",
                name: "",
                address: "",
                city: "",
                country: "",
                postalCode: "",
              },
            };
          },
        );

        // Create the reservation object
        const selectedContact = contacts?.contacts?.find(
          (c) => c.id === formData.contactId,
        );
        const selectedLocationInfo = locationsData?.locations?.find(
          (l) => l.id === formData.locationId,
        );

        const newReservation: Reservation = {
          id: reservation?.id || Date.now().toString(),
          title: formData.title,
          description: formData.description,
          total_amount: formData.totalAmount,
          reservationDate: new Date().toISOString(),
          start_date: new Date(formData.startDate).toISOString(),
          end_date: new Date(formData.endDate).toISOString(),
          status: "pending",
          contactId: selectedContact?.id || "0",
          contact: selectedContact || {
            id: "0",
            name: "",
            email: "",
            phone: "",
          },
          locationId: formData.locationId,
          location: selectedLocationInfo || {
            id: "0",
            name: "",
            address: "",
            city: "",
            country: "",
            postalCode: "",
          },
          adType: formData.adType,
          adPlacement: formData.adPlacement,
          required_capacity: formData.required_capacity,
          installments: formData.installments.map((installment, index) => ({
            id: reservation?.installments[index]?.id || Date.now() + index,
            amount: installment.amount,
            dueDate: new Date(installment.dueDate).toISOString(),
            status: installment.status,
            financialEventId: Number(installmentEvents[index].id),
            notificationDate: installment.notificationDate,
            priority: installment.priority || "medium",
            received_date: installment.received_date
              ? new Date(installment.received_date).toISOString()
              : undefined,
          })),
          created_at: reservation?.created_at || new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by: reservation?.created_by || "Admin",
          notes: formData.notes,
        };

        const installmentEventsToSave = await handleSaveNormal();
        const response = await handleSaveReservation(installmentEventsToSave);

        setMessage(t("Reservation created successfully!"));
        setShowSuccessPopup(true);
        setLoading(false);
        console.log("Reservation created successfully:", newReservation);

        setTimeout(() => {
          onSave(newReservation, installmentEvents);
          window.location.reload();
        }, 2000);
      } catch (error) {
        console.error("Error submitting reservation:", error);
        setErrorMessage(t("An error occurred while saving the reservation"));
        setShowErrorPopup(true);
      } finally {
        setLoading(false);
      }
    }
  };

  // Enhanced location capacity fetching for editing mode
  useEffect(() => {
    const fetchLocationDetails = async () => {
      if (formData.startDate && formData.endDate && formData.locationId) {
        try {
          const locationDetails =
            await reservationServices.getReservationsByLocation(
              formData.locationId,
              formData.startDate,
              formData.endDate,
            );

          if (locationDetails?.location) {
            updateLocationCapacityInfo(
              formData.locationId,
              locationDetails.location.totalCapacity,
              locationDetails.location.takenCapacity,
            );
          }
        } catch (error) {
          console.error("Error fetching location details:", error);
          // For editing mode, don't show alert, just log the error
          if (!isEditing) {
            alert("Failed to fetch location details. Please try again.");
          }
        }
      }
    };

    fetchLocationDetails();
  }, [formData.startDate, formData.endDate, formData.locationId, isEditing]);

  // Debug logging for editing mode
  useEffect(() => {
    if (isEditing && reservation) {
      console.log("Editing reservation data:", {
        reservation,
        contactId: reservation.contactId,
        contact: reservation.contact,
        locationId: reservation.locationId,
        location: reservation.location,
        selectedContact,
        formData: formData.contactId,
      });
    }
  }, [isEditing, reservation, selectedContact, formData.contactId]);

  const handleImport = async (importedReservations: any[]) => {
    // Check import permission before proceeding
    if (!hasPermission("reservations", "import_data")) {
      console.error("User lacks import_data permission for reservations");
      return;
    }

    console.log("Imported Reservations:", importedReservations);

    importedReservations.forEach(async (reservationData) => {
      const newReservation = {
        title: reservationData.title || "",
        description: reservationData.description || "",
        startDate: reservationData.startDate || "",
        endDate: reservationData.endDate || "",
        totalAmount: reservationData.amount || 0,
        contactId:
          contacts?.contacts.find((c) => c.name === reservationData.contact)
            ?.id || "",
        locationId:
          locationsData?.locations.find(
            (l) => l.name === reservationData.location,
          )?.id || "",
        adType: reservationData.adType || "",
        adPlacement: reservationData.adPlacement || "",
        requiredCapacity: reservationData.requiredCapacity || 0,
        installments: reservationData.installments || [],
        notes: reservationData.notes || "",
      };

      try {
        const response = await reservationServices.createReservation({
          reservation: {
            title: newReservation.title,
            description: newReservation.description,
            total_amount: Math.floor(newReservation.totalAmount),
            start_date: newReservation.startDate,
            end_date: newReservation.endDate,
            required_capacity: Math.floor(newReservation.requiredCapacity),
            notes: newReservation.notes,
            status: "pending",
          },
          contact_id: newReservation.contactId || "",
          location_id: newReservation.locationId || "",
          income_events: newReservation.installments.map((inst: any) => ({
            title: `${newReservation.title} - Installment`,
            amount: Math.floor(inst.amount),
            due_date: inst.dueDate,
            paid_date: null,
            description: "",
            status: "upcoming",
            priority: inst.priority || "medium",
            contact_id: newReservation.contactId || "",
            location_id: newReservation.locationId || "",
            notification_date: inst.notificationDate || null,
            autoCreate: true,
          })),
        });
        console.log("Reservation imported successfully:", response);
      } catch (error) {
        console.error("Error importing reservation:", error);
      }
    });

    setMessage(t("Reservations imported successfully"));
    setShowSuccessPopup(true);
  };

  return isLoading || isLocationsLoading || loading ? (
    <div className="flex h-screen items-center justify-center">
      <LoadingComp />
    </div>
  ) : isError || isLocationsError ? (
    <div className="text-center text-red-500">{t("Error loading data")}</div>
  ) : (
    <>
      {showSuccessPopup && (
        <SuccessPopup
          message={message}
          onClose={() => setShowSuccessPopup(false)}
        />
      )}
      {showErrorPopup && (
        <ErrorPopup
          message={errorMessage}
          onClose={() => setShowErrorPopup(false)}
        />
      )}

      <div className="mx-auto max-w-6xl rounded-lg bg-white p-4 shadow dark:bg-gray-800 sm:p-6">
        {/* Header */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white sm:text-2xl">
              {isEditing ? t("Edit Reservation") : t("Create New Reservation")}
            </h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t("Fill in the details below")}
            </p>
          </div>
          {!isEditing && hasPermission("reservations", "import_data") && (
            <button
              type="button"
              onClick={() => setIsImporting(!isImporting)}
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 sm:w-auto"
            >
              <FaPlus className="h-4 w-4" />
              {isImporting ? t("Close Import") : t("Import Reservations")}
            </button>
          )}
        </div>

        {!isEditing && isImporting ? (
          <DataImporter<ReservationFormData>
            columns={[
              {
                key: "title",
                label: t("title"),
                type: "text",
                required: true,
              },
              {
                key: "amount",
                label: t("amount"),
                type: "text",
                required: true,
              },
              {
                key: "requiredCapacity",
                label: t("requiredCapacity"),
                type: "text",
                required: true,
              },
              {
                key: "contact",
                label: t("contact"),
                type: "dropdown",
                required: false,
                options: [...new Set(contacts?.contacts.map((c) => c.name) || [])],
              },
              {
                key: "location",
                label: t("location"),
                type: "dropdown",
                required: false,
                options: [...new Set(locationsData?.locations.map((l) => l.name) || [])],
              },
              {
                key: "startDate",
                label: t("startDate"),
                type: "date",
                required: false,
              },
              {
                key: "endDate",
                label: t("endDate"),
                type: "date",
                required: false,
              },
              {
                key: "installments",
                label: t("installments"),
                type: "modal",
                required: false,
                component: InstallmentsPopUp,
              },
            ]}
            onImport={handleImport}
          />
        ) : (
          <div className="space-y-6">
            {/* Basic Information Section */}
            <ReservationFormBasicInfo
              formData={formData}
              handleInputChange={handleInputChange}
            />

            {/* Client and Location Section */}
            <ReservationFormClientLocation
              formData={formData}
              selectedContact={selectedContact}
              setSelectedContact={setSelectedContact}
              selectedLocation={selectedLocation}
              contacts={contacts?.contacts}
              locationList={locationsData?.locations}
              handleInputChange={handleInputChange}
              language={language}
              t={t}
              reservation={reservation} // Pass the reservation object
            />

            {/* Payment Installments Section */}
            {isEditing ? (
              <ReservationFormEnhancedInstallments
                formData={formData}
                setFormData={setFormData}
                t={t}
                reservationId={reservation?.id}
                incomeEvents={incomeEvents}
                expenseEvents={expenseEvents}
                onIncomeEventsChange={setIncomeEvents}
                onExpenseEventsChange={setExpenseEvents}
                validateTotalAmount={validateTotalAmountWithIncomes}
              />
            ) : (
              <ReservationFormInstallments
                formData={formData}
                handleInputChange={handleInputChange}
                handleAddInstallment={handleAddInstallment}
                handleRemoveInstallment={handleRemoveInstallment}
                handleInstallmentChange={handleInstallmentChange}
                generateEqualInstallments={generateEqualInstallments}
                t={t}
              />
            )}

            {/* Notes Section */}
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900 sm:p-6">
              <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-gray-200">
                {t("Additional Notes")}
              </h3>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={4}
                className="block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                placeholder={t("Enter any additional notes or comments...")}
              />
            </div>

            {/* Validation Status Message */}
            {(() => {
              const validation = validateTotalAmountWithIncomes();
              return (
                !validation.isValid && (
                  <div className="rounded-md bg-red-50 p-3 dark:bg-red-900/20">
                    <div className="flex">
                      <FaInfoCircle className="h-5 w-5 text-red-400" />
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                          {t("Cannot Update Reservation")}
                        </h3>
                        <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                          <p>
                            {t(
                              "Please fix the total amount validation error before updating the reservation.",
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              );
            })()}

            {/* Action Buttons */}
            <div className="flex flex-col-reverse gap-3 border-t border-gray-200 pt-6 dark:border-gray-700 sm:flex-row sm:justify-end">
              <button
                type="button"
                onClick={onCancel}
                className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 sm:w-auto"
              >
                {t("Cancel")}
              </button>
              <button
                type="button"
                onClick={() => handleSubmit(reservation)}
                disabled={loading || !validateTotalAmountWithIncomes().isValid}
                title={
                  !validateTotalAmountWithIncomes().isValid
                    ? t("Fix total amount validation to enable update")
                    : ""
                }
                className="flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 sm:w-auto"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div
                      className={`h-4 w-4 animate-spin rounded-full border-b-2 border-white ${language === "ar" ? "ml-2" : "mr-2"}`}
                    ></div>
                    {t("Processing...")}
                  </div>
                ) : (
                  <div
                    className={`flex items-center ${language === "ar" ? "flex-row-reverse" : ""}`}
                  >
                    <FaCheck
                      className={`h-4 w-4 ${language === "ar" ? "ml-2" : "mr-2"}`}
                    />
                    {isEditing
                      ? t("Update Reservation")
                      : t("Create Reservation")}
                  </div>
                )}
              </button>

              {/* Capacity validation message */}
              {!isCapacityAvailable() && selectedLocation && formData.required_capacity > 0 && (
                <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {t("Cannot create reservation: Required capacity exceeds available capacity")}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ReservationForm;

import { AxiosInstance } from "axios";

import { EventDetails } from "./interfaces/finaces";
import apiClient from "./api/apiClient";
import { update } from "@react-spring/web";
import { toast } from "react-hot-toast";
import {
  UpdateIncomePayload,
  UpdateIncomeStatusPayload,
  IncomeEditResponse,
  IncomeEditError,
} from "@/types/income";

export interface CreateIncomePayload {
  title: string;
  amount: number;
  due_date: string;
  received_date: string | null;
  description: string;
  status: string;
  priority: string;
  reservation_id?: string;
  contact_id: string;
  location_id: string;
  notification_date: string | null;
  autoCreate: boolean;
  isTotalAmount?: boolean;
}

interface CreateParentIncomePayload {
  title: string;
  amount: number;
  description: string;
  due_date: string | null;
  notification_view_date: string | null;
  priority: string;
  type_id: string;
  parent?: {
    title: string;
    amount: number;
    description: string;
  };
  children?: {
    amount: number;
    due_date: string | null;
    notification_view_date: string | null;
  }[];
}

const IncomeServices = () => ({
  getIncomes: async (signal?: AbortSignal): Promise<EventDetails[]> => {
    try {
      console.log("Making API call to fetch incomes...");
      const response = await apiClient.get(`/income/api/listall/`, {
        signal: signal,
      });
      console.log("Raw income API response:", response);

      if (!response.data) {
        console.error("Income API response has no data property");
        return [];
      }

      // Check for 'incomes' (plural) first, then fall back to 'income' (singular)
      if (response.data.incomes) {
        console.log("Found incomes property in response");
        return response.data.incomes as EventDetails[];
      } else if (response.data.income) {
        console.log("Found income property in response");
        return response.data.income as EventDetails[];
      }

      // If neither property exists, check if response.data is an array directly
      if (Array.isArray(response.data)) {
        console.log("Response data is an array, returning directly");
        return response.data;
      }

      console.error(
        "Could not find income data in the response:",
        response.data,
      );
      return [];
    } catch (error) {
      if (
        error instanceof Error &&
        (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
      ) {
        console.log("Incomes API request was cancelled");
        return [];
      }
      console.error("Error fetching income:", error);
      throw error;
    }
  },

  // New function to get incomes by location ID
  getIncomesByLocation: async (
    locationId: string | number,
    signal?: AbortSignal,
  ): Promise<EventDetails[]> => {
    try {
      console.log(
        `Making API call to fetch incomes for location ID: ${locationId}...`,
      );
      const response = await apiClient.post(
        "/income/api/by-location/",
        { location_id: locationId },
        { signal: signal },
      );
      console.log("Raw income by location API response:", response);

      if (!response.data) {
        console.error("Income by location API response has no data property");
        return [];
      }

      // Check for 'incomes' (plural) first, then fall back to 'income' (singular)
      if (response.data.incomes) {
        console.log("Found incomes property in response");
        return response.data.incomes as EventDetails[];
      } else if (response.data.income) {
        console.log("Found income property in response");
        return response.data.income as EventDetails[];
      }

      // If neither property exists, check if response.data is an array directly
      if (Array.isArray(response.data)) {
        console.log("Response data is an array, returning directly");
        return response.data;
      }

      console.error(
        "Could not find income data in the response:",
        response.data,
      );
      return [];
    } catch (error) {
      if (
        error instanceof Error &&
        (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
      ) {
        console.log(
          `Incomes by location API request was cancelled for location: ${locationId}`,
        );
        return [];
      }
      console.error("Error fetching income by location:", error);
      throw error;
    }
  },

  // New function to get incomes by contact ID
  getIncomesByContact: async (
    contactId: string | number,
  ): Promise<EventDetails[]> => {
    try {
      console.log(
        `Making API call to fetch incomes for contact ID: ${contactId}...`,
      );
      const response = await apiClient.post("/income/api/by-contact/", {
        contact_id: contactId,
      });
      console.log("Raw income by contact API response:", response);

      if (!response.data) {
        console.error("Income by contact API response has no data property");
        return [];
      }

      // Check for 'incomes' (plural) first, then fall back to 'income' (singular)
      if (response.data.incomes) {
        console.log("Found incomes property in response");
        return response.data.incomes as EventDetails[];
      } else if (response.data.income) {
        console.log("Found income property in response");
        return response.data.income as EventDetails[];
      }

      // If neither property exists, check if response.data is an array directly
      if (Array.isArray(response.data)) {
        console.log("Response data is an array, returning directly");
        return response.data;
      }

      console.error(
        "Could not find income data in the response:",
        response.data,
      );
      return [];
    } catch (error) {
      console.error("Error fetching income by contact:", error);
      throw new Error("Error fetching income by contact");
    }
  },

  createIncome: async (payload: CreateIncomePayload): Promise<EventDetails> => {
    try {
      const response = await apiClient.post("/income/api/create/", payload);
      return response.data as EventDetails;
    } catch (error) {
      console.error("Error creating expense:", error);
      throw new Error("Error creating expense");
    }
  },

  createParentIncome: async (
    payload: CreateParentIncomePayload,
  ): Promise<EventDetails> => {
    try {
      const response = await apiClient.post(
        "/income/api/parent/create-with-children/",
        payload,
      );
      return response.data as EventDetails;
    } catch (error) {
      console.error("Error creating parent expense:", error);
      throw new Error("Error creating parent expense");
    }
  },

  updateIncome: async (
    id: string | number,
    payload: CreateIncomePayload,
  ): Promise<EventDetails> => {
    try {
      const response = await apiClient.post(
        `/income/api/update/${id}/`,
        payload,
      );
      return response.data as EventDetails;
    } catch (error) {
      console.error("Error updating income:", error);
      throw new Error("Error updating income");
    }
  },

  createMultipleIncomes: async (
    payload: CreateIncomePayload[],
  ): Promise<EventDetails[]> => {
    try {
      const body = {
        incomes: payload,
      };
      const response = await apiClient.post(
        "/income/api/createMultiple/",
        body,
      );
      return response.data as EventDetails[];
    } catch (error) {
      console.error("Error creating multiple incomes:", error);
      throw new Error("Error creating multiple incomes");
    }
  },

  softDeleteincome: async (expenseId: string): Promise<void> => {
    try {
      console.log("Sending soft delete request for income:", expenseId);
      // Show loading indicator or buffer before the request
      const loadingToast = toast.loading("Deleting income...");

      try {
        const response = await apiClient.delete(
          `/income/api/softdelete/${expenseId}/`,
        );

        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Check response status
        if (response.status === 200) {
          toast.success("Income has been deleted successfully");
        }
      } catch (error: any) {
        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Handle 409 conflict error specifically
        if (error.response && error.response.status === 409) {
          console.log("409 error data:", error.response.data);

          if (error.response.data.warning) {
            toast.error(error.response.data.warning);
          } else if (error.response.data.message) {
            toast.error(error.response.data.message);
          } else {
            toast.error("Conflict occurred while deleting income");
          }
        } else {
          // Handle other errors
          toast.error("Failed to delete income. Please try again.");
          console.error("Error soft deleting income:", error);
        }
        throw error; // Re-throw for the caller to handle if needed
      }
    } catch (error) {
      console.error("Error soft deleting income:", error);
      throw error;
    }
  },

  updateIncomeStatus: async (
    incomeId: string | number,
    payload: string | { status: string; received_date?: string },
  ): Promise<EventDetails> => {
    try {
      const requestBody =
        typeof payload === "string"
          ? { income_id: incomeId, status: payload }
          : { income_id: incomeId, ...payload };

      const response = await apiClient.post(
        "/income/api/updatestatus/",
        requestBody,
      );
      console.log("Income status updated:", response.data);
      return response.data as EventDetails;
    } catch (error) {
      console.error("Error updating income status:", error);
      throw new Error("Error updating income status");
    }
  },

  // New edit income function based on API specifications
  editIncome: async (
    incomeId: string,
    payload: UpdateIncomePayload,
  ): Promise<IncomeEditResponse> => {
    try {
      const response = await apiClient.put(
        `/income/api/updateincome/${incomeId}/`,
        payload,
      );
      console.log("Income edited successfully:", response.data);
      return response.data as IncomeEditResponse;
    } catch (error: any) {
      console.error("Error editing income:", error);

      // Handle 409 conflict error (linked to reservation/contract)
      if (error.response && error.response.status === 409) {
        const errorData = error.response.data as IncomeEditError;
        throw errorData;
      }

      // Handle 400 validation error
      if (error.response && error.response.status === 400) {
        const errorData = error.response.data as IncomeEditError;
        throw errorData;
      }

      // Handle 404 not found
      if (error.response && error.response.status === 404) {
        throw new Error("Income not found");
      }

      throw new Error("Error editing income");
    }
  },

  // New edit income status only function
  editIncomeStatus: async (
    payload: UpdateIncomeStatusPayload,
  ): Promise<IncomeEditResponse> => {
    try {
      const response = await apiClient.post(
        "/income/api/updatestatus/",
        payload,
      );
      console.log("Income status edited successfully:", response.data);
      return response.data as IncomeEditResponse;
    } catch (error: any) {
      console.error("Error editing income status:", error);

      // Handle validation errors
      if (error.response && error.response.status === 400) {
        const errorData = error.response.data as IncomeEditError;
        throw errorData;
      }

      throw new Error("Error editing income status");
    }
  },
});

export default IncomeServices;

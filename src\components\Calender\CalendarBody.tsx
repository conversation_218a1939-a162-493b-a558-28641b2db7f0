import React from "react";
import CalendarDay from "./CalendarDay";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage"; // Add this import

interface CalendarBodyProps {
  days: number[];
  year: number;
  month: number;
  viewType: string;
  events: EventDetails[];
  onDayClick: (day: number) => void;
}

const CalendarBody: React.FC<CalendarBodyProps> = ({
  days,
  year,
  month,
  viewType,
  events,
  onDayClick,
}) => {
  const { t } = useLanguage(); // Use your translation hook

  const weeks = [];
  let week: JSX.Element[] = [];

  days.forEach((day, index) => {
    const date = new Date(year, month, day);
    // Filter events for this specific day
    const dayEvents = events.filter((event) => {
      if (!event.dueDate) return false;
      const eventDate = new Date(event.dueDate);
      return (
        eventDate.getFullYear() === date.getFullYear() &&
        eventDate.getMonth() === date.getMonth() &&
        eventDate.getDate() === date.getDate()
      );
    });

    week.push(
      <CalendarDay
        key={index}
        day={day}
        year={year}
        viewType={viewType}
        events={dayEvents}
        onDayClick={onDayClick}
      />,
    );

    if (week.length === 7) {
      weeks.push(<tr key={weeks.length}>{week}</tr>);
      week = [];
    }
  });

  // Fill the last week with empty cells if necessary
  if (week.length > 0) {
    while (week.length < 7) {
      week.push(
        <td
          key={`empty-${week.length}`}
          className="h-16 border border-stroke dark:border-strokedark sm:h-20 md:h-24 lg:h-28 xl:h-32"
        ></td>,
      );
    }
    weeks.push(<tr key={weeks.length}>{week}</tr>);
  }

  // Use translation for day names
  const dayNames = [
    t("Sun"),
    t("Mon"),
    t("Tue"),
    t("Wed"),
    t("Thu"),
    t("Fri"),
    t("Sat"),
  ];

  return (
    <div className="w-full">
      <table className="w-full table-fixed">
        <thead>
          <tr className="bg-gray-500 text-white dark:bg-gray-900">
            {dayNames.map((day, index) => (
              <th
                key={day}
                className={`h-10 p-1 text-xs font-semibold sm:h-12 sm:p-2 sm:text-sm md:text-base lg:h-14 lg:p-3 xl:h-16 xl:p-4 ${
                  index < 6
                    ? "border-r border-gray-300 dark:border-gray-700"
                    : ""
                }`}
              >
                <span className="block truncate">{day}</span>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>{weeks}</tbody>
      </table>
    </div>
  );
};

export default CalendarBody;

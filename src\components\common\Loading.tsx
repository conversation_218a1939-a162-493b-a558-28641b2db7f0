import useLanguage from "@/hooks/useLanguage";

export const LoadingComp = () => {
  const { language } = useLanguage();
  const isRTL = language === "ar";

  return (
    <div className={`flex gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
      {[...Array(3)].map((_, i) => (
        <div
          key={i}
          className="h-3 w-3 animate-pulse rounded-full bg-blue-500"
          style={{ animationDelay: `${i * 0.2}s` }}
        />
      ))}
    </div>
  );
};

// Enhanced loading component with text support
export const LoadingWithText = ({ text }: { text?: string }) => {
  const { language, t } = useLanguage();
  const isRTL = language === "ar";

  return (
    <div
      className={`flex items-center gap-3 ${isRTL ? "flex-row-reverse text-right" : "text-left"}`}
    >
      <LoadingComp />
      <span className="text-sm text-gray-600 dark:text-gray-400">
        {text || t("loading")}
      </span>
    </div>
  );
};

// Spinner loading component
export const LoadingSpinner = ({
  size = "md",
}: {
  size?: "sm" | "md" | "lg";
}) => {
  const { language } = useLanguage();
  const isRTL = language === "ar";

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
  };

  return (
    <div className={`flex justify-center ${isRTL ? "rtl" : "ltr"}`}>
      <div
        className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-gray-300 border-t-blue-600`}
      ></div>
    </div>
  );
};

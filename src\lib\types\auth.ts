
export type PermissionCategory = {
    sidebar: boolean;
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
    export: boolean;
    import_data: boolean;
    approve: boolean;
    reject: boolean;
    analytics: boolean;
    notifications: boolean;
    view_history: boolean;
    manage_accounts?: boolean;
    view_activity_log?: boolean;
    view_terms?: boolean;
    manage_templates?: boolean;
  };
  
  export type Permissions = {
    reservations: PermissionCategory;
    dashboard: PermissionCategory;
    financials: PermissionCategory;
    contacts: PermissionCategory;
    settings: PermissionCategory;
    users: PermissionCategory;
    contracts: PermissionCategory;
    locations: PermissionCategory;
    income: PermissionCategory;
    expenses: PermissionCategory;
    calendar: PermissionCategory;
  };
  
  export type User = {
    id: number;
    email: string;
    role: 'ADMIN' | 'USER' | 'MANAGER'; 
  };
  
  export type AuthSession = {
    user: User;
    accessToken: string;
    refreshToken: string;
    permissions: Permissions;
  };
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { EventDetails } from "@/lib/interfaces/finaces";
import { Contact } from "@/lib/types/contacts";
import { Location } from "@/lib/types/location";
import { Reservation } from "@/lib/interfaces/reservation";
import { Contract } from "@/lib/interfaces/contract";

interface PDFField {
  key: string;
  label: string;
  value: string;
  visible: boolean;
  editable: boolean;
  type: 'text' | 'number' | 'date' | 'currency' | 'textarea';
  category: string;
}

interface PDFOptions {
  language: 'ar' | 'en';
  includeCompanyInfo: boolean;
  includeLogo: boolean;
  includeDate: boolean;
  customTitle: string;
  fields: PDFField[];
}

interface PDFData {
  events?: EventDetails[];
  contacts?: Contact[];
  locations?: Location[];
  reservations?: Reservation[];
  contracts?: Contract[];
}

const convertToArabicNumerals = (num: number | string | undefined | null) => {
  if (num === undefined || num === null) return "";
  const numStr = Math.floor(Number(num)).toString();
  return numStr.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[parseInt(d)]);
};

const formatArabicCurrency = (amount: number | string | undefined | null, currency: string = "جنيه") => {
  if (amount === undefined || amount === null) return "";
  const arabicAmount = convertToArabicNumerals(amount);
  return `${arabicAmount} ${currency}`;
};

const formatArabicText = (text: string) => {
  if (!text) return '';
  return text.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\.\,\-\+\%\$\€\£\/\:]/g, '').trim();
};

const formatArabicDate = (dateStr: string) => {
  const date = new Date(dateStr);
  const day = convertToArabicNumerals(date.getDate().toString().padStart(2, '0'));
  const month = convertToArabicNumerals((date.getMonth() + 1).toString().padStart(2, '0'));
  const year = convertToArabicNumerals(date.getFullYear());
  return `${day}/${month}/${year}`;
};

// Helper function to detect if text contains Arabic characters
const containsArabic = (text: string): boolean => {
  if (!text) return false;
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
};

// Helper function to check if any data contains Arabic text
const dataContainsArabic = (data: PDFData): boolean => {
  // Check events
  if (data.events) {
    for (const event of data.events) {
      if (containsArabic(event.title) ||
          containsArabic(event.description || '') ||
          containsArabic(event.contact?.name || '') ||
          containsArabic(event.location?.name || '')) {
        return true;
      }
    }
  }

  // Check locations
  if (data.locations) {
    for (const location of data.locations) {
      if (containsArabic(location.name) || containsArabic(location.address || '')) {
        return true;
      }
    }
  }

  // Check reservations
  if (data.reservations) {
    for (const reservation of data.reservations) {
      if (containsArabic(reservation.title) || containsArabic(reservation.description || '')) {
        return true;
      }
    }
  }

  // Check contracts
  if (data.contracts) {
    for (const contract of data.contracts) {
      if (containsArabic(contract.title) || containsArabic(contract.description || '')) {
        return true;
      }
    }
  }

  return false;
};

const loadLogo = async (logoUrl: string) => {
  try {
    const response = await fetch(logoUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch logo: ${response.status}`);
    }
    
    const logoData = await response.arrayBuffer();
    const uint8Array = new Uint8Array(logoData);
    
    // Validate PNG header
    if (uint8Array.length < 8 || 
        uint8Array[0] !== 0x89 || uint8Array[1] !== 0x50 || 
        uint8Array[2] !== 0x4E || uint8Array[3] !== 0x47) {
      throw new Error("Invalid PNG file format");
    }
    
    let binary = "";
    for (let i = 0; i < uint8Array.length; i++) {
      binary += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binary);
  } catch (error) {
    console.error("Error loading logo:", error);
    return null;
  }
};

const formatLine = (label: string, value: string, language: string, pageWidth: number, doc: jsPDF, currentY: number) => {
  if (language === "ar") {
    const cleanLabel = formatArabicText(label);
    const cleanValue = formatArabicText(value);
    const formattedText = `${cleanValue} :${cleanLabel}`;
    doc.text(formattedText, pageWidth - 14, currentY, { align: "right" });
  } else {
    doc.text(`${label}: ${value}`, 14, currentY);
  }
};

export const generateCustomPDF = async (
  data: PDFData,
  options: PDFOptions,
  mode: 'events' | 'contacts' | 'locations' | 'reservations' | 'contracts'
) => {
  const { language, includeCompanyInfo, includeLogo, includeDate, customTitle, fields } = options;

  // AUTO-DETECT ARABIC: If data contains Arabic text, force Arabic formatting
  const hasArabicContent = dataContainsArabic(data);
  const effectiveLanguage = hasArabicContent ? 'ar' : language;

  try {
    // Use the EXACT original PDF generation from generatePDF.ts
    const doc = new jsPDF();
    const fontBase64 = await loadFont('/Amiri-Regular.ttf');
    doc.addFileToVFS('Amiri-Regular.ttf', fontBase64);
    doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
    doc.setFont('Amiri');

    const pageWidth = doc.internal.pageSize.getWidth();
    let currentY = 20;

    // Load and add company logo (EXACT original)
    if (includeLogo) {
      try {
        const logoBase64 = await loadLogo('/images/logo/saray-vera-logo.png');
        if (logoBase64) {
          const logoWidth = 40;
          const logoHeight = 20;
          const logoX = language === "ar" ? pageWidth - logoWidth - 14 : 14;
          doc.addImage(`data:image/png;base64,${logoBase64}`, 'PNG', logoX, currentY, logoWidth, logoHeight);
        }
      } catch (logoError) {
        console.warn("Could not load logo, continuing without it:", logoError);
      }
    }

    // Company name and document info (EXACT original)
    if (includeCompanyInfo) {
      doc.setFontSize(16);
      const companyName = "Saray Vera";
      const companyNameWidth = doc.getTextWidth(companyName);
      const companyNameX = effectiveLanguage === "ar" ? 14 : pageWidth - companyNameWidth - 14;
      doc.text(companyName, companyNameX, currentY + 8);
    }

    // Document creation date (EXACT original)
    if (includeDate) {
      doc.setFontSize(10);
      const currentDate = new Date();
      const formattedDate = effectiveLanguage === "ar"
        ? formatArabicDate(currentDate.toISOString())
        : currentDate.toLocaleDateString("en-US", {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
      const dateText = `Created: ${formattedDate}`;
      const dateWidth = doc.getTextWidth(dateText);
      const dateX = effectiveLanguage === "ar" ? 14 : pageWidth - dateWidth - 14;
      doc.text(dateText, dateX, currentY + 18);
    }

    // Add a separator line (EXACT original)
    currentY += 30;
    doc.setLineWidth(0.5);
    doc.line(14, currentY, pageWidth - 14, currentY);
    currentY += 10;

    // Report Title - Professional styling (EXACT original)
    doc.setFontSize(20);
    doc.setFont('Amiri', 'bold');
    const reportTitle = customTitle;
    const reportTitleWidth = doc.getTextWidth(reportTitle);
    const titleX = (pageWidth - reportTitleWidth) / 2; // Center the title
    doc.text(reportTitle, titleX, currentY);

    // Add underline to title (EXACT original)
    doc.setLineWidth(0.3);
    doc.line(titleX, currentY + 2, titleX + reportTitleWidth, currentY + 2);
    currentY += 15;

    // Reset font
    doc.setFont('Amiri', 'normal');

    // Use EXACT original table generation for events with effective language
    if (mode === 'events' && data.events) {
      await generateOriginalEventsTable(doc, data.events, effectiveLanguage, currentY, pageWidth, fields);
    } else if (mode === 'locations' && data.locations) {
      await generateOriginalLocationsTable(doc, data.locations, effectiveLanguage, currentY, pageWidth, fields, data);
    } else if (mode === 'reservations' && data.reservations) {
      await generateOriginalReservationsTable(doc, data.reservations, effectiveLanguage, currentY, pageWidth, fields, data);
    } else if (mode === 'contracts' && data.contracts) {
      await generateOriginalContractsTable(doc, data.contracts, effectiveLanguage, currentY, pageWidth, fields, data);
    }

    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `${customTitle.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_${timestamp}.pdf`;

    // Save the PDF
    doc.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

// Helper function to load font
const loadFont = async (fontUrl: string) => {
  try {
    const response = await fetch(fontUrl);
    const fontData = await response.arrayBuffer();
    const uint8Array = new Uint8Array(fontData);
    let binary = "";
    for (let i = 0; i < uint8Array.length; i++) {
      binary += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binary);
  } catch (error) {
    console.error("Error loading font:", error);
    return "";
  }
};

// Generate ORIGINAL Events Table with field editing support
const generateOriginalEventsTable = async (doc: any, events: EventDetails[], language: string, startY: number, pageWidth: number, fields: any[]) => {
  let currentY = startY;

  // Use the EXACT translation function from your original code
  const t = (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      en: {
        "title": "Title",
        "due date": "Due Date",
        "amount": "Amount",
        "status": "Status",
        "income events": "Income Events",
        "expense events": "Expense Events",
        "total income": "Total Income",
        "total expenses": "Total Expenses",
        "reservations": "Reservations",
        "contracts": "Contracts",
        "start date": "Start Date",
        "end date": "End Date"
      },
      ar: {
        "title": "العنوان",
        "due date": "تاريخ الاستحقاق",
        "amount": "المبلغ",
        "status": "الحالة",
        "income events": "أحداث الدخل",
        "expense events": "أحداث المصروفات",
        "total income": "إجمالي الدخل",
        "total expenses": "إجمالي المصروفات",
        "reservations": "الحجوزات",
        "contracts": "العقود",
        "start date": "تاريخ البداية",
        "end date": "تاريخ النهاية"
      }
    };
    return translations[language]?.[key] || key;
  };

  // Apply field edits if provided
  let processedEvents = [...events];
  if (fields && fields.length > 0) {
    processedEvents = events.map((event, index) => {
      const eventFields = fields.filter(f => f.key.startsWith(`event_${index}_`));
      let updatedEvent = { ...event };

      eventFields.forEach(field => {
        if (field.key.endsWith('_title')) updatedEvent.title = field.value;
        if (field.key.endsWith('_amount')) updatedEvent.amount = field.value;
        if (field.key.endsWith('_dueDate')) updatedEvent.dueDate = field.value;
        if (field.key.endsWith('_status')) updatedEvent.status = field.value;
        if (field.key.endsWith('_priority')) updatedEvent.priority = field.value;
        if (field.key.endsWith('_description')) updatedEvent.description = field.value;
      });

      return updatedEvent;
    });
  }

  // EXACT original table columns - REMOVED PRIORITY
  const tableColumn = language === "ar"
    ? [t("title"), t("due date"), t("amount"), t("status")]
    : ["Title", "Due Date", "Amount", "Status"];

  const incomeRows: any[] = [];
  const expenseRows: any[] = [];

  // Process events with EXACT original formatting
  processedEvents.forEach(event => {
    const formattedDate = language === "ar" ? formatArabicDate(event.dueDate) : new Date(event.dueDate).toLocaleDateString("en-US");
    const formattedAmount = language === "ar"
      ? formatArabicCurrency(Math.round(Number(event.amount) || 0))
      : Math.round(Number(event.amount) || 0).toString();

    const eventData = [
      event.title,
      formattedDate,
      formattedAmount,
      t(event.status)
    ];
    if (event.category === "income") incomeRows.push(eventData);
    else if (event.category === "expense") expenseRows.push(eventData);
  });

  // EXACT original totals calculation
  const totalIncome = Math.round(processedEvents
    .filter(e => e.category === "income")
    .reduce((sum, e) => sum + (Number(e.amount) || 0), 0));

  const totalExpenses = Math.round(processedEvents
    .filter(e => e.category === "expense")
    .reduce((sum, e) => sum + (Number(e.amount) || 0), 0));

  const totalIncomeText = language === "ar"
    ? `${formatArabicCurrency(totalIncome)} :${t("total income")}`
    : `${t("total income")}: ${totalIncome}`;
  const totalExpensesText = language === "ar"
    ? `${formatArabicCurrency(totalExpenses)} :${t("total expenses")}`
    : `${t("total expenses")}: ${totalExpenses}`;

  // EXACT original Income Table
  if (incomeRows.length > 0) {
    doc.setFontSize(14);
    const incomeTitle = t("income events");
    const titleWidth = doc.getTextWidth(incomeTitle);
    doc.text(incomeTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
    currentY += 5;

    autoTable(doc, {
      head: [tableColumn],
      body: incomeRows,
      startY: currentY + 5,
      styles: {
        halign: language === "ar" ? "right" : "left",
        font: 'Amiri',
        fontSize: 10,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
      },
      headStyles: {
        font: 'Amiri',
        fontStyle: 'bold',
        halign: language === "ar" ? "right" : "left",
        fillColor: [41, 128, 185], // EXACT original blue
        textColor: [255, 255, 255],
        fontSize: 11,
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { halign: language === "ar" ? "right" : "left" },
        1: { halign: language === "ar" ? "right" : "center" },
        2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
        3: { halign: language === "ar" ? "right" : "center" }
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 10;
    const incomeTextWidth = doc.getTextWidth(totalIncomeText);
    doc.setFontSize(12);
    doc.text(totalIncomeText, language === "ar" ? pageWidth - incomeTextWidth - 14 : 14, currentY);
  }

  // EXACT original Expense Table
  if (expenseRows.length > 0) {
    currentY += 15;
    doc.setFontSize(14);
    const expenseTitle = t("expense events");
    const titleWidth = doc.getTextWidth(expenseTitle);
    doc.text(expenseTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
    currentY += 5;

    autoTable(doc, {
      head: [tableColumn],
      body: expenseRows,
      startY: currentY + 5,
      styles: {
        halign: language === "ar" ? "right" : "left",
        font: 'Amiri',
        fontSize: 10,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
      },
      headStyles: {
        font: 'Amiri',
        fontStyle: 'bold',
        halign: language === "ar" ? "right" : "left",
        fillColor: [231, 76, 60], // EXACT original red
        textColor: [255, 255, 255],
        fontSize: 11,
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { halign: language === "ar" ? "right" : "left" },
        1: { halign: language === "ar" ? "right" : "center" },
        2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
        3: { halign: language === "ar" ? "right" : "center" }
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 10;
    const expenseTextWidth = doc.getTextWidth(totalExpensesText);
    doc.setFontSize(12);
    doc.text(totalExpensesText, language === "ar" ? pageWidth - expenseTextWidth - 14 : 14, currentY);
  }
};

// Generate Contacts Table
const generateContactsTable = async (doc: any, contacts: Contact[], language: string, startY: number, pageWidth: number) => {
  let currentY = startY;

  const t = (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      en: { "name": "Name", "email": "Email", "phone": "Phone", "company": "Company", "contacts": "Contacts" },
      ar: { "name": "الاسم", "email": "البريد الإلكتروني", "phone": "الهاتف", "company": "الشركة", "contacts": "جهات الاتصال" }
    };
    return translations[language]?.[key] || key;
  };

  const tableColumn = language === "ar"
    ? [t("name"), t("email"), t("phone"), t("company")]
    : ["Name", "Email", "Phone", "Company"];

  const contactRows = contacts.map(contact => [
    contact.name || "-",
    contact.email || "-",
    contact.phone || "-",
    contact.company || "-"
  ]);

  doc.setFontSize(14);
  const contactsTitle = t("contacts");
  const titleWidth = doc.getTextWidth(contactsTitle);
  doc.text(contactsTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
  currentY += 5;

  autoTable(doc, {
    head: [tableColumn],
    body: contactRows,
    startY: currentY + 5,
    styles: {
      halign: language === "ar" ? "right" : "left",
      font: 'Amiri',
      fontSize: 10,
      cellPadding: 4,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      font: 'Amiri',
      fontStyle: 'bold',
      halign: language === "ar" ? "right" : "left",
      fillColor: [52, 152, 219],
      textColor: [255, 255, 255],
      fontSize: 11,
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250],
    }
  });
};

// Generate ORIGINAL Locations Table with ALL ASSOCIATED DATA
const generateOriginalLocationsTable = async (doc: any, locations: Location[], language: string, startY: number, pageWidth: number, fields: any[], data?: any) => {
  let currentY = startY;

  // Use the EXACT translation function from your original code
  const t = (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      en: {
        "name": "Name",
        "address": "Address",
        "capacity": "Capacity",
        "status": "Status",
        "locations": "Locations",
        "active": "Active",
        "inactive": "Inactive",
        "title": "Title",
        "due date": "Due Date",
        "amount": "Amount",
        "income events": "Income Events",
        "expense events": "Expense Events",
        "total income": "Total Income",
        "total expenses": "Total Expenses",
        "reservations": "Reservations",
        "contracts": "Contracts",
        "start date": "Start Date",
        "end date": "End Date",
        "total reservations": "Total Reservations",
        "total contracts": "Total Contracts"
      },
      ar: {
        "name": "الاسم",
        "address": "العنوان",
        "capacity": "السعة",
        "status": "الحالة",
        "locations": "المواقع",
        "active": "نشط",
        "inactive": "غير نشط",
        "title": "العنوان",
        "due date": "تاريخ الاستحقاق",
        "amount": "المبلغ",
        "income events": "أحداث الدخل",
        "expense events": "أحداث المصروفات",
        "total income": "إجمالي الدخل",
        "total expenses": "إجمالي المصروفات",
        "reservations": "الحجوزات",
        "contracts": "العقود",
        "start date": "تاريخ البداية",
        "end date": "تاريخ النهاية",
        "total reservations": "إجمالي الحجوزات",
        "total contracts": "إجمالي العقود"
      }
    };
    return translations[language]?.[key] || key;
  };

  // Apply field edits if provided
  let processedLocations = [...locations];
  if (fields && fields.length > 0) {
    processedLocations = locations.map((location, index) => {
      const locationFields = fields.filter(f => f.key.startsWith(`location_${index}_`));
      let updatedLocation = { ...location };

      locationFields.forEach(field => {
        if (field.key.endsWith('_name')) updatedLocation.name = field.value;
        if (field.key.endsWith('_address')) updatedLocation.address = field.value;
        if (field.key.endsWith('_capacity')) updatedLocation.capacity = parseInt(field.value) || 0;
      });

      return updatedLocation;
    });
  }

  // EXACT original table columns
  const tableColumn = language === "ar"
    ? [t("name"), t("address"), t("capacity"), t("status")]
    : ["Name", "Address", "Capacity", "Status"];

  // Process locations with EXACT original formatting
  const locationRows = processedLocations.map(location => [
    location.name || "-",
    location.address || "-",
    location.capacity?.toString() || "-",
    (location as any).is_active ? t("active") : t("inactive")
  ]);

  // EXACT original table generation
  doc.setFontSize(14);
  const locationsTitle = t("locations");
  const titleWidth = doc.getTextWidth(locationsTitle);
  doc.text(locationsTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
  currentY += 5;

  autoTable(doc, {
    head: [tableColumn],
    body: locationRows,
    startY: currentY + 5,
    styles: {
      halign: language === "ar" ? "right" : "left",
      font: 'Amiri',
      fontSize: 10,
      cellPadding: 4,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      font: 'Amiri',
      fontStyle: 'bold',
      halign: language === "ar" ? "right" : "left",
      fillColor: [46, 204, 113], // EXACT original green
      textColor: [255, 255, 255],
      fontSize: 11,
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250],
    },
    columnStyles: {
      0: { halign: language === "ar" ? "right" : "left" },
      1: { halign: language === "ar" ? "right" : "left" },
      2: { halign: language === "ar" ? "right" : "center" },
      3: { halign: language === "ar" ? "right" : "center" }
    }
  });

  currentY = (doc as any).lastAutoTable.finalY + 20;

  // ADD ASSOCIATED INCOMES, EXPENSES, RESERVATIONS, CONTRACTS
  if (data && data.events) {
    const incomeEvents = data.events.filter((e: any) => e.category === "income");
    const expenseEvents = data.events.filter((e: any) => e.category === "expense");

    // Income Events Table
    if (incomeEvents.length > 0) {
      const incomeTableColumn = language === "ar"
        ? [t("title"), t("due date"), t("amount"), t("status")]
        : ["Title", "Due Date", "Amount", "Status"];

      const incomeRows = incomeEvents.map((event: any) => {
        const formattedDate = language === "ar" ? formatArabicDate(event.dueDate) : new Date(event.dueDate).toLocaleDateString("en-US");
        const formattedAmount = language === "ar"
          ? formatArabicCurrency(Math.round(Number(event.amount) || 0))
          : Math.round(Number(event.amount) || 0).toString();

        return [
          event.title,
          formattedDate,
          formattedAmount,
          t(event.status)
        ];
      });

      doc.setFontSize(14);
      const incomeTitle = t("income events");
      const incomeTitleWidth = doc.getTextWidth(incomeTitle);
      doc.text(incomeTitle, language === "ar" ? pageWidth - incomeTitleWidth - 14 : 14, currentY);
      currentY += 5;

      autoTable(doc, {
        head: [incomeTableColumn],
        body: incomeRows,
        startY: currentY + 5,
        styles: {
          halign: language === "ar" ? "right" : "left",
          font: 'Amiri',
          fontSize: 10,
          cellPadding: 4,
          lineColor: [200, 200, 200],
          lineWidth: 0.1,
        },
        headStyles: {
          font: 'Amiri',
          fontStyle: 'bold',
          halign: language === "ar" ? "right" : "left",
          fillColor: [41, 128, 185], // EXACT original blue
          textColor: [255, 255, 255],
          fontSize: 11,
        },
        alternateRowStyles: {
          fillColor: [248, 249, 250],
        },
        columnStyles: {
          0: { halign: language === "ar" ? "right" : "left" },
          1: { halign: language === "ar" ? "right" : "center" },
          2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
          3: { halign: language === "ar" ? "right" : "center" }
        }
      });

      currentY = (doc as any).lastAutoTable.finalY + 10;
      const totalIncome = Math.round(incomeEvents.reduce((sum: number, e: any) => sum + (Number(e.amount) || 0), 0));
      const totalIncomeText = language === "ar"
        ? `${formatArabicCurrency(totalIncome)} :${t("total income")}`
        : `${t("total income")}: ${totalIncome}`;
      const incomeTextWidth = doc.getTextWidth(totalIncomeText);
      doc.setFontSize(12);
      doc.text(totalIncomeText, language === "ar" ? pageWidth - incomeTextWidth - 14 : 14, currentY);
      currentY += 20;
    }

    // Expense Events Table
    if (expenseEvents.length > 0) {
      const expenseTableColumn = language === "ar"
        ? [t("title"), t("due date"), t("amount"), t("status")]
        : ["Title", "Due Date", "Amount", "Status"];

      const expenseRows = expenseEvents.map((event: any) => {
        const formattedDate = language === "ar" ? formatArabicDate(event.dueDate) : new Date(event.dueDate).toLocaleDateString("en-US");
        const formattedAmount = language === "ar"
          ? formatArabicCurrency(Math.round(Number(event.amount) || 0))
          : Math.round(Number(event.amount) || 0).toString();

        return [
          event.title,
          formattedDate,
          formattedAmount,
          t(event.status)
        ];
      });

      doc.setFontSize(14);
      const expenseTitle = t("expense events");
      const expenseTitleWidth = doc.getTextWidth(expenseTitle);
      doc.text(expenseTitle, language === "ar" ? pageWidth - expenseTitleWidth - 14 : 14, currentY);
      currentY += 5;

      autoTable(doc, {
        head: [expenseTableColumn],
        body: expenseRows,
        startY: currentY + 5,
        styles: {
          halign: language === "ar" ? "right" : "left",
          font: 'Amiri',
          fontSize: 10,
          cellPadding: 4,
          lineColor: [200, 200, 200],
          lineWidth: 0.1,
        },
        headStyles: {
          font: 'Amiri',
          fontStyle: 'bold',
          halign: language === "ar" ? "right" : "left",
          fillColor: [231, 76, 60], // EXACT original red
          textColor: [255, 255, 255],
          fontSize: 11,
        },
        alternateRowStyles: {
          fillColor: [248, 249, 250],
        },
        columnStyles: {
          0: { halign: language === "ar" ? "right" : "left" },
          1: { halign: language === "ar" ? "right" : "center" },
          2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
          3: { halign: language === "ar" ? "right" : "center" }
        }
      });

      currentY = (doc as any).lastAutoTable.finalY + 10;
      const totalExpenses = Math.round(expenseEvents.reduce((sum: number, e: any) => sum + (Number(e.amount) || 0), 0));
      const totalExpensesText = language === "ar"
        ? `${formatArabicCurrency(totalExpenses)} :${t("total expenses")}`
        : `${t("total expenses")}: ${totalExpenses}`;
      const expenseTextWidth = doc.getTextWidth(totalExpensesText);
      doc.setFontSize(12);
      doc.text(totalExpensesText, language === "ar" ? pageWidth - expenseTextWidth - 14 : 14, currentY);
      currentY += 20;
    }
  }

  // ADD RESERVATIONS TABLE
  if (data && data.reservations && data.reservations.length > 0) {
    const reservationTableColumn = language === "ar"
      ? [t("title"), t("start date"), t("end date"), t("status"), t("amount")]
      : ["Title", "Start Date", "End Date", "Status", "Amount"];

    const reservationRows = data.reservations.map((reservation: any) => {
      const formatDate = (date: string) => {
        return language === "ar" ? formatArabicDate(date) : new Date(date).toLocaleDateString("en-US");
      };
      return [
        reservation.title || "-",
        reservation.start_date ? formatDate(reservation.start_date) : "-",
        reservation.end_date ? formatDate(reservation.end_date) : "-",
        reservation.status ? t(reservation.status) : "-",
        reservation.total_amount !== undefined
          ? (language === "ar" ? formatArabicCurrency(reservation.total_amount) : reservation.total_amount.toString())
          : "-"
      ];
    });

    doc.setFontSize(14);
    const reservationsTitle = t("reservations");
    const reservationsTitleWidth = doc.getTextWidth(reservationsTitle);
    doc.text(reservationsTitle, language === "ar" ? pageWidth - reservationsTitleWidth - 14 : 14, currentY);
    currentY += 5;

    autoTable(doc, {
      head: [reservationTableColumn],
      body: reservationRows,
      startY: currentY + 5,
      styles: {
        halign: language === "ar" ? "right" : "left",
        font: 'Amiri',
        fontSize: 10,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
      },
      headStyles: {
        font: 'Amiri',
        fontStyle: 'bold',
        halign: language === "ar" ? "right" : "left",
        fillColor: [155, 89, 182], // Purple for reservations
        textColor: [255, 255, 255],
        fontSize: 11,
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { halign: language === "ar" ? "right" : "left" },
        1: { halign: language === "ar" ? "right" : "center" },
        2: { halign: language === "ar" ? "right" : "center" },
        3: { halign: language === "ar" ? "right" : "center" },
        4: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' }
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 10;
    const totalReservations = Math.round(data.reservations.reduce((sum: number, r: any) => sum + (Number(r.total_amount) || 0), 0));
    const totalReservationsText = language === "ar"
      ? `${formatArabicCurrency(totalReservations)} :${t("total reservations")}`
      : `${t("total reservations")}: ${totalReservations}`;
    const reservationsTextWidth = doc.getTextWidth(totalReservationsText);
    doc.setFontSize(12);
    doc.text(totalReservationsText, language === "ar" ? pageWidth - reservationsTextWidth - 14 : 14, currentY);
    currentY += 20;
  }

  // ADD CONTRACTS TABLE
  if (data && data.contracts && data.contracts.length > 0) {
    const contractTableColumn = language === "ar"
      ? [t("title"), t("start date"), t("end date"), t("status"), t("amount")]
      : ["Title", "Start Date", "End Date", "Status", "Amount"];

    const contractRows = data.contracts.map((contract: any) => {
      const formatDate = (date: string) => {
        return language === "ar" ? formatArabicDate(date) : new Date(date).toLocaleDateString("en-US");
      };
      return [
        contract.title || "-",
        contract.start_date ? formatDate(contract.start_date) : "-",
        contract.end_date ? formatDate(contract.end_date) : "-",
        contract.status ? t(contract.status) : "-",
        contract.total_amount !== undefined
          ? (language === "ar" ? formatArabicCurrency(contract.total_amount) : contract.total_amount.toString())
          : "-"
      ];
    });

    doc.setFontSize(14);
    const contractsTitle = t("contracts");
    const contractsTitleWidth = doc.getTextWidth(contractsTitle);
    doc.text(contractsTitle, language === "ar" ? pageWidth - contractsTitleWidth - 14 : 14, currentY);
    currentY += 5;

    autoTable(doc, {
      head: [contractTableColumn],
      body: contractRows,
      startY: currentY + 5,
      styles: {
        halign: language === "ar" ? "right" : "left",
        font: 'Amiri',
        fontSize: 10,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
      },
      headStyles: {
        font: 'Amiri',
        fontStyle: 'bold',
        halign: language === "ar" ? "right" : "left",
        fillColor: [52, 152, 219], // Blue for contracts
        textColor: [255, 255, 255],
        fontSize: 11,
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { halign: language === "ar" ? "right" : "left" },
        1: { halign: language === "ar" ? "right" : "center" },
        2: { halign: language === "ar" ? "right" : "center" },
        3: { halign: language === "ar" ? "right" : "center" },
        4: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' }
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 10;
    const totalContracts = Math.round(data.contracts.reduce((sum: number, c: any) => sum + (Number(c.total_amount) || 0), 0));
    const totalContractsText = language === "ar"
      ? `${formatArabicCurrency(totalContracts)} :${t("total contracts")}`
      : `${t("total contracts")}: ${totalContracts}`;
    const contractsTextWidth = doc.getTextWidth(totalContractsText);
    doc.setFontSize(12);
    doc.text(totalContractsText, language === "ar" ? pageWidth - contractsTextWidth - 14 : 14, currentY);
  }
};

// Generate ORIGINAL Reservations Table with associated incomes/expenses
const generateOriginalReservationsTable = async (doc: any, reservations: Reservation[], language: string, startY: number, pageWidth: number, fields: any[], data?: any) => {
  let currentY = startY;

  const t = (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      en: {
        "title": "Title",
        "start date": "Start Date",
        "end date": "End Date",
        "status": "Status",
        "amount": "Amount",
        "reservations": "Reservations",
        "due date": "Due Date",
        "income events": "Income Events",
        "expense events": "Expense Events",
        "total income": "Total Income",
        "total expenses": "Total Expenses",
        "total reservations": "Total Reservations"
      },
      ar: {
        "title": "العنوان",
        "start date": "تاريخ البداية",
        "end date": "تاريخ النهاية",
        "status": "الحالة",
        "amount": "المبلغ",
        "reservations": "الحجوزات",
        "due date": "تاريخ الاستحقاق",
        "income events": "أحداث الدخل",
        "expense events": "أحداث المصروفات",
        "total income": "إجمالي الدخل",
        "total expenses": "إجمالي المصروفات",
        "total reservations": "إجمالي الحجوزات"
      }
    };
    return translations[language]?.[key] || key;
  };

  // Apply field edits if provided
  let processedReservations = [...reservations];
  if (fields && fields.length > 0) {
    processedReservations = reservations.map((reservation, index) => {
      const reservationFields = fields.filter(f => f.key.startsWith(`reservation_${index}_`));
      let updatedReservation = { ...reservation };

      reservationFields.forEach(field => {
        if (field.key.endsWith('_title')) updatedReservation.title = field.value;
        if (field.key.endsWith('_start_date')) updatedReservation.start_date = field.value;
        if (field.key.endsWith('_end_date')) updatedReservation.end_date = field.value;
        if (field.key.endsWith('_status')) updatedReservation.status = field.value;
        if (field.key.endsWith('_total_amount')) updatedReservation.total_amount = parseFloat(field.value) || 0;
      });

      return updatedReservation;
    });
  }

  const tableColumn = language === "ar"
    ? [t("title"), t("start date"), t("end date"), t("status"), t("amount")]
    : ["Title", "Start Date", "End Date", "Status", "Amount"];

  const reservationRows = processedReservations.map(reservation => {
    const formatDate = (date: string) => {
      return language === "ar" ? formatArabicDate(date) : new Date(date).toLocaleDateString("en-US");
    };
    return [
      reservation.title || "-",
      reservation.start_date ? formatDate(reservation.start_date) : "-",
      reservation.end_date ? formatDate(reservation.end_date) : "-",
      reservation.status ? t(reservation.status) : "-",
      reservation.total_amount !== undefined
        ? (language === "ar" ? formatArabicCurrency(reservation.total_amount) : reservation.total_amount.toString())
        : "-"
    ];
  });

  doc.setFontSize(14);
  const reservationsTitle = t("reservations");
  const titleWidth = doc.getTextWidth(reservationsTitle);
  doc.text(reservationsTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
  currentY += 5;

  autoTable(doc, {
    head: [tableColumn],
    body: reservationRows,
    startY: currentY + 5,
    styles: {
      halign: language === "ar" ? "right" : "left",
      font: 'Amiri',
      fontSize: 10,
      cellPadding: 4,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      font: 'Amiri',
      fontStyle: 'bold',
      halign: language === "ar" ? "right" : "left",
      fillColor: [155, 89, 182], // Purple for reservations
      textColor: [255, 255, 255],
      fontSize: 11,
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250],
    },
    columnStyles: {
      0: { halign: language === "ar" ? "right" : "left" },
      1: { halign: language === "ar" ? "right" : "center" },
      2: { halign: language === "ar" ? "right" : "center" },
      3: { halign: language === "ar" ? "right" : "center" },
      4: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' }
    }
  });

  currentY = (doc as any).lastAutoTable.finalY + 10;
  const totalReservations = Math.round(processedReservations.reduce((sum, r) => sum + (Number(r.total_amount) || 0), 0));
  const totalReservationsText = language === "ar"
    ? `${formatArabicCurrency(totalReservations)} :${t("total reservations")}`
    : `${t("total reservations")}: ${totalReservations}`;
  const reservationsTextWidth = doc.getTextWidth(totalReservationsText);
  doc.setFontSize(12);
  doc.text(totalReservationsText, language === "ar" ? pageWidth - reservationsTextWidth - 14 : 14, currentY);
  currentY += 20;

  // ADD ASSOCIATED INCOMES AND EXPENSES
  if (data && data.events) {
    const incomeEvents = data.events.filter((e: any) => e.category === "income");
    const expenseEvents = data.events.filter((e: any) => e.category === "expense");

    // Income Events Table
    if (incomeEvents.length > 0) {
      const incomeTableColumn = language === "ar"
        ? [t("title"), t("due date"), t("amount"), t("status")]
        : ["Title", "Due Date", "Amount", "Status"];

      const incomeRows = incomeEvents.map((event: any) => {
        const formattedDate = language === "ar" ? formatArabicDate(event.dueDate) : new Date(event.dueDate).toLocaleDateString("en-US");
        const formattedAmount = language === "ar"
          ? formatArabicCurrency(Math.round(Number(event.amount) || 0))
          : Math.round(Number(event.amount) || 0).toString();

        return [
          event.title,
          formattedDate,
          formattedAmount,
          t(event.status)
        ];
      });

      doc.setFontSize(14);
      const incomeTitle = t("income events");
      const incomeTitleWidth = doc.getTextWidth(incomeTitle);
      doc.text(incomeTitle, language === "ar" ? pageWidth - incomeTitleWidth - 14 : 14, currentY);
      currentY += 5;

      autoTable(doc, {
        head: [incomeTableColumn],
        body: incomeRows,
        startY: currentY + 5,
        styles: {
          halign: language === "ar" ? "right" : "left",
          font: 'Amiri',
          fontSize: 10,
          cellPadding: 4,
          lineColor: [200, 200, 200],
          lineWidth: 0.1,
        },
        headStyles: {
          font: 'Amiri',
          fontStyle: 'bold',
          halign: language === "ar" ? "right" : "left",
          fillColor: [41, 128, 185], // EXACT original blue
          textColor: [255, 255, 255],
          fontSize: 11,
        },
        alternateRowStyles: {
          fillColor: [248, 249, 250],
        },
        columnStyles: {
          0: { halign: language === "ar" ? "right" : "left" },
          1: { halign: language === "ar" ? "right" : "center" },
          2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
          3: { halign: language === "ar" ? "right" : "center" }
        }
      });

      currentY = (doc as any).lastAutoTable.finalY + 10;
      const totalIncome = Math.round(incomeEvents.reduce((sum: number, e: any) => sum + (Number(e.amount) || 0), 0));
      const totalIncomeText = language === "ar"
        ? `${formatArabicCurrency(totalIncome)} :${t("total income")}`
        : `${t("total income")}: ${totalIncome}`;
      const incomeTextWidth = doc.getTextWidth(totalIncomeText);
      doc.setFontSize(12);
      doc.text(totalIncomeText, language === "ar" ? pageWidth - incomeTextWidth - 14 : 14, currentY);
      currentY += 20;
    }

    // Expense Events Table
    if (expenseEvents.length > 0) {
      const expenseTableColumn = language === "ar"
        ? [t("title"), t("due date"), t("amount"), t("status")]
        : ["Title", "Due Date", "Amount", "Status"];

      const expenseRows = expenseEvents.map((event: any) => {
        const formattedDate = language === "ar" ? formatArabicDate(event.dueDate) : new Date(event.dueDate).toLocaleDateString("en-US");
        const formattedAmount = language === "ar"
          ? formatArabicCurrency(Math.round(Number(event.amount) || 0))
          : Math.round(Number(event.amount) || 0).toString();

        return [
          event.title,
          formattedDate,
          formattedAmount,
          t(event.status)
        ];
      });

      doc.setFontSize(14);
      const expenseTitle = t("expense events");
      const expenseTitleWidth = doc.getTextWidth(expenseTitle);
      doc.text(expenseTitle, language === "ar" ? pageWidth - expenseTitleWidth - 14 : 14, currentY);
      currentY += 5;

      autoTable(doc, {
        head: [expenseTableColumn],
        body: expenseRows,
        startY: currentY + 5,
        styles: {
          halign: language === "ar" ? "right" : "left",
          font: 'Amiri',
          fontSize: 10,
          cellPadding: 4,
          lineColor: [200, 200, 200],
          lineWidth: 0.1,
        },
        headStyles: {
          font: 'Amiri',
          fontStyle: 'bold',
          halign: language === "ar" ? "right" : "left",
          fillColor: [231, 76, 60], // EXACT original red
          textColor: [255, 255, 255],
          fontSize: 11,
        },
        alternateRowStyles: {
          fillColor: [248, 249, 250],
        },
        columnStyles: {
          0: { halign: language === "ar" ? "right" : "left" },
          1: { halign: language === "ar" ? "right" : "center" },
          2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
          3: { halign: language === "ar" ? "right" : "center" }
        }
      });

      currentY = (doc as any).lastAutoTable.finalY + 10;
      const totalExpenses = Math.round(expenseEvents.reduce((sum: number, e: any) => sum + (Number(e.amount) || 0), 0));
      const totalExpensesText = language === "ar"
        ? `${formatArabicCurrency(totalExpenses)} :${t("total expenses")}`
        : `${t("total expenses")}: ${totalExpenses}`;
      const expenseTextWidth = doc.getTextWidth(totalExpensesText);
      doc.setFontSize(12);
      doc.text(totalExpensesText, language === "ar" ? pageWidth - expenseTextWidth - 14 : 14, currentY);
    }
  }
};

// Generate ORIGINAL Contracts Table with associated incomes/expenses
const generateOriginalContractsTable = async (doc: any, contracts: Contract[], language: string, startY: number, pageWidth: number, fields: any[], data?: any) => {
  let currentY = startY;

  const t = (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      en: {
        "title": "Title",
        "start date": "Start Date",
        "end date": "End Date",
        "status": "Status",
        "amount": "Amount",
        "contracts": "Contracts",
        "due date": "Due Date",
        "income events": "Income Events",
        "expense events": "Expense Events",
        "total income": "Total Income",
        "total expenses": "Total Expenses",
        "total contracts": "Total Contracts"
      },
      ar: {
        "title": "العنوان",
        "start date": "تاريخ البداية",
        "end date": "تاريخ النهاية",
        "status": "الحالة",
        "amount": "المبلغ",
        "contracts": "العقود",
        "due date": "تاريخ الاستحقاق",
        "income events": "أحداث الدخل",
        "expense events": "أحداث المصروفات",
        "total income": "إجمالي الدخل",
        "total expenses": "إجمالي المصروفات",
        "total contracts": "إجمالي العقود"
      }
    };
    return translations[language]?.[key] || key;
  };

  // Apply field edits if provided
  let processedContracts = [...contracts];
  if (fields && fields.length > 0) {
    processedContracts = contracts.map((contract, index) => {
      const contractFields = fields.filter(f => f.key.startsWith(`contract_${index}_`));
      let updatedContract = { ...contract };

      contractFields.forEach(field => {
        if (field.key.endsWith('_title')) updatedContract.title = field.value;
        if (field.key.endsWith('_start_date')) updatedContract.start_date = field.value;
        if (field.key.endsWith('_end_date')) updatedContract.end_date = field.value;
        if (field.key.endsWith('_status')) updatedContract.status = field.value;
        if (field.key.endsWith('_total_amount')) updatedContract.total_amount = parseFloat(field.value) || 0;
      });

      return updatedContract;
    });
  }

  const tableColumn = language === "ar"
    ? [t("title"), t("start date"), t("end date"), t("status"), t("amount")]
    : ["Title", "Start Date", "End Date", "Status", "Amount"];

  const contractRows = processedContracts.map(contract => {
    const formatDate = (date: string) => {
      return language === "ar" ? formatArabicDate(date) : new Date(date).toLocaleDateString("en-US");
    };
    return [
      contract.title || "-",
      contract.start_date ? formatDate(contract.start_date) : "-",
      contract.end_date ? formatDate(contract.end_date) : "-",
      contract.status ? t(contract.status) : "-",
      contract.total_amount !== undefined
        ? (language === "ar" ? formatArabicCurrency(contract.total_amount) : contract.total_amount.toString())
        : "-"
    ];
  });

  doc.setFontSize(14);
  const contractsTitle = t("contracts");
  const titleWidth = doc.getTextWidth(contractsTitle);
  doc.text(contractsTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
  currentY += 5;

  autoTable(doc, {
    head: [tableColumn],
    body: contractRows,
    startY: currentY + 5,
    styles: {
      halign: language === "ar" ? "right" : "left",
      font: 'Amiri',
      fontSize: 10,
      cellPadding: 4,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      font: 'Amiri',
      fontStyle: 'bold',
      halign: language === "ar" ? "right" : "left",
      fillColor: [52, 152, 219], // Blue for contracts
      textColor: [255, 255, 255],
      fontSize: 11,
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250],
    },
    columnStyles: {
      0: { halign: language === "ar" ? "right" : "left" },
      1: { halign: language === "ar" ? "right" : "center" },
      2: { halign: language === "ar" ? "right" : "center" },
      3: { halign: language === "ar" ? "right" : "center" },
      4: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' }
    }
  });

  currentY = (doc as any).lastAutoTable.finalY + 10;
  const totalContracts = Math.round(processedContracts.reduce((sum, c) => sum + (Number(c.total_amount) || 0), 0));
  const totalContractsText = language === "ar"
    ? `${formatArabicCurrency(totalContracts)} :${t("total contracts")}`
    : `${t("total contracts")}: ${totalContracts}`;
  const contractsTextWidth = doc.getTextWidth(totalContractsText);
  doc.setFontSize(12);
  doc.text(totalContractsText, language === "ar" ? pageWidth - contractsTextWidth - 14 : 14, currentY);
  currentY += 20;

  // ADD ASSOCIATED INCOMES AND EXPENSES (same as reservations)
  if (data && data.events) {
    const incomeEvents = data.events.filter((e: any) => e.category === "income");
    const expenseEvents = data.events.filter((e: any) => e.category === "expense");

    // Income Events Table (same implementation as reservations)
    if (incomeEvents.length > 0) {
      const incomeTableColumn = language === "ar"
        ? [t("title"), t("due date"), t("amount"), t("status")]
        : ["Title", "Due Date", "Amount", "Status"];

      const incomeRows = incomeEvents.map((event: any) => {
        const formattedDate = language === "ar" ? formatArabicDate(event.dueDate) : new Date(event.dueDate).toLocaleDateString("en-US");
        const formattedAmount = language === "ar"
          ? formatArabicCurrency(Math.round(Number(event.amount) || 0))
          : Math.round(Number(event.amount) || 0).toString();

        return [
          event.title,
          formattedDate,
          formattedAmount,
          t(event.status)
        ];
      });

      doc.setFontSize(14);
      const incomeTitle = t("income events");
      const incomeTitleWidth = doc.getTextWidth(incomeTitle);
      doc.text(incomeTitle, language === "ar" ? pageWidth - incomeTitleWidth - 14 : 14, currentY);
      currentY += 5;

      autoTable(doc, {
        head: [incomeTableColumn],
        body: incomeRows,
        startY: currentY + 5,
        styles: {
          halign: language === "ar" ? "right" : "left",
          font: 'Amiri',
          fontSize: 10,
          cellPadding: 4,
          lineColor: [200, 200, 200],
          lineWidth: 0.1,
        },
        headStyles: {
          font: 'Amiri',
          fontStyle: 'bold',
          halign: language === "ar" ? "right" : "left",
          fillColor: [41, 128, 185], // EXACT original blue
          textColor: [255, 255, 255],
          fontSize: 11,
        },
        alternateRowStyles: {
          fillColor: [248, 249, 250],
        },
        columnStyles: {
          0: { halign: language === "ar" ? "right" : "left" },
          1: { halign: language === "ar" ? "right" : "center" },
          2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
          3: { halign: language === "ar" ? "right" : "center" }
        }
      });

      currentY = (doc as any).lastAutoTable.finalY + 10;
      const totalIncome = Math.round(incomeEvents.reduce((sum: number, e: any) => sum + (Number(e.amount) || 0), 0));
      const totalIncomeText = language === "ar"
        ? `${formatArabicCurrency(totalIncome)} :${t("total income")}`
        : `${t("total income")}: ${totalIncome}`;
      const incomeTextWidth = doc.getTextWidth(totalIncomeText);
      doc.setFontSize(12);
      doc.text(totalIncomeText, language === "ar" ? pageWidth - incomeTextWidth - 14 : 14, currentY);
      currentY += 20;
    }

    // Expense Events Table (same implementation as reservations)
    if (expenseEvents.length > 0) {
      const expenseTableColumn = language === "ar"
        ? [t("title"), t("due date"), t("amount"), t("status")]
        : ["Title", "Due Date", "Amount", "Status"];

      const expenseRows = expenseEvents.map((event: any) => {
        const formattedDate = language === "ar" ? formatArabicDate(event.dueDate) : new Date(event.dueDate).toLocaleDateString("en-US");
        const formattedAmount = language === "ar"
          ? formatArabicCurrency(Math.round(Number(event.amount) || 0))
          : Math.round(Number(event.amount) || 0).toString();

        return [
          event.title,
          formattedDate,
          formattedAmount,
          t(event.status)
        ];
      });

      doc.setFontSize(14);
      const expenseTitle = t("expense events");
      const expenseTitleWidth = doc.getTextWidth(expenseTitle);
      doc.text(expenseTitle, language === "ar" ? pageWidth - expenseTitleWidth - 14 : 14, currentY);
      currentY += 5;

      autoTable(doc, {
        head: [expenseTableColumn],
        body: expenseRows,
        startY: currentY + 5,
        styles: {
          halign: language === "ar" ? "right" : "left",
          font: 'Amiri',
          fontSize: 10,
          cellPadding: 4,
          lineColor: [200, 200, 200],
          lineWidth: 0.1,
        },
        headStyles: {
          font: 'Amiri',
          fontStyle: 'bold',
          halign: language === "ar" ? "right" : "left",
          fillColor: [231, 76, 60], // EXACT original red
          textColor: [255, 255, 255],
          fontSize: 11,
        },
        alternateRowStyles: {
          fillColor: [248, 249, 250],
        },
        columnStyles: {
          0: { halign: language === "ar" ? "right" : "left" },
          1: { halign: language === "ar" ? "right" : "center" },
          2: { halign: language === "ar" ? "right" : "right", fontStyle: 'bold' },
          3: { halign: language === "ar" ? "right" : "center" }
        }
      });

      currentY = (doc as any).lastAutoTable.finalY + 10;
      const totalExpenses = Math.round(expenseEvents.reduce((sum: number, e: any) => sum + (Number(e.amount) || 0), 0));
      const totalExpensesText = language === "ar"
        ? `${formatArabicCurrency(totalExpenses)} :${t("total expenses")}`
        : `${t("total expenses")}: ${totalExpenses}`;
      const expenseTextWidth = doc.getTextWidth(totalExpensesText);
      doc.setFontSize(12);
      doc.text(totalExpensesText, language === "ar" ? pageWidth - expenseTextWidth - 14 : 14, currentY);
    }
  }
};

export default generateCustomPDF;

// Contract related interfaces

export interface Client {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
}

export interface Location {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
}

export interface RenewalTerms {
  auto_renew: boolean;
  increase_percentage: number;
  notice_period_days: number;
}

export interface DocumentUpload {
  id: string;
  name: string;
  url: string;
  uploadDate: string;
  type: string;
  fileType: 'pdf' | 'image' | 'document' | string;
}

export interface Contract {
  id: string;
  contractNumber: string;
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  status: 'active' | 'pending' | 'expired' | 'terminated' | 'draft';
  monthlyAmount: number;
  total_amount: number;
  paymentDay: number;
  paymentStatus: 'up-to-date' | 'overdue' | 'partially-paid';
  client: Client;
  location: Location;
  renewal_terms: RenewalTerms;
  document_uploaded: DocumentUpload[];
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  actual_amount: number;
}

export interface Incomes {
  id: string;
  amount: number;
  due_date: string;
  notification_view_date: string;
  status?: string;
  priority?: string;
  received_date?: string;
}

export interface ContractFormData {
  priority: string;
  contact_id: string;
  location_id: string;
  type_id: string;
  contract: {
    id: string;
    title: string;
    description: string;
    start_date: string;
    end_date: string;
    total_amount: number;
    status?: string;
    notes?: string;
  };
  installments: {
    id?: string;
    amount: number;
    due_date: string;
    notification_view_date: string;
    status?: string;
    priority?: string;
    paid_date?: string;
    income?: Incomes[];
    expense_id?: string;
    title?: string;
    description?: string;
    contact_id?: string;
    location_id?: string;
    type_id?: string;
    is_deleted?: boolean;
    type?: 'income' | 'expense';
  }[];
  documentUpload?: DocumentUpload[];
  installmentCount: number;
  renewal_terms?: {
    auto_renew: boolean;
    increase_percentage: number;
    notice_period_days: number;
  };
}

"use client";
import React, { useState, useEffect } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { Reservation } from "@/lib/interfaces/reservation";
import { EventDetails } from "@/lib/interfaces/finaces";
import ReservationList from "./ReservationList";
import ReservationForm from "./ReservationForm";
import ReservationDetails from "./ReservationDetails";
import { FaPlus } from "react-icons/fa";
import { usePermissions } from "@/hooks/usePermissions";
import { LoadingComp } from "../common/Loading";
import { Card, CardContent } from "../cards/card";
import { Calendar } from "lucide-react";

const ReservationsOverview: React.FC = () => {
  const { t } = useLanguage();
  // Initialize with empty array instead of mockReservations
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [incomeEvents, setIncomeEvents] = useState<EventDetails[]>([]);
  const [selectedReservation, setSelectedReservation] = useState<Reservation | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isViewingDetails, setIsViewingDetails] = useState(false);
  const { hasPermission, permissionsLoaded } = usePermissions();

  // Handle form submission for new reservation
  const handleCreateReservation = (newReservation: Reservation, installmentEvents: EventDetails[]) => {
    setReservations([...reservations, newReservation]);
    setIncomeEvents([...incomeEvents, ...installmentEvents]);
    setIsCreating(false);
  };

  // Handle clicking on a reservation to view its details - Updated to accept string IDs to match Reservation interface
  const handleReservationClick = (id: number, reservation?: Reservation) => {
    console.log("Received reservation click for ID:", id);
    const idString = id.toString();
    
    if (reservation) {
      // If the reservation object was passed directly, use it
      console.log("Using directly passed reservation object");
      setSelectedReservation(reservation);
      setIsViewingDetails(true);
    } else {
      // Fallback to finding by ID if object wasn't passed
      console.log("Falling back to find reservation by ID");
      const foundReservation = reservations.find((r) => r.id === idString);
      if (foundReservation) {
        setSelectedReservation(foundReservation);
        setIsViewingDetails(true);
      } else {
        console.error("Reservation not found for ID:", idString);
      }
    }
    
    // Scroll to the top of the page to show the details
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Close details view
  const handleCloseDetails = () => {
    setIsViewingDetails(false);
    setSelectedReservation(null);
  };

  // Delete a reservation - Changed to accept number IDs to match child component
  const handleDeleteReservation = (id: number) => {
    const idString = id.toString();
    setReservations(reservations.filter((reservation) => reservation.id !== idString));
    // Would also need to handle deleting associated financial events
    if (selectedReservation?.id === idString) {
      setSelectedReservation(null);
      setIsViewingDetails(false);
    }
  };

  // Update a reservation
  const handleUpdateReservation = (updatedReservation: Reservation, updatedEvents: EventDetails[]) => {
    setReservations(
      reservations.map((reservation) =>
        reservation.id === updatedReservation.id ? updatedReservation : reservation
      )
    );
    
    // Update existing events and add new ones
    const updatedIncomeEvents = [...incomeEvents];
    updatedEvents.forEach(event => {
      const existingEventIndex = updatedIncomeEvents.findIndex(e => e.id === event.id);
      if (existingEventIndex >= 0) {
        updatedIncomeEvents[existingEventIndex] = event;
      } else {
        updatedIncomeEvents.push(event);
      }
    });
    
    setIncomeEvents(updatedIncomeEvents);
    setIsViewingDetails(false);
    setSelectedReservation(null);
  };

  // Check permissions loading state
  if (!permissionsLoaded) {
    return (
      <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }

  // Check if user has view permission for reservations
  if (!hasPermission("reservations", "view")) {
    return (
      <>
        <Breadcrumb pageName={t("reservations")} />
        <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
          <div className="flex items-center justify-center h-full">
            <Card className="w-full max-w-md">
              <CardContent className="text-center py-12">
                <Calendar size={48} className="mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  {t("accessDenied")}
                </h2>
                <p className="text-gray-500 dark:text-gray-400">
                  {t("noPermissionToViewReservations")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Breadcrumb pageName={t("reservations")} />

      <div className="flex flex-col gap-6 max-w-full overflow-x-auto">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-200">
            {t("Advertising Reservations")}
          </h2>
          {hasPermission("reservations", "create") && (
            <button
              onClick={() => setIsCreating(true)}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex items-center gap-2"
            >
              <FaPlus /> {t("New Reservation")}
            </button>
          )}
        </div>

        {/* Create New Reservation Form - Replaces everything when active */}
        {isCreating && hasPermission("reservations", "create") ? (
          <ReservationForm
            onSave={handleCreateReservation}
            onCancel={() => setIsCreating(false)}
          />
        ) : (
          <>
            {/* Reservation Details - Shown above list when a reservation is selected */}
            {isViewingDetails && selectedReservation && (
              <div className="mb-6 animate-fadeIn">
                <ReservationDetails
                  reservation={selectedReservation}
                  onClose={handleCloseDetails}
                  onUpdate={handleUpdateReservation}
                  onDelete={() => handleDeleteReservation(Number(selectedReservation.id))}
                  canEdit={hasPermission("reservations", "edit")}
                  canDelete={hasPermission("reservations", "delete")}
                  canExport={hasPermission("reservations", "export")}
                />
              </div>
            )}

            {/* Reservation List - Always shown when not creating */}
            <div className={isViewingDetails ? "mt-4" : ""}>
              <ReservationList
                onReservationClick={handleReservationClick}
                onDeleteReservation={handleDeleteReservation}
                canEdit={hasPermission("reservations", "edit")}
                canDelete={hasPermission("reservations", "delete")}
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default ReservationsOverview;

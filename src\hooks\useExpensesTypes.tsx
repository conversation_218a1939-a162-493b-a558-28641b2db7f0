import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api/apiClient';
import { ExpenseType } from '@/lib/interfaces/eventTypes';
import createExpenseTypeServices from '@/lib/expensesTypes';

export interface ExpenseTypeApiResponse {
    event_types: ExpenseType[];
  }


  export const useExpenseTypes= () => {
    return useQuery<ExpenseTypeApiResponse>({
      queryKey: ['event_types'],
      queryFn: async () => {
        const { data } = await apiClient.get('/expenses/api/types/');
        return data;
      },
      staleTime: 10 * 60 * 1000, 
    });
  };

  export function useExpenseTypeServices() {
    return createExpenseTypeServices(apiClient);
  }

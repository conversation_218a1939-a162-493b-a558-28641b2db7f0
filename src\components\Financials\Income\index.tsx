"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import IncomeSummary from "./IncomeSummary";
import IncomePopup from "./IncomePopup";
import UpcomingEventsPage from "@/components/Dashboard/UpcommingEventsPage/UpcommingEvents";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import {
  getDateRange,
  DateRangeSelector,
} from "@/components/Financials/common/dateRanges";
import { LoadingComp } from "@/components/common/Loading";
import { usePermissions } from "@/hooks/usePermissions";
import IncomeServices from "@/lib/income";
import { Card, CardContent } from "../../cards/card";
import { DollarSign } from "lucide-react";

const IncomeOverview: React.FC = () => {
  const { t, language } = useLanguage();
  const searchParams = useSearchParams();
  const [incomes, setIncomes] = useState<EventDetails[]>([]);
  const [selectedIncome, setSelectedIncome] = useState<EventDetails | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const { hasPermission, permissionsLoaded } = usePermissions();
  const { getIncomes } = IncomeServices();

  // Default Date Range
  const currentDate = new Date();
  const [startDate, setStartDate] = useState<Date>(
    new Date(currentDate.setDate(currentDate.getDate() - 15)),
  );
  const [endDate, setEndDate] = useState<Date>(
    new Date(new Date().setDate(new Date().getDate() + 15)),
  );

  useEffect(() => {
    const fetchIncomes = async () => {
      setLoading(true);
      try {
        const result = await getIncomes();
        console.log("Fetched incomes:", result);
        setIncomes(result || []);
      } catch (err) {
        console.error("Failed to fetch incomes:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchIncomes();
  }, []);

  // Check permissions loading state
  if (!permissionsLoaded) {
    return (
      <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }

  // Check if user has view permission for income
  if (!hasPermission("income", "view")) {
    return (
      <>
        <Breadcrumb pageName={t("income")} />
        <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
          <div className="flex items-center justify-center h-full">
            <Card className="w-full max-w-md">
              <CardContent className="text-center py-12">
                <DollarSign size={48} className="mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  {t("accessDenied")}
                </h2>
                <p className="text-gray-500 dark:text-gray-400">
                  {t("noPermissionToViewIncome")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  }

  const filterIncomesByDate = (incomes: EventDetails[]) => {
    return incomes.filter((income) => {
      // Normalize dates to remove time component for accurate comparison
      const incomeDate = new Date(income.dueDate);
      incomeDate.setHours(0, 0, 0, 0);

      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);

      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // Set to end of day for inclusive comparison

      return incomeDate >= start && incomeDate <= end;
    });
  };

  // Change the parameter type from number to string
  const handleEditClick = (id: string) => {
    const income = incomes.find((i) => i.id === id);
    if (income) setSelectedIncome(income);
  };

  const handlePopupClose = () => setSelectedIncome(null);

  const handleSavePopUp = (updatedIncome: EventDetails) => {
    setIncomes((prev) =>
      prev.map((income) =>
        income.id === updatedIncome.id ? updatedIncome : income,
      ),
    );
    handlePopupClose();
  };

  const handleDateRangeChange = (range: string) => {
    const { start, end } = getDateRange(range);
    setStartDate(new Date(start));
    setEndDate(new Date(end));
  };

  const locale = language === "ar" ? arSA : enUS;
  const eventId = searchParams ? searchParams.get("eventId") : null;

  return (
    <div className="space-y-4 sm:space-y-6 lg:space-y-8">
      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex flex-col space-y-3 sm:space-y-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-400 sm:text-2xl lg:text-3xl">
            {t("Income Overview")}
          </h2>

          {/* Date Range Controls */}
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400 sm:text-base">
              {t("as of")}
            </span>
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
              <DatePicker
                selected={startDate}
                onChange={(date: Date | null) => date && setStartDate(date)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 sm:w-auto"
                dateFormat="yyyy-MM-dd"
                locale={locale}
              />
              <span className="text-sm text-gray-500 dark:text-gray-400 sm:text-base">
                {t("to")}
              </span>
              <DatePicker
                selected={endDate}
                onChange={(date: Date | null) => date && setEndDate(date)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 sm:w-auto"
                dateFormat="yyyy-MM-dd"
                locale={locale}
              />
            </div>
          </div>
        </div>

        {/* Date Range Selector */}
        <DateRangeSelector onChange={handleDateRangeChange} />
      </div>

      {/* Content Sections */}
      <div className="space-y-4 sm:space-y-6 lg:space-y-8">
        {/* Analytics Section */}
        {hasPermission("income", "analytics") && (
          <div className="w-full">
            <IncomeSummary
              incomes={filterIncomesByDate(incomes)}
              loading={loading}
            />
          </div>
        )}

        {/* Events Section */}
        <div className="w-full">
          {loading ? (
            <div className="flex min-h-[400px] items-center justify-center rounded-lg bg-white dark:bg-gray-800">
              <LoadingComp />
            </div>
          ) : (
            <UpcomingEventsPage
              events={incomes}
              setEvents={setIncomes}
              mode="income"
              selectedEventId={eventId || undefined} // Pass the string ID directly, no need to parse to number
            />
          )}
        </div>
      </div>

      {/* Popup */}
      {selectedIncome && (
        <IncomePopup
          income={selectedIncome}
          onClose={handlePopupClose}
          onSave={handleSavePopUp}
        />
      )}
    </div>
  );
};

export default IncomeOverview;

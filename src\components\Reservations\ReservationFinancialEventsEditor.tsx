import React, { useState, useEffect } from 'react';
import { EventDetails } from '@/lib/interfaces/finaces';
import useLanguage from '@/hooks/useLanguage';
import { FaPlus, FaEdit, FaTrash, FaSave, FaTimes, FaCalendarAlt, FaDollarSign } from 'react-icons/fa';

interface ReservationFinancialEventsEditorProps {
  reservationId?: string;
  incomeEvents: EventDetails[];
  expenseEvents: EventDetails[];
  onIncomeEventsChange: (events: EventDetails[]) => void;
  onExpenseEventsChange: (events: EventDetails[]) => void;
  contactId?: string;
  reservationTitle?: string;
}

interface EditingEvent {
  id?: string;
  title: string;
  description: string;
  amount: number;
  dueDate: string;
  status: 'pending' | 'completed' | 'overdue' | 'cancelled';
  type: string;
  category: 'income' | 'expense';
  isNew?: boolean;
}

const ReservationFinancialEventsEditor: React.FC<ReservationFinancialEventsEditorProps> = ({
  reservationId,
  incomeEvents,
  expenseEvents,
  onIncomeEventsChange,
  onExpenseEventsChange,
  contactId,
  reservationTitle = 'Reservation'
}) => {
  const { t, language } = useLanguage();
  const [activeTab, setActiveTab] = useState<'income' | 'expense'>('income');
  const [editingEvent, setEditingEvent] = useState<EditingEvent | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  const statusOptions = ['pending', 'completed', 'overdue', 'cancelled'];
  const incomeTypes = ['Payment', 'Deposit', 'Installment', 'Bonus', 'Refund'];
  const expenseTypes = ['Maintenance', 'Utilities', 'Insurance', 'Supplies', 'Service', 'Other'];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-EG' : 'en-US', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US');
  };

  const handleAddNew = () => {
    const newEvent: EditingEvent = {
      title: `${reservationTitle} - ${activeTab === 'income' ? t('Payment') : t('Expense')}`,
      description: '',
      amount: 0,
      dueDate: new Date().toISOString().split('T')[0],
      status: 'pending',
      type: activeTab === 'income' ? 'Payment' : 'Other',
      category: activeTab,
      isNew: true
    };
    setEditingEvent(newEvent);
    setIsAddingNew(true);
  };

  const handleEdit = (event: EventDetails) => {
    const editEvent: EditingEvent = {
      id: event.id,
      title: event.title,
      description: event.description || '',
      amount: Number(event.amount),
      dueDate: event.dueDate.split('T')[0],
      status: event.status || 'pending',
      type: event.type || (activeTab === 'income' ? 'Payment' : 'Other'),
      category: activeTab,
      isNew: false
    };
    setEditingEvent(editEvent);
    setIsAddingNew(false);
  };

  const handleSave = () => {
    if (!editingEvent) return;

    const eventData: EventDetails = {
      id: editingEvent.id || `temp_${Date.now()}`,
      title: editingEvent.title,
      description: editingEvent.description,
      amount: editingEvent.amount,
      dueDate: editingEvent.dueDate,
      status: editingEvent.status,
      type: editingEvent.type,
      category: editingEvent.category,
      contactId: contactId || '',
      reservationId: reservationId || ''
    };

    if (activeTab === 'income') {
      if (editingEvent.isNew) {
        onIncomeEventsChange([...incomeEvents, eventData]);
      } else {
        onIncomeEventsChange(incomeEvents.map(e => e.id === editingEvent.id ? eventData : e));
      }
    } else {
      if (editingEvent.isNew) {
        onExpenseEventsChange([...expenseEvents, eventData]);
      } else {
        onExpenseEventsChange(expenseEvents.map(e => e.id === editingEvent.id ? eventData : e));
      }
    }

    setEditingEvent(null);
    setIsAddingNew(false);
  };

  const handleDelete = (eventId: string) => {
    if (window.confirm(t('Are you sure you want to delete this event?'))) {
      if (activeTab === 'income') {
        onIncomeEventsChange(incomeEvents.filter(e => e.id !== eventId));
      } else {
        onExpenseEventsChange(expenseEvents.filter(e => e.id !== eventId));
      }
    }
  };

  const handleCancel = () => {
    setEditingEvent(null);
    setIsAddingNew(false);
  };

  const currentEvents = activeTab === 'income' ? incomeEvents : expenseEvents;
  const currentTypes = activeTab === 'income' ? incomeTypes : expenseTypes;

  return (
    <div className="bg-white border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
      {/* Header */}
      <div className="p-6 border-b dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('Reservation Financial Events')}
        </h3>
        
        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('income')}
            className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'income'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FaDollarSign className="inline mr-2" />
            {t('Income Events')} ({incomeEvents.length})
          </button>
          <button
            onClick={() => setActiveTab('expense')}
            className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'expense'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FaDollarSign className="inline mr-2" />
            {t('Expense Events')} ({expenseEvents.length})
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Add New Button */}
        <div className="mb-4">
          <button
            onClick={handleAddNew}
            disabled={!!editingEvent}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FaPlus className="w-4 h-4" />
            <span>{t(`Add New ${activeTab === 'income' ? 'Income' : 'Expense'}`)}</span>
          </button>
        </div>

        {/* Editing Form */}
        {editingEvent && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
              {isAddingNew ? t(`Add New ${activeTab === 'income' ? 'Income' : 'Expense'}`) : t('Edit Event')}
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('Title')}
                </label>
                <input
                  type="text"
                  value={editingEvent.title}
                  onChange={(e) => setEditingEvent({...editingEvent, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('Type')}
                </label>
                <select
                  value={editingEvent.type}
                  onChange={(e) => setEditingEvent({...editingEvent, type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  {currentTypes.map(type => (
                    <option key={type} value={type}>{t(type)}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('Amount')}
                </label>
                <input
                  type="number"
                  value={editingEvent.amount}
                  onChange={(e) => setEditingEvent({...editingEvent, amount: Number(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('Due Date')}
                </label>
                <input
                  type="date"
                  value={editingEvent.dueDate}
                  onChange={(e) => setEditingEvent({...editingEvent, dueDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('Status')}
                </label>
                <select
                  value={editingEvent.status}
                  onChange={(e) => setEditingEvent({...editingEvent, status: e.target.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  {statusOptions.map(status => (
                    <option key={status} value={status}>{t(status)}</option>
                  ))}
                </select>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('Description')}
                </label>
                <textarea
                  value={editingEvent.description}
                  onChange={(e) => setEditingEvent({...editingEvent, description: e.target.value})}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>
            </div>
            
            <div className="flex space-x-2 mt-4">
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                <FaSave className="w-4 h-4" />
                <span>{t('Save')}</span>
              </button>
              <button
                onClick={handleCancel}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                <FaTimes className="w-4 h-4" />
                <span>{t('Cancel')}</span>
              </button>
            </div>
          </div>
        )}

        {/* Events List */}
        <div className="space-y-3">
          {currentEvents.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <FaDollarSign className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>{t(`No ${activeTab} events found`)}</p>
              <p className="text-sm">{t('Click "Add New" to create your first event')}</p>
            </div>
          ) : (
            currentEvents.map((event) => (
              <div
                key={event.id}
                className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">{event.title}</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{event.description}</p>
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${activeTab === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(Number(event.amount))}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        <FaCalendarAlt className="inline mr-1" />
                        {formatDate(event.dueDate)}
                      </p>
                    </div>
                    <div className="text-center">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        event.status === 'completed' ? 'bg-green-100 text-green-800' :
                        event.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        event.status === 'overdue' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {t(event.status || 'pending')}
                      </span>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {t(event.type || 'Payment')}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => handleEdit(event)}
                    disabled={!!editingEvent}
                    className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    title={t('Edit')}
                  >
                    <FaEdit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(event.id)}
                    disabled={!!editingEvent}
                    className="p-2 text-red-600 hover:bg-red-100 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    title={t('Delete')}
                  >
                    <FaTrash className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ReservationFinancialEventsEditor;

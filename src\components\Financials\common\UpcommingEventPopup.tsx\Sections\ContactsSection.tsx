import React from "react";
import { Contact } from "@/lib/interfaces/finaces";

interface ContactsSectionProps {
  contact: Contact;
  onRemove: () => void;
  onExpand?: () => void;
}

const ContactsSection: React.FC<ContactsSectionProps> = ({ contact, onRemove, onExpand }) => {
  return (
    <div className="mt-6">
      {/* Table Header */}
      <div className="hidden md:grid grid-cols-4 font-semibold text-gray-900 dark:text-gray-200 bg-gray-200 dark:bg-gray-700 p-3 rounded-t-lg">
        <div className="px-4">Name</div>
        <div className="px-4">Email</div>
        <div className="px-4">Phone</div>
        <div className="text-center px-4">Actions</div>
      </div>

      {/* Table Content */}
      <div className="flex flex-col">
        <div className={`grid grid-cols-1 md:grid-cols-4 items-center bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-b`}>
          <div className="px-4 py-2 font-semibold truncate">{contact.name}</div>
          <div className="px-4 py-2 truncate">{contact.email}</div>
          <div className="px-4 py-2 truncate">{contact.phone}</div>
          <div className="flex justify-center gap-4 px-4 py-2">
            <button
              className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
              onClick={() => alert("View contact details")}
            >
              View
            </button>
            <button
              className="text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300"
              onClick={onRemove}
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactsSection;
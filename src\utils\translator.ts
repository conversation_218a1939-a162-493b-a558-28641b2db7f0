import enCommon from "../../public/locales/en/common.json";
import enSidebar from "../../public/locales/en/sidebar.json";
import arCommon from "../../public/locales/ar/common.json";
import arSidebar from "../../public/locales/ar/sidebar.json";
import enOverview from "../../public/locales/en/overview.json";
import arOverview from "../../public/locales/ar/overview.json";
import enContacts from "../../public/locales/en/contacts.json";
import arContacts from "../../public/locales/ar/contacts.json";
import enCalender from "../../public/locales/en/calender.json";
import arCalender from "../../public/locales/ar/calender.json";
import enNotifications from "../../public/locales/en/notification.json";
import arNotifications from "../../public/locales/ar/notification.json";
import enModal from "../../public/locales/en/modal.json";
import arModal from "../../public/locales/ar/modal.json";
import enReservation from "../../public/locales/en/reservation.json";
import arReservation from "../../public/locales/ar/reservation.json";
import enAddEvent from "../../public/locales/en/addEvent.json";
import arAddEvent from "../../public/locales/ar/addEvent.json";
import enLocationHisotry from "../../public/locales/en/locationHistory.json";
import arLocationHisotry from "../../public/locales/ar/locationsHisotry.json";
import enSettings from "../../public/locales/en/settings.json";
import arSettings from "../../public/locales/ar/settings.json";
import enContracts from "../../public/locales/en/contracts.json";
import arContracts from "../../public/locales/ar/contracts.json";
import arDashboard from "../../public/locales/ar/dashboard.json";
import enDashboard from "../../public/locales/en/dashboard.json";
import enNotification from "../../public/locales/en/notifications.json";
import arNotification from "../../public/locales/ar/notifications.json";
import enLocations from "../../public/locales/en/locations.json";
import arLocations from "../../public/locales/ar/locations.json";
import encontact from "../../public/locales/en/contact.json";
import arcontact from "../../public/locales/ar/contact.json";
import enContract from "../../public/locales/en/contract.json";
import arContract from "../../public/locales/ar/contract.json";

const translations: Record<string, Record<string, string>> = {
  en: {
    ...enCommon,
    ...enSidebar,
    ...enOverview,
    ...enContacts,
    ...enCalender,
    ...enNotifications,
    ...enModal,
    ...enReservation,
    ...enAddEvent,
    ...enLocationHisotry,
    ...enSettings,
    ...enContracts,
    ...enDashboard,
    ...enNotification,
    ...enLocations,
    ...encontact,
    ...enContract
  },
  ar: {
    ...arCommon,
    ...arSidebar,
    ...arOverview,
    ...arContacts,
    ...arCalender,
    ...arNotifications,
    ...arModal,
    ...arReservation,
    ...arAddEvent,
    ...arLocationHisotry,
    ...arSettings,
    ...arContracts,
    ...arDashboard,
    ...arNotification,
    ...arLocations,
    ...arcontact,
    ...arContract
  },
};

export const translate = (key: string, language: string): string => {
  return translations[language]?.[key] || key; 
};

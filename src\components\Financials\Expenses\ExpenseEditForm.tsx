import React, { useState, useEffect } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import { UpdateExpensePayload, ExpenseEditResponse, ExpenseEditError, canEditExpense, formatAmountForAPI, toISOString } from "@/types/expenses";
import ExpenseServices from "@/lib/expenses";
import { toast } from "react-hot-toast";
import useLanguage from "@/hooks/useLanguage";
import { FaSave, FaTimes, FaExclamationTriangle } from "react-icons/fa";

interface ExpenseEditFormProps {
  expense: EventDetails;
  onClose: () => void;
  onSave: (updatedExpense: ExpenseEditResponse) => void;
  onError?: (error: ExpenseEditError) => void;
}

const ExpenseEditForm: React.FC<ExpenseEditFormProps> = ({
  expense,
  onClose,
  onSave,
  onError,
}) => {
  const { t } = useLanguage();
  const expenseServices = ExpenseServices();
  
  const [formData, setFormData] = useState<UpdateExpensePayload>({
    title: expense.title || "",
    amount: expense.amount || 0,
    due_date: expense.dueDate ? new Date(expense.dueDate).toISOString() : new Date().toISOString(),
    paid_date: expense.paid_date ? new Date(expense.paid_date).toISOString() : null,
    description: expense.description || "",
    status: expense.status || "pending",
    priority: expense.priority || "medium",
    type_id: expense.type || undefined,
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Check if expense can be edited
  const canEdit = canEditExpense(expense);

  useEffect(() => {
    if (!canEdit) {
      const errorMessage = expense.reservation_id 
        ? t("Cannot edit expense linked to reservation")
        : t("Cannot edit expense linked to contract");
      toast.error(errorMessage);
    }
  }, [canEdit, expense, t]);

  const handleInputChange = (field: keyof UpdateExpensePayload, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = t("Title is required");
    }

    if (formData.amount <= 0) {
      newErrors.amount = t("Amount must be greater than 0");
    }

    if (formData.amount > 9999999999.99) {
      newErrors.amount = t("Amount exceeds maximum allowed value");
    }

    if (!formData.due_date) {
      newErrors.due_date = t("Due date is required");
    }

    // Validate paid_date if status is completed
    if (formData.status === "completed" && !formData.paid_date) {
      newErrors.paid_date = t("Paid date is required for completed expense");
    }

    // Cannot set paid_date for cancelled expense
    if (formData.status === "cancelled" && formData.paid_date) {
      newErrors.paid_date = t("Cannot set paid date for cancelled expense");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!canEdit) {
      toast.error(t("This expense cannot be edited"));
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Format the payload
      const payload: UpdateExpensePayload = {
        ...formData,
        amount: formatAmountForAPI(formData.amount),
        due_date: toISOString(formData.due_date),
        paid_date: formData.paid_date ? toISOString(formData.paid_date) : null,
      };

      const updatedExpense = await expenseServices.editExpense(expense.id, payload);
      
      toast.success(t("Expense updated successfully"));
      onSave(updatedExpense);
      onClose();
    } catch (error: any) {
      console.error("Error updating expense:", error);
      
      if (error.can_update === false) {
        // Handle 409 conflict error
        if (error.reservation) {
          toast.error(t("Expense is linked to reservation: ") + error.reservation.title);
        } else if (error.contract) {
          toast.error(t("Expense is linked to contract: ") + error.contract.title);
        } else {
          toast.error(error.error || t("Cannot update this expense"));
        }
        
        if (onError) {
          onError(error as ExpenseEditError);
        }
      } else if (error.details) {
        // Handle validation errors
        const fieldErrors: Record<string, string> = {};
        Object.keys(error.details).forEach(field => {
          fieldErrors[field] = error.details[field][0] || t("Invalid value");
        });
        setErrors(fieldErrors);
        toast.error(t("Please fix the validation errors"));
      } else {
        toast.error(error.message || t("Failed to update expense"));
      }
    } finally {
      setLoading(false);
    }
  };

  if (!canEdit) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
          <div className="flex items-center gap-3 text-amber-600">
            <FaExclamationTriangle className="text-xl" />
            <h3 className="text-lg font-semibold">{t("Cannot Edit Expense")}</h3>
          </div>
          <p className="mt-3 text-gray-600 dark:text-gray-300">
            {expense.reservation_id 
              ? t("This expense is linked to a reservation and cannot be edited.")
              : t("This expense is linked to a contract and cannot be edited.")
            }
          </p>
          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="rounded-lg bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
            >
              {t("Close")}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-2xl rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
        <h2 className="mb-6 text-xl font-semibold text-gray-900 dark:text-white">
          {t("Edit Expense")}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Title")} *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              className={`w-full rounded-lg border p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.title ? "border-red-500" : "border-gray-300 dark:border-gray-600"
              }`}
              placeholder={t("Enter expense title")}
            />
            {errors.title && <p className="mt-1 text-sm text-red-500">{errors.title}</p>}
          </div>

          {/* Amount */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Amount")} *
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="9999999999.99"
              value={formData.amount}
              onChange={(e) => handleInputChange("amount", parseFloat(e.target.value) || 0)}
              className={`w-full rounded-lg border p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.amount ? "border-red-500" : "border-gray-300 dark:border-gray-600"
              }`}
              placeholder={t("Enter amount")}
            />
            {errors.amount && <p className="mt-1 text-sm text-red-500">{errors.amount}</p>}
          </div>

          {/* Due Date */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Due Date")} *
            </label>
            <input
              type="datetime-local"
              value={formData.due_date ? new Date(formData.due_date).toISOString().slice(0, 16) : ""}
              onChange={(e) => handleInputChange("due_date", new Date(e.target.value).toISOString())}
              className={`w-full rounded-lg border p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.due_date ? "border-red-500" : "border-gray-300 dark:border-gray-600"
              }`}
            />
            {errors.due_date && <p className="mt-1 text-sm text-red-500">{errors.due_date}</p>}
          </div>

          {/* Paid Date */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Paid Date")}
            </label>
            <input
              type="datetime-local"
              value={formData.paid_date ? new Date(formData.paid_date).toISOString().slice(0, 16) : ""}
              onChange={(e) => handleInputChange("paid_date", e.target.value ? new Date(e.target.value).toISOString() : null)}
              className={`w-full rounded-lg border p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.paid_date ? "border-red-500" : "border-gray-300 dark:border-gray-600"
              }`}
            />
            {errors.paid_date && <p className="mt-1 text-sm text-red-500">{errors.paid_date}</p>}
          </div>

          {/* Status */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Status")}
            </label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange("status", e.target.value as any)}
              className="w-full rounded-lg border border-gray-300 p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="pending">{t("Pending")}</option>
              <option value="completed">{t("Completed")}</option>
              <option value="cancelled">{t("Cancelled")}</option>
              <option value="upcoming">{t("Upcoming")}</option>
              <option value="overdue">{t("Overdue")}</option>
            </select>
          </div>

          {/* Priority */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Priority")}
            </label>
            <select
              value={formData.priority}
              onChange={(e) => handleInputChange("priority", e.target.value as any)}
              className="w-full rounded-lg border border-gray-300 p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="low">{t("Low")}</option>
              <option value="medium">{t("Medium")}</option>
              <option value="high">{t("High")}</option>
            </select>
          </div>

          {/* Description */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Description")}
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={3}
              className="w-full rounded-lg border border-gray-300 p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
              placeholder={t("Enter description")}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex items-center gap-2 rounded-lg bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
              disabled={loading}
            >
              <FaTimes className="text-sm" />
              {t("Cancel")}
            </button>
            <button
              type="submit"
              className="flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50"
              disabled={loading}
            >
              <FaSave className="text-sm" />
              {loading ? t("Saving...") : t("Save")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExpenseEditForm;

import React, { ReactNode } from 'react';
import { Card, CardContent } from '@/components/cards/card';
import { ArrowDownRight, ArrowUpRight } from 'lucide-react';

interface ContactOverviewCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  iconBgClass?: string;
  changeValue?: number;
  changeIsPositive?: boolean;
  progressValue?: number;
  details?: Array<{
    label: string;
    value: string | number;
  }>;
  topItems?: Array<{
    label: string;
    value: string | number;
  }>;
}

const ContactOverviewCard: React.FC<ContactOverviewCardProps> = ({
  title,
  value,
  icon,
  iconBgClass = "text-blue-500 bg-blue-50 dark:bg-blue-900/20",
  changeValue,
  changeIsPositive,
  progressValue,
  details,
  topItems
}) => {
  return (
    <Card className="bg-white dark:bg-gray-800 shadow-sm">
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</p>
            <h3 className="text-2xl font-bold mt-1">{value}</h3>
          </div>
          
          {changeValue !== undefined ? (
            <div className={`flex items-center ${changeIsPositive ? 'text-green-500' : 'text-red-500'}`}>
              {changeIsPositive 
                ? <ArrowUpRight className="h-5 w-5 mr-1" /> 
                : <ArrowDownRight className="h-5 w-5 mr-1" />
              }
              <span className="font-medium">{Math.abs(changeValue)}%</span>
            </div>
          ) : icon && (
            <div className={`${iconBgClass} p-2 rounded-full`}>
              {icon}
            </div>
          )}
        </div>
        
        {progressValue !== undefined && (
          <div className="mt-4 h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
            <div 
              className="h-full bg-blue-500 rounded-full" 
              style={{ width: `${Math.min(100, progressValue)}%` }} 
            />
          </div>
        )}
        
        {details && details.length > 0 && (
          <div className="mt-6 flex items-center justify-between text-sm">
            {details.map((detail, index) => (
              <div key={index}>
                <span className="block text-gray-500 dark:text-gray-400">{detail.label}</span>
                <span className="font-medium mt-1 block">{detail.value}</span>
              </div>
            ))}
          </div>
        )}
        
        {topItems && topItems.length > 0 && (
          <div className="mt-6 space-y-2">
            {topItems.map((item, index) => (
              <div key={index} className="flex justify-between text-sm">
                <span className="text-gray-500">{item.label}</span>
                <span className="font-medium">{item.value}</span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ContactOverviewCard;

import { Location } from './location';

export interface LocationSnapshot {
  id: string;
  locationId: string;
  timestamp: Date | string;
  lastActiveState: {
    status: 'active' | 'inactive' | 'maintenance';
    reservedBy: string;
    reservedFrom?: Date | string;
    reservedUntil?: Date | string;
  };
  financialMetrics: {
    incomingRevenue: number;
    lastRevenue: number;
    totalRevenue: number;
    revenueGrowth: number;  // Percentage growth since last period
    averageDailyRevenue?: number;
  };
  occupancyMetrics: {
    utilizationRate: number; // Percentage of time the location is reserved
    totalCapacity: number;
    usedCapacity: number;
    vacancyPeriods?: {
      startDate: Date | string;
      endDate: Date | string;
      duration: number; // in days
    }[];
  };
  performanceScore: number; // Overall performance score (0-100)
  adPerformanceMetrics?: {
    totalAds: number;
    topPerformingAdType?: string;
    averageClientSatisfaction?: number; // 0-10 rating
    estimatedReach?: number;
    actualReach?: number;
  };
  maintenanceEvents?: {
    date: Date | string;
    description: string;
    cost: number;
    downtime: number; // in days
  }[];
  seasonalPerformance?: {
    season: 'winter' | 'spring' | 'summer' | 'fall';
    year: number;
    revenueGenerated: number;
    occupancyRate: number;
    topClient?: string;
  }[];
}

function calculateOccupancyRate(location: Location): number {
  // Assuming usedCapacity is derived from some business logic
  const usedCapacity = calculateUsedCapacity(location);
  return (usedCapacity / location.capacity) * 100;
}

function calculateUsedCapacity(location: Location): number {
  // Implement your business logic here
  // For example: based on current reservations or actual usage
  return location.capacity * (location.ourPercentage / 100);
}

function calculateUtilizationRate(location: Location): number {
  return calculateOccupancyRate(location) / 100;
}

function calculateRevenueGrowth(location: Location): number {
  // if (!location.lastRevenue) return 0;
  // return ((location.incomingRevenue - location.lastRevenue) / location.lastRevenue) * 100;
  return 0;
}

function calculatePerformanceScore(location: Location): number {
  // Complex calculation based on multiple factors
  const utilizationScore = calculateUtilizationRate(location);
  const revenueScore = 0;
  const statusScore = location.status === 'active' ? 1 : 0;
  
  return (utilizationScore * 0.4 + revenueScore * 0.4 + statusScore * 0.2) * 100;
}

function getReservationHistory(location: Location): any[] {
  // This should be implemented based on your data storage solution
  return [];
}

function createLocationSnapshot(location: Location): LocationSnapshot {
  return {
    id: `snap_${Date.now()}`,
    locationId: location.id,
    timestamp: new Date(),
    financialMetrics: {
      // incomingRevenue: location.incomingRevenue,
      // lastRevenue: location.lastRevenue,
      // totalRevenue: location.totalRevenue,
      incomingRevenue: 0,
      lastRevenue: 0,
      totalRevenue: 0,
      
      revenueGrowth: calculateRevenueGrowth(location)
    },
    occupancyMetrics: {
      totalCapacity: location.capacity,
      usedCapacity: calculateUsedCapacity(location),
      utilizationRate: calculateUtilizationRate(location)
    },
    performanceScore: calculatePerformanceScore(location),
    lastActiveState: {
      status: location.status,
      reservedBy: location.reservedBy ?? ""
    }
  };
}

export { createLocationSnapshot };
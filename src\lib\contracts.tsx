import { AxiosInstance } from "axios";
import apiClient from "./api/apiClient";
import { Contract, ContractFormData } from "./interfaces/contract";
import { EventDetails } from "./interfaces/finaces";
import { toast } from "react-hot-toast";

export interface ContractEventsApiResponse {
  incomes: EventDetails[];
  expenses: EventDetails[];
}

/**
 * Service for handling contract-related API requests
 */
const ContractServices = () => {
  /**
   * Process API response to extract contracts data consistently
   */
  const processContractsResponse = (response: any): Contract[] => {
    if (!response.data) {
      return [];
    }

    if (response.data.contracts) {
      return response.data.contracts as Contract[];
    }

    if (Array.isArray(response.data)) {
      return response.data as Contract[];
    }

    return [];
  };

  /**
   * Handle common request errors
   */
  const handleRequestError = (
    error: any,
    requestName: string,
    isCancellable = true,
  ): never => {
    if (
      isCancellable &&
      error instanceof Error &&
      (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
    ) {
      console.log(`${requestName} was cancelled`);
      return [] as never;
    }

    console.error(`Error in ${requestName}:`, error);
    throw error;
  };

  return {
    /**
     * Get all contracts
     */
    getContracts: async (signal?: AbortSignal): Promise<Contract[]> => {
      try {
        const response = await apiClient.get("/contracts/api/getall/", {
          signal,
        });
        return processContractsResponse(response);
      } catch (error) {
        return handleRequestError(error, "Fetching contracts");
      }
    },

    /**
     * Get contracts by location ID
     */
    getContractsByLocation: async (
      locationId: string | number,
      signal?: AbortSignal,
    ): Promise<Contract[]> => {
      try {
        const response = await apiClient.post(
          "/contracts/api/by-location/",
          { location_id: locationId },
          { signal },
        );
        return processContractsResponse(response);
      } catch (error) {
        return handleRequestError(
          error,
          `Fetching contracts for location ${locationId}`,
        );
      }
    },

    getContractEventsById: async (
      id: string | number,
      signal?: AbortSignal,
    ): Promise<ContractEventsApiResponse> => {
      try {
        console.log(
          `Making API call to fetch reservation events for ID: ${id}...`,
        );
        const response = await apiClient.post(
          "/contracts/api/contract-finances/",
          {
            signal: signal,
            contract_id: id,
          },
        );
        console.log("Raw contract events API response:", response);

        if (!response.data) {
          console.error("Contracts events API response has no data property");
          return { incomes: [], expenses: [] };
        }

        if (
          response.data.finances.income.events &&
          response.data.finances.expenses.events
        ) {
          return {
            incomes: response.data.finances.income.events as EventDetails[],
            expenses: response.data.finances.expenses.events as EventDetails[],
          };
        }

        return { incomes: [], expenses: [] };
      } catch (error) {
        if (
          error instanceof Error &&
          (error.name === "AbortError" ||
            (error as any).code === "ECONNABORTED")
        ) {
          console.log("Contracts events API request was cancelled");
          return { incomes: [], expenses: [] };
        }
        console.error("Error fetching reservation events:", error);
        throw new Error("Error fetching reservation events");
      }
    },

    /**
     * Get contracts by contact ID
     */
    getContractsByContact: async (
      contactId: string | number,
      signal?: AbortSignal,
    ): Promise<Contract[]> => {
      try {
        const response = await apiClient.post(
          "/contracts/api/by-contact/",
          { contact_id: contactId },
          { signal },
        );
        return processContractsResponse(response);
      } catch (error) {
        return handleRequestError(
          error,
          `Fetching contracts for contact ${contactId}`,
        );
      }
    },

    /**
     * Get a contract by ID
     */
    getContractById: async (
      id: string | number,
      signal?: AbortSignal,
    ): Promise<Contract | null> => {
      try {
        const response = await apiClient.get(`/contracts/api/${id}/`, {
          signal,
        });
        if (response.data) {
          return response.data as Contract;
        }
        return null;
      } catch (error) {
        handleRequestError(error, `Fetching contract with ID ${id}`);
        return null; // This line will never be reached due to the throw in handleRequestError
      }
    },

    /**
     * Create a new contract
     */
    createContract: async (
      data: ContractFormData,
      signal?: AbortSignal,
    ): Promise<Contract | null> => {
      try {
        console.log("Creating contract with data:", data);

        const response = await apiClient.post(
          "/contracts/api/create-with-expenses/",
          data,
          { signal },
        );
        if (response.data) {
          return response.data as Contract;
        }
        return null;
      } catch (error) {
        handleRequestError(error, "Creating contract");
        return null; // This line will never be reached due to the throw in handleRequestError
      }
    },

    /**
     * Update an existing contract
     */
    updateContract: async (
      contractId: string | number,
      data: ContractFormData,
      signal?: AbortSignal,
    ): Promise<Contract | null> => {
      try {
        console.log(
          "Updating contract with ID:",
          contractId,
          "and data:",
          data,
        );

        const response = await apiClient.post(
          `/contracts/api/updatecontract/${contractId}/`,
          data,
          { signal },
        );

        if (response.data) {
          console.log("Contract update response:", response.data);
          return response.data as Contract;
        }
        return null;
      } catch (error) {
        console.error(`Error updating contract with ID ${contractId}:`, error);
        // Re-throw the error so the form can handle it properly
        throw error;
      }
    },

    /**
     * Soft delete a contract
     */
    softDeleteContract: async (contractId: string): Promise<void> => {
      try {
        console.log("Sending soft delete request for contract:", contractId);
        // Show loading indicator or buffer before the request
        const loadingToast = toast.loading("Deleting contract...");

        try {
          const response = await apiClient.post("/contracts/api/soft-delete/", {
            contract_id: contractId,
          });

          // Remove loading indicator
          toast.dismiss(loadingToast);

          // Check response status
          if (response.status === 200) {
            toast.success("Contract has been deleted successfully");
          }
        } catch (error: any) {
          // Remove loading indicator
          toast.dismiss(loadingToast);

          // Handle 409 conflict error specifically
          if (error.response && error.response.status === 409) {
            console.log("409 error data:", error.response.data);

            if (error.response.data.warning) {
              toast.error(error.response.data.warning);
            } else if (error.response.data.message) {
              toast.error(error.response.data.message);
            } else {
              toast.error("Conflict occurred while deleting contract");
            }
          } else if (error.response && error.response.data) {
            // Handle other server errors with specific messages
            const errorMessage =
              error.response.data.details ||
              error.response.data.error ||
              error.response.data.warning ||
              error.response.data.message ||
              "Error deleting contract";
            toast.error(errorMessage);
          } else {
            // Handle other errors
            toast.error("Failed to delete contract. Please try again.");
            console.error("Error soft deleting contract:", error);
          }
          throw error; // Re-throw for the caller to handle if needed
        }
      } catch (error) {
        console.error("Error soft deleting contract:", error);
        throw error;
      }
    },
  };
};

export default ContractServices;

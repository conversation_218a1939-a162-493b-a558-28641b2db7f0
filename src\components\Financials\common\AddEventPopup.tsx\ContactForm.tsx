import React, { useState } from "react";
import { Contact } from "@/lib/interfaces/finaces";
import { Button } from '@/components/ui/button';
import useLanguage from "@/hooks/useLanguage";

interface ContactFormProps {
    onClose: () => void;
    onSave: (contact: Contact) => void;
}

const ContactForm: React.FC<ContactFormProps> = ({ onClose, onSave }) => {
    const { t } = useLanguage();
    const [name, setName] = useState("");
    const [email, setEmail] = useState("");
    const [phone, setPhone] = useState("");

    const handleSave = () => {
        // Generate a unique string ID and include it in the contact object
        onSave({ 
            id: Date.now().toString(), 
            name, 
            email, 
            phone 
        });
    };

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
            <div className="bg-white dark:bg-boxdark p-8 rounded-lg shadow-lg relative w-full max-w-md">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-6">{t("Add New Contact")}</h2>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Name")}</label>
                    <input
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Email")}</label>
                    <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Phone")}</label>
                    <input
                        type="text"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
                    />
                </div>
                <div className="flex justify-end gap-4">
                    <Button variant="outline" onClick={onClose}>{t("Cancel")}</Button>
                    <Button variant="default" onClick={handleSave}>{t("Save")}</Button>
                </div>
            </div>
        </div>
    );
};

export default ContactForm;
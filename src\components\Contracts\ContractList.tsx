import React, { useState } from "react";
import useLanguage from "@/hooks/useLanguage";
import {
  FaEye,
  FaTrashAlt,
  FaTimes,
  FaSearch,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaUser,
  FaMoneyBillAlt,
  FaPercentage,
  FaFileUpload,
} from "react-icons/fa";
import { Contract } from "@/lib/interfaces/contract";

interface ContractListProps {
  contracts: Contract[];
  loading?: boolean;
  onContractClick: (id: string, contract?: Contract) => void; // Changed from number to string
  onDeleteContract: (id: string) => void; // Changed from number to string
  canEdit?: boolean;
  canDelete?: boolean;
}

const ContractList: React.FC<ContractListProps> = ({
  contracts,
  loading = false,
  onContractClick,
  onDeleteContract,
  canEdit = true,
  canDelete = true,
}) => {
  const { t, language } = useLanguage();
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState<{ start: string; end: string }>({
    start: "",
    end: "",
  });

  // Function to calculate status based on dates
  const calculateContractStatus = (
    startDate: string,
    endDate: string,
  ): string => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison

    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999); // End of day

    if (today < start) {
      return "upcoming";
    } else if (today >= start && today <= end) {
      return "active";
    } else {
      return "completed";
    }
  };

  // Apply filters to contracts
  const filteredContracts = contracts.filter((contract) => {
    const matchesSearch =
      contract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (contract.client?.name || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      (contract.location?.name || "")
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

    // Calculate dynamic status instead of using backend status
    const dynamicStatus = calculateContractStatus(
      contract.start_date,
      contract.end_date,
    );
    const matchesStatus =
      statusFilter === "all" || dynamicStatus === statusFilter;

    const start_date = dateFilter.start ? new Date(dateFilter.start) : null;
    const end_date = dateFilter.end ? new Date(dateFilter.end) : null;
    const contractStart = new Date(contract.start_date);
    const contractEnd = new Date(contract.end_date);

    const matchesDate =
      (!start_date ||
        contractStart >= start_date ||
        contractEnd >= start_date) &&
      (!end_date || contractStart <= end_date);

    return matchesSearch && matchesStatus && matchesDate;
  });
  const [showActualAmount, setShowActualAmount] = useState(false);
  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setDateFilter({ start: "", end: "" });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === "ar" ? "ar-EG" : "en-US",
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "upcoming":
        return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
      case "completed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
      case "expired":
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      case "terminated":
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      case "draft":
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  // Function to determine if we should use card view (mobile) or table view
  const [isCardView, setIsCardView] = useState(window.innerWidth < 900); // Increased breakpoint for better results

  // Listen for window resizes to toggle between card and table view
  React.useEffect(() => {
    const handleResize = () => {
      setIsCardView(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Function to handle contract click and pass the full contract data
  const handleContractClick = (id: string) => {
    // Changed from number to string
    const contract = contracts.find((c) => c.id === id);
    onContractClick(id, contract);
  };

  // Calculate contract duration in months
  const getContractDuration = (start_date: string, end_date: string) => {
    const start = new Date(start_date);
    const end = new Date(end_date);
    const months =
      (end.getFullYear() - start.getFullYear()) * 12 +
      (end.getMonth() - start.getMonth());
    return Math.max(0, months);
  };

  return (
    <div className="rounded-lg bg-white p-3 shadow dark:bg-gray-800 sm:p-6">
      {/* Loading indicator */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900 dark:border-white"></div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="py-4 text-center text-red-500">
          {error} -{" "}
          <button
            className="underline"
            onClick={() => window.location.reload()}
          >
            Try again
          </button>
        </div>
      )}

      {!loading && !error && (
        <>
          {/* Filters - Improved for mobile */}
          <div className="mb-4 sm:mb-6">
            <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:hidden">
              {t("searchAndFilters")}
            </h3>
            <div className="flex flex-col space-y-3 sm:flex-row sm:flex-wrap sm:gap-4 sm:space-y-0">
              {/* Search - Full width on mobile */}
              <div className="w-full">
                <div className="relative">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder={t("searchContracts")}
                    className="w-full rounded-lg border py-2 pl-10 pr-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
                  />
                  <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm("")}
                      className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
                    >
                      <FaTimes />
                    </button>
                  )}
                </div>
              </div>

              {/* Filter Controls */}
              <div className="grid w-full grid-cols-1 gap-3 sm:grid-cols-3">
                {/* Status Filter */}
                <div className="w-full">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
                  >
                    <option value="all">{t("allStatuses")}</option>
                    <option value="active">{t("active")}</option>
                    <option value="upcoming">{t("upcoming")}</option>
                    <option value="completed">{t("completed")}</option>
                  </select>
                </div>

                {/* Date Range */}
                <div className="grid grid-cols-2 gap-2">
                  <div className="relative">
                    <FaCalendarAlt className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
                    <input
                      type="date"
                      value={dateFilter.start}
                      onChange={(e) =>
                        setDateFilter({ ...dateFilter, start: e.target.value })
                      }
                      className="w-full rounded-lg border py-2 pl-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
                      placeholder={t("startDate")}
                    />
                  </div>
                  <div className="relative">
                    <input
                      type="date"
                      value={dateFilter.end}
                      onChange={(e) =>
                        setDateFilter({ ...dateFilter, end: e.target.value })
                      }
                      className="w-full rounded-lg border py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
                      placeholder={t("endDate")}
                    />
                  </div>
                </div>

                {/* Reset Button */}
                <button
                  onClick={resetFilters}
                  className="flex w-full items-center justify-center gap-2 rounded-lg bg-red-500 px-4 py-2 text-white transition-colors hover:bg-red-600 sm:py-3"
                >
                  <FaTimes />
                  <span>{t("resetFilters")}</span>
                </button>
              </div>
            </div>
          </div>
          <div className="mb-4 flex justify-end">
            <label className="flex cursor-pointer items-center gap-2">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {language === "ar" ? t("Our Amount") : t("Total Amount")}
              </span>
              <div className="relative h-5 w-10 rounded-full bg-gray-300">
                <input
                  type="checkbox"
                  className="sr-only"
                  checked={showActualAmount}
                  onChange={() => setShowActualAmount(!showActualAmount)}
                  aria-label={t("Toggle Actual/Total Amount")}
                />
                <div
                  className={`absolute left-0.5 top-0.5 h-4 w-4 transform rounded-full bg-white shadow transition-transform ${
                    showActualAmount ? "translate-x-5" : ""
                  }`}
                />
              </div>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {language === "ar" ? t("Total Amount") : t("Our Amount")}
              </span>
            </label>
          </div>
          {/* Card View for Mobile */}
          {isCardView ? (
            <div className="space-y-4">
              {filteredContracts.length > 0 ? (
                filteredContracts.map((contract) => {
                  const dynamicStatus = calculateContractStatus(
                    contract.start_date,
                    contract.end_date,
                  );
                  return (
                    <div
                      key={contract.id}
                      className="cursor-pointer rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
                      onClick={() => handleContractClick(contract.id)}
                    >
                      <div className="mb-2 flex items-start justify-between">
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {contract.title}
                          </h3>
                        </div>
                        <span
                          className={`rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(dynamicStatus)}`}
                        >
                          {t(
                            dynamicStatus.charAt(0).toUpperCase() +
                              dynamicStatus.slice(1),
                          )}
                        </span>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex items-center text-gray-500 dark:text-gray-400">
                          <FaUser className="mr-2" size={14} />{" "}
                          {contract.client?.name || t("Unknown Client")}
                        </div>
                        <div className="flex items-center text-gray-500 dark:text-gray-400">
                          <FaMapMarkerAlt className="mr-2" size={14} />{" "}
                          {contract.location?.name || t("Unknown Location")}
                        </div>
                        <div className="flex flex-wrap items-center text-gray-500 dark:text-gray-400">
                          <FaCalendarAlt className="mr-2" size={14} />
                          <span className="mr-1">
                            {formatDate(contract.start_date)} -{" "}
                            {formatDate(contract.end_date)}
                          </span>
                          <span className="text-xs">
                            (
                            {Number(
                              getContractDuration(
                                contract.start_date,
                                contract.end_date,
                              ),
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}{" "}
                            {t("months")})
                          </span>
                        </div>
                        <div className="flex items-center font-medium text-gray-900 dark:text-white">
                          <FaMoneyBillAlt className="mr-2" size={14} />
                          {formatCurrency(contract.monthlyAmount)} /{" "}
                          {t("month")}
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {t("due")}:{" "}
                            {Number(contract.paymentDay).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}
                            {t("th")}
                          </span>
                        </div>
                        {contract.renewal_terms?.auto_renew && (
                          <div className="flex flex-wrap items-center text-gray-500 dark:text-gray-400">
                            <FaPercentage className="mr-2" size={14} />
                            <span className="text-xs">
                              {t("autoRenews")}{" "}
                              {Number(
                                contract.renewal_terms.increase_percentage,
                              ).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}
                              % {t("increase")}
                            </span>
                          </div>
                        )}
                        {contract.document_uploaded.length > 0 && (
                          <div className="flex items-center text-gray-500 dark:text-gray-400">
                            <FaFileUpload className="mr-2" size={14} />
                            {Number(
                              contract.document_uploaded.length,
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}{" "}
                            {t("documents")}
                          </div>
                        )}
                      </div>

                      <div className="mt-3 flex justify-end space-x-2 border-t border-gray-200 pt-3 dark:border-gray-700">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleContractClick(contract.id);
                          }}
                          className="p-2 text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                          aria-label={t("viewDetails")}
                        >
                          <FaEye size={18} />
                        </button>
                        {canDelete && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (confirm(t("deleteConfirmation"))) {
                                onDeleteContract(contract.id);
                              }
                            }}
                            className="p-2 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                            aria-label={t("deleteContract")}
                          >
                            <FaTrashAlt size={18} />
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="py-8 text-center text-gray-500 dark:text-gray-400">
                  {t("noContractsFound")}
                </div>
              )}
            </div>
          ) : (
            /* Table View for Tablets and Larger */
            <div className="overflow-x-auto rounded-lg">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th
                      className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                    >
                      {t("contract")}
                    </th>
                    <th
                      className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                    >
                      {t("client")}
                    </th>
                    <th
                      className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                    >
                      {t("contractPeriod")}
                    </th>
                    <th
                      className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                    >
                      {t("totalAmount")}
                    </th>
                    <th
                      className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                    >
                      {t("status")}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                  {filteredContracts.length > 0 ? (
                    filteredContracts.map((contract) => {
                      const dynamicStatus = calculateContractStatus(
                        contract.start_date,
                        contract.end_date,
                      );
                      return (
                        <tr
                          key={contract.id}
                          className="cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700"
                          onClick={() => handleContractClick(contract.id)}
                        >
                          <td className="px-6 py-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {contract.title}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {contract.client?.name || t("Unknown Client")}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {contract.location?.name || t("Unknown Location")}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {formatDate(contract.start_date)} -{" "}
                              {formatDate(contract.end_date)}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {Number(
                                getContractDuration(
                                  contract.start_date,
                                  contract.end_date,
                                ),
                              ).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              {t("months")}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {/* {formatCurrency(contract.totalAmount)} */}
                              {showActualAmount
                                ? formatCurrency(contract.actual_amount)
                                : formatCurrency(contract.total_amount)}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <span
                              className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(dynamicStatus)}`}
                            >
                              {t(
                                dynamicStatus.charAt(0).toUpperCase() +
                                  dynamicStatus.slice(1),
                              )}
                            </span>
                          </td>
                        </tr>
                      );
                    })
                  ) : (
                    <tr>
                      <td
                        colSpan={6}
                        className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        {t("noContractsFound")}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ContractList;

import React, { useState } from "react";
import {
  FaChevronDown,
  FaChevronUp,
  FaEdit,
  FaCalendarAlt,
  FaMoneyBillAlt,
  FaUser,
  FaMapMarkerAlt,
  FaInfoCircle,
} from "react-icons/fa";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";

interface EventWithChildrenCardProps {
  event: EventDetails;
  onEdit: (id: string) => void; // Changed from number to string
  onDelete: (id: string) => void; // Changed from number to string
  onClick: (id: string) => void; // Changed from number to string
}

const EventWithChildrenCard: React.FC<EventWithChildrenCardProps> = ({
  event,
  onEdit,
  onDelete,
  onClick,
}) => {
  const { t, language } = useLanguage();
  const [expanded, setExpanded] = useState(false);

  const titleStyle =
    event.category === "income"
      ? "text-green-600 dark:text-green-400"
      : "text-red-600 dark:text-red-400";

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);

  return (
    <div
      className="cursor-pointer rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
      onClick={() => onClick(event.id)}
    >
      <div className="grid grid-cols-2 items-center gap-4 sm:grid-cols-3 md:grid-cols-6">
        <div className="flex items-center gap-2">
          <h2 className={`text-sm font-semibold ${titleStyle}`}>
            {event.title}
          </h2>
        </div>

        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaCalendarAlt className="h-4 w-4" />
          <span>
            {new Date(event.dueDate).toLocaleDateString(
              language === "ar" ? "ar-EG" : "en-US",
            )}
          </span>
        </div>

        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaMoneyBillAlt className={titleStyle} />
          <span className={titleStyle}>{formatCurrency(event.amount)}</span>
        </div>

        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaInfoCircle className="h-4 w-4" />
          <span
            className={`rounded-full px-2 py-1 text-xs font-medium ${
              event.status === "pending"
                ? "bg-yellow-100 text-yellow-600 dark:bg-yellow-800 dark:text-yellow-300"
                : event.status === "completed"
                  ? "bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-300"
                  : "bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-300"
            }`}
          >
            {t(event.status)}
          </span>
        </div>

        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <span
            className={`rounded-full px-2 py-1 text-xs font-medium ${
              event.priority === "low"
                ? "bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-300"
                : event.priority === "medium"
                  ? "bg-yellow-100 text-yellow-600 dark:bg-yellow-800 dark:text-yellow-300"
                  : "bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-300"
            }`}
          >
            {t(event.priority)}
          </span>
        </div>

        <div className="flex items-center justify-end gap-4">
          <button
            className="text-gray-500 transition-colors hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
            onClick={(e) => {
              e.stopPropagation();
              onEdit(event.id);
            }}
          >
            <FaEdit className="h-5 w-5" />
          </button>

          {event.child_count && event.child_count > 2 && (
            <button
              className="text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-white"
              onClick={(e) => {
                e.stopPropagation();
                setExpanded(!expanded);
              }}
            >
              {expanded ? <FaChevronUp /> : <FaChevronDown />}
            </button>
          )}
        </div>
      </div>

      {/* Always show up to 2 child events */}
      {event.child_events && event.child_events.length > 0 && (
        <div className="mt-4 space-y-3">
          {event.child_events.slice(0, 2).map((child) => (
            <div
              key={child.id}
              className="rounded-md border-l-4 border-blue-500 bg-blue-50 py-2 pl-4 pr-2 shadow-sm dark:border-blue-400 dark:bg-gray-700"
            >
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-semibold text-gray-800 dark:text-white">
                  {child.title}
                </h4>
                <span className="text-xs font-medium text-blue-600 dark:text-blue-300">
                  {formatCurrency(child.amount)}
                </span>
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-300">
                {new Date(child.dueDate).toLocaleDateString(language)}
              </div>
              {child.description && (
                <p className="mt-1 text-xs italic text-gray-500 dark:text-gray-400">
                  {child.description}
                </p>
              )}
            </div>
          ))}

          {event.child_events.length > 2 && !expanded && (
            <div
              className="mt-1 cursor-pointer pl-2 text-sm text-blue-600 dark:text-blue-300"
              onClick={() => setExpanded(true)}
            >
              +{event.child_events.length - 2} {t("more")}
            </div>
          )}

          {expanded &&
            event.child_events.slice(2).map((child) => (
              <div
                key={child.id}
                className="rounded-md border-l-4 border-blue-300 bg-blue-50 py-2 pl-4 pr-2 shadow-sm dark:border-blue-400 dark:bg-gray-700"
              >
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-semibold text-gray-800 dark:text-white">
                    {child.title}
                  </h4>
                  <span className="text-xs font-medium text-blue-600 dark:text-blue-300">
                    {formatCurrency(child.amount)}
                  </span>
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-300">
                  {new Date(child.dueDate).toLocaleDateString(language)}
                </div>
                {child.description && (
                  <p className="mt-1 text-xs italic text-gray-500 dark:text-gray-400">
                    {child.description}
                  </p>
                )}
              </div>
            ))}
        </div>
      )}

      {/* Child Events */}
      {expanded && event.child_events && event.child_events.length > 0 && (
        <div className="mt-4">
          <h4 className="mb-3 text-sm font-semibold text-gray-700 dark:text-gray-300">
            {t("Related Events")}
          </h4>
          <div className="space-y-2">
            {event.child_events.map((childEvent, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-600 dark:bg-gray-700"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h5 className={`text-sm font-medium ${titleStyle}`}>
                      {childEvent.title}
                    </h5>
                    <div className="mt-1 flex items-center gap-4 text-xs text-gray-600 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <FaCalendarAlt className="h-3 w-3" />
                        {new Date(childEvent.dueDate).toLocaleDateString(
                          language === "ar" ? "ar-EG" : "en-US",
                        )}
                      </span>
                      <span className="flex items-center gap-1">
                        <FaMoneyBillAlt className={titleStyle} />
                        <span className={titleStyle}>
                          {formatCurrency(childEvent.amount)}
                        </span>
                      </span>
                      <span
                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                          childEvent.status === "pending"
                            ? "bg-yellow-100 text-yellow-600 dark:bg-yellow-800 dark:text-yellow-300"
                            : childEvent.status === "completed"
                              ? "bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-300"
                              : "bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-300"
                        }`}
                      >
                        {t(childEvent.status)}
                      </span>
                    </div>

                    {/* Show payment date for completed child events */}
                    {childEvent.status === "completed" && (
                      <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                        <span className="font-medium">
                          {event.category === "income"
                            ? t("Received")
                            : t("Paid")}
                          :
                        </span>
                        {event.category === "income" &&
                        childEvent.received_date ? (
                          <span className="ml-1 text-green-600 dark:text-green-400">
                            {new Date(
                              childEvent.received_date,
                            ).toLocaleDateString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}
                          </span>
                        ) : event.category === "expense" &&
                          childEvent.paid_date ? (
                          <span className="ml-1 text-green-600 dark:text-green-400">
                            {new Date(childEvent.paid_date).toLocaleDateString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}
                          </span>
                        ) : (
                          <span className="ml-1 text-gray-500 dark:text-gray-400">
                            {t("Date not available")}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <hr className="my-4 border-gray-200 dark:border-gray-700" />

      <div className="mt-2">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaUser className="h-4 w-4" />
          <span className="font-medium">{t("Contact")}:</span>
          {event.contact ? (
            <span className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300">
              {event.contact.name}
            </span>
          ) : (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {t("No contact available")}
            </span>
          )}
        </div>
      </div>

      <div className="mt-2">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <FaMapMarkerAlt className="h-4 w-4" />
          <span className="font-medium">{t("Location")}:</span>
          {event.location ? (
            <span className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300">
              {event.location.name}
            </span>
          ) : (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {t("No location available")}
            </span>
          )}
        </div>
      </div>

      {/* Parent Event Payment Date - Only show when completed */}
      {event.status === "completed" && (
        <>
          <hr className="my-4 border-gray-200 dark:border-gray-700" />
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <FaCalendarAlt className="h-4 w-4" />
            <span className="font-medium">
              {event.category === "income"
                ? t("Received Date")
                : t("Paid Date")}
              :
            </span>
            {event.category === "income" && event.received_date ? (
              <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-700 dark:bg-green-700 dark:text-green-300">
                {new Date(event.received_date).toLocaleDateString(
                  language === "ar" ? "ar-EG" : "en-US",
                )}
              </span>
            ) : event.category === "expense" && event.paid_date ? (
              <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-700 dark:bg-green-700 dark:text-green-300">
                {new Date(event.paid_date).toLocaleDateString(
                  language === "ar" ? "ar-EG" : "en-US",
                )}
              </span>
            ) : (
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {t("Date not available")}
              </span>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default EventWithChildrenCard;

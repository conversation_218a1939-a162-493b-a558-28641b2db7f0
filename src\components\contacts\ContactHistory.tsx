"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
} from "@/components/cards/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";
import { useRouter } from "next/navigation";
import { Contact, contactLoaction } from "@/lib/types/contacts";
import { Skeleton } from "@/components/ui/skeleton";
import Breadcrumb from "../Breadcrumbs/Breadcrumb";
// Import components
import ContactStatistics from "./ContactStatistics";
import FinancialHistory from "./FinancialHistory";

// Import the types from ContactFinancialHistoryTypes

interface ContactHistoryProps {
  contactId: string;
}

// Define a mapping interface to convert between mock data and Contact type
interface MockContact {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  address?: string;
  type: string[];
  sharedLocations: { name: string }[]; // This is the structure in mock data
  ownedLocations: string[];
  createdAt: string;
  createdBy: string;
  balance: number;
}

export const ContactHistory: React.FC<ContactHistoryProps> = ({
  contactId,
}) => {
  const { t } = useLanguage();
  const router = useRouter();

  // State variables with the correct types
  const [contact, setContact] = useState<Contact | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // In production, replace these with actual API calls
        const contactData: MockContact[] = [
          {
            id: "1",
            name: "John Doe",
            email: "<EMAIL>",
            phone: "****** 567 890",
            company: "Acme Inc",
            address: "123 Main St, Springfield",
            type: ["client"],
            sharedLocations: [
              { name: "New York" },
              { name: "Chicago" },
              { name: "Miami" },
              { name: "Seattle" },
            ],
            ownedLocations: ["San Francisco"],
            createdAt: "2021-09-12",
            createdBy: "Jane Smith",
            balance: 1000,
          },
          {
            id: "2",
            name: "Jane Smith",
            email: "<EMAIL>",
            phone: "****** 654 321",
            company: "Globex Corp",
            type: ["agency"],
            sharedLocations: [{ name: "New York" }, { name: "Los Angeles" }],
            ownedLocations: ["San Francisco"],
            createdAt: "2021-09-15",
            createdBy: "John Doe",
            balance: 500,
          },
          {
            id: "3",
            name: "Alice Johnson",
            email: "<EMAIL>",
            phone: "****** 123 456",
            company: "Tech Solutions",
            address: "456 Elm St, Metropolis",
            type: ["client"],
            sharedLocations: [{ name: "Chicago" }, { name: "Austin" }],
            ownedLocations: ["Dallas"],
            createdAt: "2021-10-01",
            createdBy: "John Doe",
            balance: -750,
          },
          {
            id: "4",
            name: "Bob Williams",
            email: "<EMAIL>",
            phone: "****** 987 654",
            company: "Innovate Ltd",
            address: "789 Oak St, Gotham",
            type: ["agency"],
            sharedLocations: [{ name: "San Francisco" }, { name: "Denver" }],
            ownedLocations: ["Boston"],
            createdAt: "2021-11-03",
            createdBy: "Jane Smith",
            balance: 250,
          },
          {
            id: "5",
            name: "Charlie Brown",
            email: "<EMAIL>",
            phone: "****** 765 432",
            company: "BuildCorp",
            type: ["other"],
            sharedLocations: [{ name: "Seattle" }, { name: "San Diego" }],
            ownedLocations: ["Phoenix"],
            createdAt: "2021-12-05",
            createdBy: "Alice Johnson",
            balance: 0,
          },
          {
            id: "6",
            name: "Diane Carter",
            email: "<EMAIL>",
            phone: "****** 333 999",
            company: "Design Co.",
            type: ["client"],
            sharedLocations: [{ name: "Portland" }, { name: "Salt Lake City" }],
            ownedLocations: ["Las Vegas"],
            createdAt: "2022-01-10",
            createdBy: "Bob Williams",
            balance: 150000,
          },
          {
            id: "7",
            name: "Eve Adams",
            email: "<EMAIL>",
            phone: "****** 444 888",
            company: "Alpha Group",
            address: "123 Market St, Springfield",
            type: ["client"],
            sharedLocations: [{ name: "New York" }, { name: "Orlando" }],
            ownedLocations: ["Tampa"],
            createdAt: "2022-02-15",
            createdBy: "Charlie Brown",
            balance: -5000,
          },
          {
            id: "8",
            name: "Frank Thomas",
            email: "<EMAIL>",
            phone: "****** 222 777",
            company: "Beta Ltd",
            address: "789 Central Ave, Gotham",
            type: ["agency"],
            sharedLocations: [{ name: "Houston" }, { name: "Nashville" }],
            ownedLocations: ["Atlanta"],
            createdAt: "2022-03-20",
            createdBy: "Diane Carter",
            balance: 5000,
          },
          {
            id: "9",
            name: "Grace Lee",
            email: "<EMAIL>",
            phone: "****** 111 666",
            company: "Visionary Inc",
            address: "321 Sunset Blvd, Metropolis",
            type: ["other"],
            sharedLocations: [{ name: "Phoenix" }, { name: "Miami" }],
            ownedLocations: ["Dallas"],
            createdAt: "2022-04-25",
            createdBy: "Eve Adams",
            balance: 10000,
          },
          {
            id: "10",
            name: "Henry King",
            email: "<EMAIL>",
            phone: "****** 000 555",
            company: "NextGen Tech",
            address: "654 Beacon St, Gotham",
            type: ["client"],
            sharedLocations: [],
            ownedLocations: [],
            createdAt: "2022-05-30",
            createdBy: "Frank Thomas",
            balance: -2500,
          },
        ];

        const mockContact = contactData.find((c) => c.id === contactId) || null;

        // Transform mock data to match Contact type
        if (mockContact) {
          // Convert sharedLocations from {name: string}[] to contactLoaction[]
          const convertedSharedLocations: contactLoaction[] =
            mockContact.sharedLocations.map((loc) => ({
              id: `loc-${loc.name.toLowerCase().replace(/\s/g, "-")}`, // Generate a fake id
              name: loc.name,
              address: "", // Default empty string for missing fields
              capacity: 0,
              is_active: true,
            }));

          // Convert ownedLocations from string[] to contactLoaction[]
          const convertedOwnedLocations: contactLoaction[] =
            mockContact.ownedLocations.map((locName) => ({
              id: `loc-${locName.toLowerCase().replace(/\s/g, "-")}`,
              name: locName,
              address: "",
              capacity: 0,
              is_active: true,
            }));

          // Create properly typed Contact object
          const typedContact: Contact = {
            id: mockContact.id,
            name: mockContact.name,
            email: mockContact.email,
            phone: mockContact.phone,
            company: mockContact.company,
            address: mockContact.address || "",
            type: mockContact.type as any, // Type assertion
            sharedLocations: convertedSharedLocations,
            ownedLocations: convertedOwnedLocations,
            createdAt: mockContact.createdAt,
            createdBy: mockContact.createdBy,
            balance: mockContact.balance,
          };

          setContact(typedContact);
        } else {
          setContact(null);
        }
      } catch (error) {
        console.error("Error fetching contact history:", error);
        setContact(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [contactId]);

  return (
    <div className="space-y-6">
      <Breadcrumb pageName={t("contactHistory")} />

      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ChevronLeft className="mr-1 h-4 w-4" />
          {t("back")}
        </Button>
      </div>

      {/* ContactStatistics component */}
      <ContactStatistics contactId={contactId} contactName={contact?.name} />

      {/* Pass the correct prop: ContactId instead of name */}
      <FinancialHistory ContactId={contactId} isLoading={isLoading} />
    </div>
  );
};

export default ContactHistory;

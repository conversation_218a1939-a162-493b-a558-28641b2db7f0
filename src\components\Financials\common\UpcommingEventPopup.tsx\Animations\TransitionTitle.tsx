import React from "react";

interface TransitionTitleProps {
  title: string;
  isExpanded: boolean;
  onExpand: () => void;
}

const TransitionTitle: React.FC<TransitionTitleProps> = ({ title, isExpanded, onExpand }) => {
  return (
    <div
      className={`text-center relative transition-all duration-500 ${
        isExpanded ? "text-left -top-10" : ""
      }`}
      onClick={onExpand}
    >
      <hr className="border-gray-300 dark:border-gray-700 my-4" />
      <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">{title}</h3>
      <hr className="border-gray-300 dark:border-gray-700 my-4" />
    </div>
  );
};

export default TransitionTitle;
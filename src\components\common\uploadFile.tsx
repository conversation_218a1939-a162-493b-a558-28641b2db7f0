"use client";
import React, { useRef, useState } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Database } from "types/supabase";
type Props = {
  onUploadComplete: (urls: string[]) => void;
};

const UploadFiles: React.FC<Props> = ({ onUploadComplete }) => {
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const supabase = createClientComponentClient<Database>();

  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    console.log("Files to upload:", files);
    
    setUploading(true);

    const urls: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const filePath = `${Date.now()}-${file.name}`;

      const { error } = await supabase.storage
        .from("sarayvera-documents")
        .upload(filePath, file);

      if (error) {
        console.error("Upload error:", error.message);
        continue;
      }

      const {
        data: { publicUrl },
      } = supabase.storage.from("sarayvera-documents").getPublicUrl(filePath);

      urls.push(publicUrl);
    }
    console.log("Uploaded URLs:", urls);
    

    onUploadComplete(urls);
    setUploading(false);
  };

  return (
    <div>
      <label className="block mb-2 text-sm font-medium text-gray-700">
        Upload Documents
      </label>
      <input
        type="file"
        multiple
        ref={fileInputRef}
        onChange={handleUpload}
        disabled={uploading}
        className="block w-full text-sm text-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-xl file:border-0 file:text-sm file:font-semibold file:bg-blue-100 hover:file:bg-blue-200"
      />
      {uploading && <p className="text-blue-500 mt-2">Uploading...</p>}
    </div>
  );
};

export default UploadFiles;

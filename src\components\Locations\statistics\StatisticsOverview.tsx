import React from 'react';
import StatOverviewCard from './StatOverviewCard';
import { Eye, Layers, Users } from 'lucide-react';
import useLanguage from "@/hooks/useLanguage";

interface StatisticsOverviewProps {
  totalRevenue: number;
  revenueGrowth: number;
  averageOccupancy: number;
  totalImpressions: number;
  totalClients: number;
}

const StatisticsOverview: React.FC<StatisticsOverviewProps> = ({
  totalRevenue,
  revenueGrowth,
  averageOccupancy,
  totalImpressions,
  totalClients,
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
      {/* Revenue Card */}
      <StatOverviewCard
        title={t("totalRevenue")}
        value={`$${totalRevenue.toLocaleString()}`}
        changeValue={revenueGrowth}
        changeIsPositive={revenueGrowth >= 0}
        progressValue={Math.min(100, Math.abs(revenueGrowth) + 50)}
        icon={<Eye className="h-5 w-5" />}
        iconBgClass="text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20"
      />
      
      {/* Occupancy Card */}
      <StatOverviewCard
        title={t("averageOccupancy")}
        value={`${averageOccupancy}%`}
        icon={<Layers className="h-5 w-5" />}
        iconBgClass="text-blue-500 bg-blue-50 dark:bg-blue-900/20"
        progressValue={averageOccupancy}
      />
      
      {/* Impressions Card */}
      <StatOverviewCard
        title={t("totalImpressions")}
        value={`${(totalImpressions / 1000000).toFixed(1)}M`}
        icon={<Eye className="h-5 w-5" />}
        iconBgClass="text-purple-500 bg-purple-50 dark:bg-purple-900/20"
        details={[
          { label: t("viewRate"), value: "78%" },
          { label: t("engagementRate"), value: "6.2%" },
          { label: t("conversionRate"), value: "1.4%" },
        ]}
      />
      
      {/* Clients Card */}
      <StatOverviewCard
        title={t("activeClients")}
        value={totalClients}
        icon={<Users className="h-5 w-5" />}
        iconBgClass="text-green-500 bg-green-50 dark:bg-green-900/20"
        details={[
          { label: t("newClients"), value: "+3" },
          { label: t("returningClients"), value: `${totalClients - 3}` },
          { label: t("retentionRate"), value: "85%" },
        ]}
      />
    </div>
  );
};

export default StatisticsOverview;

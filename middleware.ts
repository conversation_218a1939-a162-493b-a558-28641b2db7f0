import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import type { NextRequest } from "next/server";
import { authService } from "@/lib/api/auth";

export async function middleware(request: NextRequest) {
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });
  const { pathname } = request.nextUrl;

  console.log(`\n[Middleware] Path: ${pathname}`);
  console.log('[Middleware] Token:', token?.role); 

  if (!token) {
    console.log('[Middleware] No token - redirecting to login');
    return NextResponse.redirect(new URL("/auth/signin", request.url));
  }

  if (typeof token.exp === "number" && Date.now() / 1000 > token.exp) {
    console.log('[Middleware] Token expired - attempting refresh');
    try {
      if (!token.refreshToken) throw new Error("No refresh token available");
      
      if (typeof token.refreshToken !== 'string') {
        throw new Error("Invalid refresh token");
      }
      const newTokens = await authService.refreshToken(token.refreshToken);
      
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('Authorization', `Bearer ${newTokens.accessToken}`);
      
      const response = NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });
      
      response.cookies.set('next-auth.session-token', newTokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
      });
      
      if (newTokens.refreshToken) {
        response.cookies.set('next-auth.refresh-token', newTokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
        });
      }
      
      return response;
    } catch (error) {
      console.error('[Middleware] Refresh failed:', error);
      await authService.logout();
      return NextResponse.redirect(
        new URL(`/auth/signin?error=session_expired`, request.url)
      );
    }
  }

  const rolePermissions: Record<string, string[]> = {
    '/admin': ['admin'],
    '/dashboard': ['admin', 'user'],
    '/settings': ['admin', 'user'],
    '/finances': ['user'],
  };

  const [matchedPath, requiredRoles] = Object.entries(rolePermissions).find(
    ([path]) => pathname.startsWith(path) || 
               pathname === path || 
               pathname.startsWith(`${path}/`)
  ) || [null, null];

  console.log('[Middleware] Matched path:', matchedPath);
  console.log('[Middleware] Required roles:', requiredRoles);

  if (requiredRoles && !requiredRoles.some(role => 
    role.toLowerCase() === (token.role as string)?.toLowerCase()
  )) {
    console.log(`[Middleware] Access denied for ${token.role} to ${matchedPath}`);
    return NextResponse.redirect(new URL("/unauthorized", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/admin/:path*',
    '/dashboard/:path*',
    '/settings/:path*',
    '/finances/:path*',
  ],
};
"use client";
import React, { useState, useEffect } from 'react';
import { X, User, Mail, AtSign, Upload, Clipboard } from 'lucide-react';
import { UserDetails, UserPermissions } from '@/lib/interfaces/userDetails';
import useLanguage from "@/hooks/useLanguage";
import { createPortal } from 'react-dom';

interface UserFormProps {
  isOpen: boolean;
  onClose: () => void;
  user?: UserDetails;
  onSubmit: (userData: Partial<UserDetails>) => void;
  view?: 'modal' | 'inline';
}

const UserForm: React.FC<UserFormProps> = ({ isOpen, onClose, user, onSubmit, view = 'modal' }) => {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);
  const [formData, setFormData] = useState<Partial<UserDetails>>({
    username: '',
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    role: 'USER',
  });
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [idImagePreview, setIdImagePreview] = useState<string | null>(null);
const [permissionsData, setPermissionsData] = useState<UserPermissions>({
  dashboard: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  reservations: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  financials: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  contacts: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  settings: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  users: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
    manage_accounts: false,
    view_activity_log: false,
  },
  contracts: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  locations: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  income: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  expenses: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
  calendar: {
    sidebar: false,
    view: false,
    create: false,
    edit: false,
    delete: false,
    export: false,
    import_data: false,
    approve: false,
    reject: false,
    analytics: false,
    notifications: false,
    view_history: false,
  },
});
  const [activeTab, setActiveTab] = useState<'info' | 'permissions'>('info');
  const [showPassword, setShowPassword] = useState(false); // State to toggle password visibility
  const [confirmPassword, setConfirmPassword] = useState(""); // State for confirm password field

  useEffect(() => {
    console.log(user);
    
    setMounted(true);
    if (isOpen && view === 'modal') {
      document.body.style.overflow = 'hidden';
    }
    
    return () => {
      if (view === 'modal') {
        document.body.style.overflow = 'unset';
      }
    };
  }, [isOpen, view]);

  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        role: user.role || 'USER',
        password: '',
      });

      if (user.permissions) {
        setPermissionsData(user.permissions);
      }
    } else {
      // Reset form for new user
      setFormData({
        username: '',
        first_name: '',
        last_name: '',
        email: '',
        password: '',
        role: 'USER',
      });
      setImagePreview(null);
      setPermissionsData({
        dashboard: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        reservations: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        financials: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        contacts: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        settings: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        users: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false,
          manage_accounts: false,
          view_activity_log: false
        },
        contracts: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        locations: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        income: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        expenses: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        calendar: {
          sidebar: false,
          view: false,
          create: false,
          edit: false,
          delete: false,
          export: false,
          import_data: false,
          approve: false,
          reject: false,
          analytics: false,
          notifications: false,
          view_history: false
        },
        
      });
    }
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        setImagePreview(result);
        setFormData(prev => ({ ...prev, image: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePermissionChange = (
    module: string,
    permission: string,
    value: boolean
  ) => {
    setPermissionsData(prev => ({
      ...prev,
      [module]: {
        ...prev[module as keyof UserPermissions],
        [permission]: value
      }
    }));
  };

  const handleAdminToggle = (isAdmin: boolean) => {
    const newPermissions = Object.keys(permissionsData).reduce((acc, module) => {
      return {
        ...acc,
        [module]: Object.keys(permissionsData[module as keyof UserPermissions]).reduce((modAcc, perm) => {
          return {
            ...modAcc,
            [perm]: isAdmin
          };
        }, {} as UserPermissions[keyof UserPermissions])
      };
    }, {} as UserPermissions);
    
    setPermissionsData(newPermissions);
    setFormData(prev => ({ ...prev, role: isAdmin ? 'ADMIN' : 'USER' }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.password !== confirmPassword) {
      alert(t("passwordMismatch")); // Show error if passwords don't match
      return;
    }

    const userData: Partial<UserDetails> = {
      ...formData,
      permissions: permissionsData,
    };
    
    // Remove password if empty (for editing)
    if (userData.password === '') {
      delete userData.password;
    }
    
    onSubmit(userData);
    onClose();
  };

  const generatePassword = () => {
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+[]{}|;:,.<>?";
    let password = "";
    for (let i = 0; i < 16; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }
    return password;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert(t("passwordCopied")); // Optional: Show a confirmation message
    });
  };

  if (!mounted || !isOpen) return null;

  const renderForm = () => (
    <form onSubmit={handleSubmit}>
      <div className="overflow-y-auto max-h-[60vh] sm:max-h-[70vh] p-3 sm:p-6">
        {activeTab === 'info' ? (
          <div className="space-y-5 sm:space-y-6">
            <div className="flex flex-col md:flex-row gap-4 md:gap-6">


              <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("firstName")} *
                  </label>
                  <input
                    required
                    type="text"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-lg bg-white text-gray-900"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("lastName")} *
                  </label>
                  <input
                    required
                    type="text"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-lg bg-white text-gray-900"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("username")} *
                  </label>
                  <input
                    required
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-lg bg-white text-gray-900"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("email")} *
                  </label>
                  <input
                    required
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-lg bg-white text-gray-900"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("role")} *
                  </label>
                  <select
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-gray-300 rounded-lg bg-white text-gray-900"
                  >
                    <option value="USER">User</option>
                    <option value="ADMIN">Admin</option>
                    <option value="MANAGER">Manager</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {user ? t("newPassword") : t("password")} {user ? '' : '*'}
                  </label>
                  <div className="flex items-center gap-2">
                    <input
                      required={!user}
                      type={showPassword ? "text" : "password"} // Toggle between text and password
                      name="password"
                      value={formData.password || ''}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-lg bg-white text-gray-900"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword((prev) => !prev)} // Toggle password visibility
                      className="px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
                    >
                      {showPassword ? t("hide") : t("view")}
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const newPassword = generatePassword();
                        setFormData((prev) => ({ ...prev, password: newPassword }));
                      }}
                      className="px-3 py-2 bg-primary text-white rounded-lg hover:bg-primary/90"
                    >
                      {t("generate")}
                    </button>
                    {formData.password && (
                      <button
                        type="button"
                        onClick={() => formData.password && copyToClipboard(formData.password)}
                        className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                      >
                        <Clipboard size={16} className="text-gray-600" />
                      </button>
                    )}
                  </div>
                </div>

                {/* Confirm Password Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t("confirmPassword")} *
                  </label>
                  <input
                    required
                    type="password"
                    name="confirmPassword"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)} // Update confirm password state
                    className="w-full p-2 border border-gray-300 rounded-lg bg-white text-gray-900"
                  />
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Admin Toggle */}
            <div className="mb-6 bg-gray-50 p-3 sm:p-4 rounded-lg border border-gray-200">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
                <div>
                  <h3 className="font-medium text-gray-900">{t("administrator")}</h3>
                  <p className="text-sm text-gray-500 mt-1 sm:mt-0">{t("adminDescription")}</p>
                </div>
                <label className="flex items-center cursor-pointer mt-2 sm:mt-0">
                  <input 
                    type="checkbox"
                    checked={formData.role === 'ADMIN'}
                    onChange={(e) => handleAdminToggle(e.target.checked)}
                    className="sr-only"
                  />
                  <div className={`relative w-14 h-7 transition-colors duration-200 ease-linear rounded-full ${formData.role === 'ADMIN' ? 'bg-primary' : 'bg-gray-300'}`}>
                    <div className={`absolute left-1 top-1 bg-white w-5 h-5 transition-transform duration-200 ease-linear rounded-full transform ${formData.role === 'ADMIN' ? 'translate-x-7' : ''}`}></div>
                  </div>
                </label>
              </div>
            </div>

            {/* Permission Groups */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 sm:gap-6">
              {Object.entries(permissionsData).map(([module, permissions]) => (
                <div key={module} className="grid-col-2 bg-gray-50 p-3 sm:p-4 rounded-lg border border-gray-200">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="font-medium text-sm text-gray-900 capitalize">
                      {t(module)} {/* Ensure translation exists for all modules */}
                    </h4>
                    <button
                      type="button"
                      onClick={() => {
                        const allEnabled = Object.values(permissions).every(value => value);
                        const updatedPermissions = Object.keys(permissions).reduce((acc, key) => {
                          return { ...acc, [key]: !allEnabled };
                        }, {} as UserPermissions[keyof UserPermissions]);
                        setPermissionsData(prev => ({ ...prev, [module]: updatedPermissions }));
                      }}
                      className={`text-xs px-2 py-1 rounded-lg ${
                        Object.values(permissions).every(value => value)
                          ? 'bg-primary text-white'
                          : 'bg-gray-200 text-gray-700'
                      }`}
                    >
                      {Object.values(permissions).every(value => value) ? t("deselectAll") : t("selectAll")}
                    </button>
                  </div>
                  <div className="space-y-2 max-h-[200px] overflow-y-auto pr-1 sm:pr-2">
                    {Object.entries(permissions).map(([permission, value]) => (
                      <div key={permission} className="flex items-center justify-between">
                        <span className="text-sm capitalize text-gray-800">
                          {t(permission)} {/* Ensure translation exists for all permissions */}
                        </span>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => handlePermissionChange(module, permission, e.target.checked)}
                            className="sr-only"
                          />
                          <div className={`w-9 h-5 rounded-full transition-colors ${value ? 'bg-primary' : 'bg-gray-300'}`}>
                            <div className={`absolute w-3.5 h-3.5 bg-white rounded-full transform transition-transform top-0.75 ${value ? 'translate-x-5' : 'translate-x-0.5'}`} style={{ top: '0.1875rem' }}></div>
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="p-4 sm:p-6 border-t border-gray-200 flex flex-col-reverse sm:flex-row justify-end gap-3 sm:gap-4">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 w-full sm:w-auto"
        >
          {t("cancel")}
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 w-full sm:w-auto"
        >
          {user ? t("saveChanges") : t("createUser")}
        </button>
      </div>
    </form>
  );

  // Modal view
  if (view === 'modal') {
    return createPortal(
      <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
        <div className="relative w-full max-w-2xl">
          <div className="w-full bg-white rounded-lg shadow-xl overflow-hidden">
            <div className="flex justify-between items-center p-4 sm:p-6 border-b border-gray-200">
              <h2 className="text-lg sm:text-xl font-bold">
                {user ? t('editUser') : t('addNewUser')}
              </h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X size={20} className="text-gray-600" />
              </button>
            </div>
            
            {/* Tab Navigation */}
            {user && (
              <div className="flex border-b border-gray-200">
                <button
                  className={`px-4 py-2 text-sm font-medium flex-1 ${
                    activeTab === 'info'
                      ? 'border-b-2 border-primary text-primary'
                      : 'text-gray-500'
                  }`}
                  onClick={() => setActiveTab('info')}
                >
                  {t("userInformation")}
                </button>
                <button
                  className={`px-4 py-2 text-sm font-medium flex-1 ${
                    activeTab === 'permissions'
                      ? 'border-b-2 border-primary text-primary'
                      : 'text-gray-500'
                  }`}
                  onClick={() => setActiveTab('permissions')}
                >
                  {t("permissions")}
                </button>
              </div>
            )}

            {renderForm()}
          </div>
        </div>
      </div>,
      document.body
    );
  }

  // Inline view
  return (
    <div className="bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center p-4 sm:p-6 border-b border-gray-200">
        <h2 className="text-lg sm:text-xl font-bold">
          {user ? t('editUser') : t('addNewUser')}
        </h2>
      </div>
            
      {user && (
        <div className="flex border-b border-gray-200">
          <button
            className={`px-4 py-2 text-sm font-medium flex-1 ${
              activeTab === 'info'
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-500'
            }`}
            onClick={() => setActiveTab('info')}
          >
            {t("userInformation")}
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium flex-1 ${
              activeTab === 'permissions'
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-500'
            }`}
            onClick={() => setActiveTab('permissions')}
          >
            {t("permissions")}
          </button>
        </div>
      )}

      {renderForm()}
    </div>
  );
};

export default UserForm;
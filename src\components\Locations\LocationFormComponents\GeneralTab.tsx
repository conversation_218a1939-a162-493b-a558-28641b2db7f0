import React, { useState } from "react";
import { X, Plus, Check } from "lucide-react";
import { GeneralTabProps } from "./types";
import handleAddCustomType from "../LocationForm";
import { CreateLocationTypePayload } from "@/lib/locations";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocationServices } from "@/hooks/useLocations";

const GeneralTab: React.FC<GeneralTabProps> = ({
  formData,
  handleChange,
  locationTypesWithIds,
  selectedTypeId,
  setSelectedTypeId,
  typesLoading,
  t,
}) => {
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState<boolean>(false);
  const [isAddingCustomType, setIsAddingCustomType] = useState<boolean>(false);
  const [customType, setCustomType] = useState<string>("");
  const [searchTypeValue, setSearchTypeValue] = useState<string>("");
  const [filteredTypeOptions, setFilteredTypeOptions] = useState<string[]>(
    locationTypesWithIds.map((type) => type.name),
  );
  const locationServices = useLocationServices();
  const queryClient = useQueryClient();

  const handleTypeSelect = (typeName: string) => {
    console.log(`Attempting to select type: ${typeName}`);
    console.log(`Available types:`, locationTypesWithIds);

    handleChange("type", typeName);

    // Find the type object with this name to get its ID
    const typeObject = locationTypesWithIds.find((t) => t.name === typeName);
    if (typeObject) {
      setSelectedTypeId(typeObject.id);
      console.log(
        `Successfully selected type: ${typeName}, ID: ${typeObject.id}`,
      );
    } else {
      console.warn(`No ID found for selected type: ${typeName}`);
      console.warn(
        `Available types:`,
        locationTypesWithIds.map((t) => ({ name: t.name, id: t.id })),
      );
      // Create a fallback ID based on the name if needed
      setSelectedTypeId("");
    }

    setIsTypeDropdownOpen(false);
  };

  // Create location type mutation
  const createTypeMutation = useMutation({
    mutationFn: async (payload: CreateLocationTypePayload) => {
      return await locationServices.createLocationType(payload);
    },
    onSuccess: (newType) => {
      // Invalidate and refetch the location types
      queryClient.invalidateQueries({ queryKey: ["locationTypes"] });

      // The parent component will handle updating locationTypesWithIds through the query refetch
      console.log("Successfully created new location type:", newType);
    },
    onError: (error) => {
      console.error("Error creating location type:", error);
    },
  });

  const handleAddCustomType = async () => {
    if (customType.trim()) {
      try {
        // Create payload
        const payload: CreateLocationTypePayload = {
          name: customType.trim().toLowerCase(),
        };

        // Use the mutation instead of directly calling the API
        const newType = await createTypeMutation.mutateAsync(payload);

        // Set the type in the form data immediately for UX responsiveness
        handleChange("type", customType.trim().toLowerCase());

        // Set the selectedTypeId to the newly created type's ID
        if (newType && newType.id) {
          setSelectedTypeId(newType.id);
          console.log(
            `Created and selected new type: ${customType.trim().toLowerCase()}, ID: ${newType.id}`,
          );
        }

        // Reset the custom type input
        setCustomType("");
        setIsAddingCustomType(false);
      } catch (error) {
        // Error is handled by the mutation's onError
      }
    }
  };

  // Filter types when search value changes
  React.useEffect(() => {
    if (locationTypesWithIds.length > 0) {
      setFilteredTypeOptions(
        locationTypesWithIds
          .map((type) => type.name)
          .filter((type) =>
            type.toLowerCase().includes(searchTypeValue.toLowerCase()),
          ),
      );
    }
  }, [locationTypesWithIds, searchTypeValue]);

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("locationName")}
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => handleChange("name", e.target.value)}
          className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
        />
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("locationType")}
        </label>
        <div className="relative">
          {isAddingCustomType ? (
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={customType}
                onChange={(e) => setCustomType(e.target.value)}
                className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                placeholder={t("enterCustomType")}
                autoFocus
              />
              <button
                type="button"
                onClick={() => handleAddCustomType()}
                className="rounded-lg bg-blue-600 p-2 text-white hover:bg-blue-700"
              >
                <Check size={16} />
              </button>
              <button
                type="button"
                onClick={() => setIsAddingCustomType(false)}
                className="rounded-lg bg-gray-200 p-2 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                <X size={16} />
              </button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <div
                className="w-full cursor-pointer rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                onClick={() => setIsTypeDropdownOpen((prev) => !prev)}
              >
                {formData.type
                  ? formData.type.charAt(0).toUpperCase() +
                    formData.type.slice(1)
                  : t("selectType")}
              </div>
              <button
                type="button"
                onClick={() => setIsAddingCustomType(true)}
                className="rounded-lg bg-blue-600 p-2 text-white hover:bg-blue-700"
                title={t("addCustomType")}
              >
                <Plus size={16} />
              </button>
            </div>
          )}

          {isTypeDropdownOpen && !isAddingCustomType && (
            <div className="absolute z-10 mt-2 max-h-40 w-full overflow-hidden rounded-lg border bg-white shadow-md dark:bg-gray-800">
              <input
                type="text"
                className="w-full border-b p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                placeholder={t("searchTypes")}
                value={searchTypeValue}
                onChange={(e) => setSearchTypeValue(e.target.value)}
              />

              <ul className="max-h-32 overflow-y-auto">
                {typesLoading ? (
                  <li className="p-2 text-center text-gray-500">
                    {t("loading")}
                  </li>
                ) : filteredTypeOptions.length > 0 ? (
                  filteredTypeOptions.map((option) => (
                    <li
                      key={option}
                      onClick={() => handleTypeSelect(option)}
                      className={`flex cursor-pointer items-center p-2 hover:bg-blue-100 dark:hover:bg-gray-700 ${
                        formData.type === option
                          ? "bg-blue-50 dark:bg-gray-700"
                          : ""
                      }`}
                    >
                      <input
                        type="radio"
                        checked={formData.type === option}
                        onChange={() => handleTypeSelect(option)}
                        className="pointer-events-none mr-2"
                      />
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </li>
                  ))
                ) : (
                  <li className="p-2 text-center text-gray-500">
                    {t("noTypesFound")}
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("status")}
        </label>
        <select
          value={formData.status}
          onChange={(e) => handleChange("status", e.target.value)}
          className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
        >
          <option value="active">{t("active")}</option>
          <option value="inactive">{t("inactive")}</option>
        </select>
      </div>

      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("capacity")}
        </label>
        <input
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          value={formData.capacity || ""}
          onChange={(e) => {
            const value = e.target.value;
            // Only allow numbers
            if (value === "" || /^\d+$/.test(value)) {
              handleChange("capacity", value === "" ? "" : parseInt(value));
            }
          }}
          onKeyDown={(e) => {
            // Prevent arrow keys from incrementing/decrementing
            if (e.key === "ArrowUp" || e.key === "ArrowDown") {
              e.preventDefault();
            }
          }}
          className="w-full rounded-lg border p-2 [-moz-appearance:textfield] focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
          placeholder={t("enterCapacity")}
        />
      </div>

      <div className="md:col-span-2">
        <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("address")}
        </label>
        <input
          type="text"
          value={formData.address}
          onChange={(e) => handleChange("address", e.target.value)}
          className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
        />
      </div>

      <div className="md:col-span-2">
        <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("description")}
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => handleChange("description", e.target.value)}
          rows={4}
          className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
          placeholder={t("enterLocationDescription")}
        />
      </div>
    </div>
  );
};

export default GeneralTab;

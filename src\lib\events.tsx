import IncomeServices from "./income";
import ExpenseServices from "./expenses";
import { EventDetails } from "./interfaces/finaces";

const getCombinedEvents = async (): Promise<EventDetails[]> => {
  try {
    // Fetch incomes and expenses
    var incomes = await IncomeServices().getIncomes();
    var expenses = await ExpenseServices().getExpenses();
    console.log("Fetched incomes:", incomes);
    console.log("Fetched expenses:", expenses);
    
    if(incomes?.length === 0 || !incomes) {
        console.log("No incomes found.");
        incomes = [];
    }
    if(expenses.length === 0 ) {
        console.log("No expenses found.");
        expenses = [];
    }
    const combinedEvents = [...incomes, ...expenses];

    // Sort the combined array by dueDate
    combinedEvents.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());

    return combinedEvents;
  } catch (error) {
    console.error("Error fetching combined events:", error);
    throw new Error("Error fetching combined events");
  }
};

export default getCombinedEvents;
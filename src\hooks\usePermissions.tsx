'use client';

import { useSession } from "next-auth/react";
import { PermissionService } from "@/lib/permissions";
import { Permissions } from "@/lib/types/auth";
import { useEffect, useState } from "react";

export const usePermissions = () => {
  const { data: session , status} = useSession();
  
  const permissions = session?.permissions || {} as Permissions;
  
  const permissionService = new PermissionService(permissions);
  const [permissionsLoaded, setPermissionsLoaded] = useState(false);
  
  
  useEffect(() => {
    if (status === 'authenticated') {
      setPermissionsLoaded(true);
    } else if (status === 'unauthenticated') {
        window.location.href = '/auth/signin';
    }
  }, [status]);

  return {
    hasPermission: permissionService.hasPermission.bind(permissionService),
    hasCategoryAccess: permissionService.hasCategoryAccess.bind(permissionService),
    getAllPermissions: permissionService.getAllPermissions.bind(permissionService),
    permissionsLoaded,
    hasAllPermissions: permissionService.hasAllPermissions.bind(permissionService),
  };
};
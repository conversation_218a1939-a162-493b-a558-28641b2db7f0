import React, { useState, useMemo } from "react";
import { Composed<PERSON>hart, Bar, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from "recharts";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";
import { 
    processEventsToMonthlyData, 
    formatCurrency, 
    formatNumber, 
    getChartConfig, 
    filterValidEvents,
    calculateGrowthRate,
    calculateTrend,
    CHART_COLORS
} from "@/utils/analyticsUtils";
import { TrendingUp, TrendingDown, Minus, Target, DollarSign, Calendar, BarChart3 } from "lucide-react";

interface AdvancedAnalyticsSectionProps {
    events: EventDetails[];
}

const AdvancedAnalyticsSection: React.FC<AdvancedAnalyticsSectionProps> = ({ events }) => {
    const { t, language } = useLanguage();
    const [timeframe, setTimeframe] = useState<"6m" | "12m" | "24m">("12m");
    const [analysisType, setAnalysisType] = useState<"trend" | "forecast" | "comparison">("trend");

    // Process and validate events
    const validEvents = useMemo(() => filterValidEvents(events), [events]);
    const monthlyData = useMemo(() => processEventsToMonthlyData(validEvents, language), [validEvents, language]);

    // Filter data based on timeframe
    const filteredData = useMemo(() => {
        const months = timeframe === "6m" ? 6 : timeframe === "12m" ? 12 : 24;
        return monthlyData.slice(-months);
    }, [monthlyData, timeframe]);

    // Advanced analytics calculations
    const analytics = useMemo(() => {
        if (filteredData.length < 2) return null;

        const currentPeriod = filteredData.slice(-6);
        const previousPeriod = filteredData.slice(-12, -6);

        const currentIncome = currentPeriod.reduce((sum, item) => sum + item.income, 0);
        const currentExpense = currentPeriod.reduce((sum, item) => sum + item.expense, 0);
        const previousIncome = previousPeriod.reduce((sum, item) => sum + item.income, 0);
        const previousExpense = previousPeriod.reduce((sum, item) => sum + item.expense, 0);

        const incomeGrowth = calculateGrowthRate(currentIncome, previousIncome);
        const expenseGrowth = calculateGrowthRate(currentExpense, previousExpense);
        const profitGrowth = calculateGrowthRate(currentIncome - currentExpense, previousIncome - previousExpense);

        // Volatility calculation (standard deviation)
        const profitValues = filteredData.map(item => item.difference);
        const avgProfit = profitValues.reduce((sum, val) => sum + val, 0) / profitValues.length;
        const variance = profitValues.reduce((sum, val) => sum + Math.pow(val - avgProfit, 2), 0) / profitValues.length;
        const volatility = Math.sqrt(variance);

        // Trend analysis
        const incomeTrend = calculateTrend(currentIncome, previousIncome);
        const expenseTrend = calculateTrend(currentExpense, previousExpense);

        // Efficiency metrics
        const profitMargin = currentIncome > 0 ? ((currentIncome - currentExpense) / currentIncome) * 100 : 0;
        const expenseRatio = currentIncome > 0 ? (currentExpense / currentIncome) * 100 : 0;

        // Forecast (simple linear projection)
        const forecast = filteredData.map((item, index) => {
            const futureIncome = item.income * (1 + incomeGrowth / 100 / 12);
            const futureExpense = item.expense * (1 + expenseGrowth / 100 / 12);
            return {
                ...item,
                name: `${item.name} (F)`,
                income: futureIncome,
                expense: futureExpense,
                difference: futureIncome - futureExpense,
                isForecast: true
            };
        });

        return {
            incomeGrowth,
            expenseGrowth,
            profitGrowth,
            volatility,
            incomeTrend,
            expenseTrend,
            profitMargin,
            expenseRatio,
            forecast,
            avgProfit,
            currentIncome,
            currentExpense
        };
    }, [filteredData]);

    const chartConfig = getChartConfig(language);

    // Custom tooltip for advanced charts
    const AdvancedTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-white p-4 border rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700">
                    <p className="font-semibold text-gray-900 dark:text-white mb-2">{label}</p>
                    {payload.map((entry: any, index: number) => (
                        <div key={index} className="flex items-center justify-between space-x-4">
                            <span className="text-sm" style={{ color: entry.color }}>
                                {entry.name}:
                            </span>
                            <span className="font-medium" style={{ color: entry.color }}>
                                {formatCurrency(entry.value, language)}
                            </span>
                        </div>
                    ))}
                    {payload.length === 2 && (
                        <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600 dark:text-gray-400">{t("Net")}:</span>
                                <span className={`font-medium ${
                                    (payload[0].value - payload[1].value) >= 0 ? 'text-green-600' : 'text-red-600'
                                }`}>
                                    {formatCurrency(payload[0].value - payload[1].value, language)}
                                </span>
                            </div>
                        </div>
                    )}
                </div>
            );
        }
        return null;
    };

    // Trend indicator component
    const TrendIndicator = ({ value, label }: { value: number; label: string }) => {
        const isPositive = value > 0;
        const isNeutral = Math.abs(value) < 1;

        return (
            <div className="flex items-center space-x-1 min-w-0">
                {isNeutral ? (
                    <Minus className="w-4 h-4 text-gray-500 flex-shrink-0" />
                ) : isPositive ? (
                    <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                ) : (
                    <TrendingDown className="w-4 h-4 text-red-500 flex-shrink-0" />
                )}
                <span className={`text-sm font-medium whitespace-nowrap ${
                    isNeutral ? 'text-gray-500' : isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                    {Math.abs(value).toFixed(1)}%
                </span>
                {label && (
                    <span className="text-xs text-gray-400 whitespace-nowrap">
                        {label}
                    </span>
                )}
            </div>
        );
    };

    if (!validEvents || validEvents.length === 0) {
        return (
            <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                <div className="text-center py-12">
                    <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        {t("No Data for Advanced Analytics")}
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                        {t("Add more financial events to see advanced insights")}
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            {/* Header */}
            <div className="p-6 border-b dark:border-gray-700">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                    <h2 className="text-xl font-semibold dark:text-white mb-4 lg:mb-0">
                        {t("Advanced Financial Analytics")}
                    </h2>
                    
                    {/* Controls */}
                    <div className="flex flex-wrap gap-4">
                        {/* Timeframe Selection */}
                        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                            {[
                                { key: "6m", label: t("6 Months") },
                                { key: "12m", label: t("12 Months") },
                                { key: "24m", label: t("24 Months") }
                            ].map((option) => (
                                <button
                                    key={option.key}
                                    onClick={() => setTimeframe(option.key as any)}
                                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                                        timeframe === option.key
                                            ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                                            : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                                    }`}
                                >
                                    {option.label}
                                </button>
                            ))}
                        </div>

                        {/* Analysis Type Selection */}
                        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                            {[
                                { key: "trend", label: t("Trend") },
                                { key: "forecast", label: t("Forecast") },
                                { key: "comparison", label: t("Compare") }
                            ].map((option) => (
                                <button
                                    key={option.key}
                                    onClick={() => setAnalysisType(option.key as any)}
                                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                                        analysisType === option.key
                                            ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                                            : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                                    }`}
                                >
                                    {option.label}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Key Metrics */}
                {analytics && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-lg overflow-hidden">
                            <div className="flex items-start justify-between min-h-[80px]">
                                <div className="flex-1 min-w-0 pr-3">
                                    <p className="text-sm text-blue-600 dark:text-blue-400 mb-1">{t("Profit Margin")}</p>
                                    <p className="text-lg font-semibold text-blue-700 dark:text-blue-300 whitespace-nowrap">
                                        {analytics.profitMargin.toFixed(1)}%
                                    </p>
                                </div>
                                <div className="flex-shrink-0">
                                    <Target className="w-8 h-8 text-blue-500" />
                                </div>
                            </div>
                        </div>

                        <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-lg overflow-hidden">
                            <div className="flex items-start justify-between min-h-[80px]">
                                <div className="flex-1 min-w-0 pr-3">
                                    <p className="text-sm text-green-600 dark:text-green-400 mb-1">{t("Income Growth")}</p>
                                    <div className="flex items-center">
                                        <TrendIndicator value={analytics.incomeGrowth} label="" />
                                    </div>
                                </div>
                                <div className="flex-shrink-0">
                                    <DollarSign className="w-8 h-8 text-green-500" />
                                </div>
                            </div>
                        </div>

                        <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-lg overflow-hidden">
                            <div className="flex items-start justify-between min-h-[80px]">
                                <div className="flex-1 min-w-0 pr-3">
                                    <p className="text-sm text-purple-600 dark:text-purple-400 mb-1">{t("Volatility")}</p>
                                    <p className="text-lg font-semibold text-purple-700 dark:text-purple-300 truncate">
                                        {formatNumber(analytics.volatility, language)}
                                    </p>
                                </div>
                                <div className="flex-shrink-0">
                                    <BarChart3 className="w-8 h-8 text-purple-500" />
                                </div>
                            </div>
                        </div>

                        <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-4 rounded-lg overflow-hidden">
                            <div className="flex items-start justify-between min-h-[80px]">
                                <div className="flex-1 min-w-0 pr-3">
                                    <p className="text-sm text-orange-600 dark:text-orange-400 mb-1">{t("Expense Ratio")}</p>
                                    <p className="text-lg font-semibold text-orange-700 dark:text-orange-300 whitespace-nowrap">
                                        {analytics.expenseRatio.toFixed(1)}%
                                    </p>
                                </div>
                                <div className="flex-shrink-0">
                                    <Calendar className="w-8 h-8 text-orange-500" />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Charts */}
            <div className="p-6">
                <div className="h-96 mb-8 w-full overflow-hidden">
                    <ResponsiveContainer width="100%" height="100%">
                        {analysisType === "trend" ? (
                            <ComposedChart
                                data={filteredData}
                                margin={{
                                    top: 20,
                                    right: 30,
                                    left: language === 'ar' ? 50 : 40,
                                    bottom: language === 'ar' ? 80 : 60
                                }}
                            >
                                <defs>
                                    <linearGradient id="incomeAreaGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={CHART_COLORS.income} stopOpacity={0.3}/>
                                        <stop offset="95%" stopColor={CHART_COLORS.income} stopOpacity={0.1}/>
                                    </linearGradient>
                                </defs>
                                <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                <XAxis
                                    dataKey="name"
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{
                                        fontSize: 11,
                                        fill: '#6b7280',
                                        textAnchor: language === 'ar' ? 'start' : 'end',
                                        dy: language === 'ar' ? 10 : 0
                                    }}
                                    angle={-45}
                                    textAnchor={language === 'ar' ? 'start' : 'end'}
                                    height={60}
                                    interval={0}
                                />
                                <YAxis
                                    tickFormatter={(value) => formatNumber(value, language)}
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{ fontSize: 11, fill: '#6b7280' }}
                                    width={60}
                                />
                                <Tooltip content={<AdvancedTooltip />} />
                                <Legend wrapperStyle={{ paddingTop: '20px' }} />
                                <Bar dataKey="expense" fill={CHART_COLORS.expense} name={t("Expense")} maxBarSize={60} />
                                <Bar dataKey="income" fill={CHART_COLORS.income} name={t("Income")} maxBarSize={60} />
                                <Line
                                    type="monotone"
                                    dataKey="difference"
                                    stroke={CHART_COLORS.profit}
                                    strokeWidth={3}
                                    name={t("Net Profit")}
                                    dot={{ r: 4 }}
                                />
                            </ComposedChart>
                        ) : analysisType === "forecast" && analytics ? (
                            <AreaChart
                                data={[...filteredData, ...analytics.forecast]}
                                margin={{
                                    top: 20,
                                    right: 30,
                                    left: language === 'ar' ? 50 : 40,
                                    bottom: language === 'ar' ? 80 : 60
                                }}
                            >
                                <defs>
                                    <linearGradient id="forecastGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={CHART_COLORS.profit} stopOpacity={0.4}/>
                                        <stop offset="95%" stopColor={CHART_COLORS.profit} stopOpacity={0.1}/>
                                    </linearGradient>
                                </defs>
                                <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                <XAxis
                                    dataKey="name"
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{
                                        fontSize: 11,
                                        fill: '#6b7280',
                                        textAnchor: language === 'ar' ? 'start' : 'end',
                                        dy: language === 'ar' ? 10 : 0
                                    }}
                                    angle={-45}
                                    textAnchor={language === 'ar' ? 'start' : 'end'}
                                    height={60}
                                    interval={0}
                                />
                                <YAxis
                                    tickFormatter={(value) => formatNumber(value, language)}
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{ fontSize: 11, fill: '#6b7280' }}
                                    width={60}
                                />
                                <Tooltip content={<AdvancedTooltip />} />
                                <Legend wrapperStyle={{ paddingTop: '20px' }} />
                                <Area
                                    type="monotone"
                                    dataKey="difference"
                                    stroke={CHART_COLORS.profit}
                                    fill="url(#forecastGradient)"
                                    strokeWidth={2}
                                    name={t("Profit Trend & Forecast")}
                                />
                            </AreaChart>
                        ) : (
                            <ComposedChart
                                data={filteredData}
                                margin={{
                                    top: 20,
                                    right: 30,
                                    left: language === 'ar' ? 50 : 40,
                                    bottom: language === 'ar' ? 80 : 60
                                }}
                            >
                                <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                <XAxis
                                    dataKey="name"
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{
                                        fontSize: 11,
                                        fill: '#6b7280',
                                        textAnchor: language === 'ar' ? 'start' : 'end',
                                        dy: language === 'ar' ? 10 : 0
                                    }}
                                    angle={-45}
                                    textAnchor={language === 'ar' ? 'start' : 'end'}
                                    height={60}
                                    interval={0}
                                />
                                <YAxis
                                    tickFormatter={(value) => formatNumber(value, language)}
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{ fontSize: 11, fill: '#6b7280' }}
                                    width={60}
                                />
                                <Tooltip content={<AdvancedTooltip />} />
                                <Legend wrapperStyle={{ paddingTop: '20px' }} />
                                <Bar dataKey="income" fill={CHART_COLORS.income} name={t("Income")} maxBarSize={60} />
                                <Bar dataKey="expense" fill={CHART_COLORS.expense} name={t("Expense")} maxBarSize={60} />
                            </ComposedChart>
                        )}
                    </ResponsiveContainer>
                </div>

                {/* Insights */}
                {analytics && (
                    <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold dark:text-white mb-4">{t("Key Insights")}</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <p className="text-gray-600 dark:text-gray-400">
                                    {t("Your income has")} <span className={`font-medium ${
                                        analytics.incomeGrowth > 0 ? 'text-green-600' : 'text-red-600'
                                    }`}>
                                        {analytics.incomeGrowth > 0 ? t("increased") : t("decreased")} by {Math.abs(analytics.incomeGrowth).toFixed(1)}%
                                    </span> {t("compared to the previous period")}.
                                </p>
                            </div>
                            <div>
                                <p className="text-gray-600 dark:text-gray-400">
                                    {t("Your profit margin is")} <span className={`font-medium ${
                                        analytics.profitMargin > 20 ? 'text-green-600' : analytics.profitMargin > 10 ? 'text-yellow-600' : 'text-red-600'
                                    }`}>
                                        {analytics.profitMargin.toFixed(1)}%
                                    </span>, which is {analytics.profitMargin > 20 ? t("excellent") : analytics.profitMargin > 10 ? t("good") : t("needs improvement")}.
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AdvancedAnalyticsSection;

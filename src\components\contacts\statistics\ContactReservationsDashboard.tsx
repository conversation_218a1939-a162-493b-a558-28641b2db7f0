import React from 'react';
import { Button } from '@/components/ui/button';
import useLanguage from "@/hooks/useLanguage";
import { Badge } from "@/components/ui/badge";
import {
  Bar<PERSON>hart as ReChartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ReChartsTool<PERSON>,
  <PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Line<PERSON>hart as <PERSON><PERSON><PERSON>s<PERSON>ine<PERSON><PERSON>,
  Line,
  Pie<PERSON>hart as ReChartsPie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';

interface ContactReservationsDashboardProps {
  reservationTrendData: any[];
  reservationStatusData: any[];
  revenuePerLocationData: any[];
  totalReservations: number;
  activeReservations: number;
  avgReservationValue: number;
  COLORS: string[];
}

const ContactReservationsDashboard: React.FC<ContactReservationsDashboardProps> = ({
  reservationTrendData,
  reservationStatusData,
  revenuePerLocationData,
  totalReservations,
  activeReservations,
  avgReservationValue,
  COLORS
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium">{t("reservationTrends")}</h3>
          <Button variant="outline" size="sm" className="h-8">
            {t("exportData")}
          </Button>
        </div>
        
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <ReChartsLineChart
              data={reservationTrendData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="period" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} />
              <ReChartsTooltip />
              <ReChartsLegend />
              <Line
                type="monotone"
                dataKey="active"
                name={t("activeReservations")}
                stroke="#3b82f6"
                strokeWidth={2}
                activeDot={{ r: 8 }}
              />
              <Line
                type="monotone"
                dataKey="completed"
                name={t("completedReservations")}
                stroke="#10b981"
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="total"
                name={t("totalReservations")}
                stroke="#6366f1"
                strokeWidth={2}
                strokeDasharray="5 5"
              />
            </ReChartsLineChart>
          </ResponsiveContainer>
        </div>
        
        <div className="grid grid-cols-3 gap-4 mt-6">
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("totalReservations")}</p>
            <p className="text-xl font-bold mt-1">{totalReservations}</p>
            <div className="flex items-center mt-2 text-sm">
              <span className="text-gray-500 dark:text-gray-400">{t("lifetime")}</span>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("activeReservations")}</p>
            <p className="text-xl font-bold mt-1">{activeReservations}</p>
            <div className="flex items-center mt-2 text-sm">
              <span className="text-blue-500 font-medium">{t("currentlyActive")}</span>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("averageReservationValue")}</p>
            <p className="text-xl font-bold mt-1">${avgReservationValue.toLocaleString()}</p>
            <div className="flex items-center mt-2 text-sm">
              <span className="text-green-500 font-medium">+12% </span>
              <span className="text-gray-500 dark:text-gray-400 ml-1">{t("vsAverage")}</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Reservation Status Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-medium mb-4">{t("reservationStatus")}</h3>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <ReChartsPieChart>
                <Pie
                  data={reservationStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {reservationStatusData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={index === 0 ? '#3b82f6' : '#10b981'} 
                    />
                  ))}
                </Pie>
                <ReChartsTooltip 
                  formatter={(value: any) => [value, ""]}
                />
              </ReChartsPieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="flex justify-center gap-6 mt-2">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2" />
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {t("active")}: {reservationStatusData[0]?.percentage}%
              </span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2" />
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {t("completed")}: {reservationStatusData[1]?.percentage}%
              </span>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-medium mb-4">{t("reservationValueDistribution")}</h3>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <ReChartsBarChart
                data={revenuePerLocationData.slice(0, 5)}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                <XAxis 
                  type="number" 
                  axisLine={false} 
                  tickLine={false}
                  tickFormatter={(value) => `$${value.toLocaleString()}`}
                />
                <YAxis 
                  type="category" 
                  dataKey="name" 
                  axisLine={false} 
                  tickLine={false}
                  width={150}
                />
                <ReChartsTooltip 
                  formatter={(value: any) => [`$${value.toLocaleString()}`, t("avgRevenuePerReservation")]}
                />
                <Bar 
                  dataKey="revenue" 
                  name={t("avgRevenue")}
                  fill="#6366f1" 
                  radius={[0, 4, 4, 0]}
                />
              </ReChartsBarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
      
      {/* Recent Reservations Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium">{t("recentReservations")}</h3>
          <Button variant="outline" size="sm">
            {t("viewAll")}
          </Button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b dark:border-gray-700">
                <th className="text-left py-3 px-4">{t("location")}</th>
                <th className="text-left py-3 px-4">{t("period")}</th>
                <th className="text-right py-3 px-4">{t("amount")}</th>
                <th className="text-center py-3 px-4">{t("status")}</th>
                <th className="text-right py-3 px-4">{t("adType")}</th>
              </tr>
            </thead>
            <tbody>
              {/* Mocked recent reservation data */}
              <tr className="border-b dark:border-gray-700">
                <td className="py-3 px-4 font-medium">Downtown Billboard</td>
                <td className="py-3 px-4">Jun 24, 2023 - Jul 24, 2023</td>
                <td className="text-right py-3 px-4">$4,500</td>
                <td className="text-center py-3 px-4">
                  <Badge className="bg-blue-100 text-white dark:bg-blue-900/20 dark:text-blue-300">{t("active")}</Badge>
                </td>
                <td className="text-right py-3 px-4">Billboard</td>
              </tr>
              <tr className="border-b dark:border-gray-700">
                <td className="py-3 px-4 font-medium">Shopping District</td>
                <td className="py-3 px-4">May 15, 2023 - Jun 15, 2023</td>
                <td className="text-right py-3 px-4">$3,200</td>
                <td className="text-center py-3 px-4">
                  <Badge className="bg-green-100 text-white dark:bg-green-900/20 dark:text-green-300">{t("completed")}</Badge>
                </td>
                <td className="text-right py-3 px-4">Digital Display</td>
              </tr>
              <tr>
                <td className="py-3 px-4 font-medium">Business Park</td>
                <td className="py-3 px-4">Jul 01, 2023 - Aug 01, 2023</td>
                <td className="text-right py-3 px-4">$2,800</td>
                <td className="text-center py-3 px-4">
                  <Badge className="bg-blue-100 text-white dark:bg-blue-900/20 dark:text-blue-300">{t("active")}</Badge>
                </td>
                <td className="text-right py-3 px-4">Street Banner</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ContactReservationsDashboard;

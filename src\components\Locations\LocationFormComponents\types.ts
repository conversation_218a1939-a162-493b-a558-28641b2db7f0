import { Location } from "@/lib/types/location";

export interface LocationFormProps {
  isOpen: boolean;
  onClose: () => void;
  location?: Location;
  onSubmit: (data: Partial<Location>) => void;
  view?: "modal" | "sidebar";
}

export interface OwnershipShare {
  name: string;
  percentage: number;
}

export interface LocationType {
  id: string;
  name: string;
}

export interface GeneralTabProps {
  formData: Partial<Location>;
  handleChange: (field: keyof Location, value: any) => void;
  locationTypesWithIds: LocationType[];
  selectedTypeId: string;
  setSelectedTypeId: (id: string) => void;
  typesLoading: boolean;
  t: (key: string) => string;
}

export interface OwnershipTabProps {
  formData: Partial<Location>;
  handleChange: (field: keyof Location, value: any) => void;
  ownershipShares: OwnershipShare[];
  setOwnershipShares: React.Dispatch<React.SetStateAction<OwnershipShare[]>>;
  isEgyCommPrimaryOwner: boolean;
  setIsEgyCommPrimaryOwner: React.Dispatch<React.SetStateAction<boolean>>;
  isOwnedByEgyComm: boolean;
  setIsOwnedByEgyComm: React.Dispatch<React.SetStateAction<boolean>>;
  handleOwnedBySelect: (owner: string) => void;
  contactOptions: string[];
  sharedWith: string[];
  t: (key: string) => string;
  totalPercentage: number;
  combinedTotalPercentage?: number; // Add this prop
  percentageError: string;
  validatePercentages: () => boolean;
}

import React from 'react';
import { DataImporter } from './DataImporter';
import type { ColumnDefinition } from './DataImporter';

// Example of how to use the new DataImporter
export function DataImporterExample() {
  // Define your columns with their types and properties
  const columns: ColumnDefinition[] = [
    {
      key: 'name',
      label: 'Full Name',
      type: 'text',
      required: true,
    },
    {
      key: 'email',
      label: 'Email Address',
      type: 'text',
      required: true,
    },
    {
      key: 'birthDate',
      label: 'Birth Date',
      type: 'date',
      required: false,
    },
    {
      key: 'department',
      label: 'Department',
      type: 'dropdown',
      required: true,
      options: ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance'],
    },
    {
      key: 'address',
      label: 'Address',
      type: 'modal',
      required: false,
      component: AddressModal, // Your custom modal component
    },
  ];

  const handleImport = (data: any[]) => {
    console.log('Imported data:', data);
    // Process your imported data here
  };

  const handleValidationChange = (isValid: boolean, errors: any[]) => {
    console.log('Validation status:', isValid, errors);
    // Handle validation feedback
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Employee Data Import</h1>
      
      <DataImporter
        columns={columns}
        onImport={handleImport}
        maxPreviewRows={100}
        allowedFileTypes={['.csv', '.xlsx']}
        onValidationChange={handleValidationChange}
      />
    </div>
  );
}

// Example custom modal component for address input
function AddressModal({ onSubmit, onClose, data }: any) {
  const [address, setAddress] = React.useState(data || {
    street: '',
    city: '',
    state: '',
    zipCode: '',
  });

  const handleSubmit = () => {
    onSubmit(address);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Address Information</h3>
      
      <div className="grid grid-cols-1 gap-4">
        <input
          placeholder="Street Address"
          value={address.street}
          onChange={(e) => setAddress({ ...address, street: e.target.value })}
          className="p-2 border rounded"
        />
        
        <div className="grid grid-cols-2 gap-2">
          <input
            placeholder="City"
            value={address.city}
            onChange={(e) => setAddress({ ...address, city: e.target.value })}
            className="p-2 border rounded"
          />
          <input
            placeholder="State"
            value={address.state}
            onChange={(e) => setAddress({ ...address, state: e.target.value })}
            className="p-2 border rounded"
          />
        </div>
        
        <input
          placeholder="ZIP Code"
          value={address.zipCode}
          onChange={(e) => setAddress({ ...address, zipCode: e.target.value })}
          className="p-2 border rounded"
        />
      </div>
      
      <div className="flex justify-end gap-2">
        <button
          onClick={onClose}
          className="px-4 py-2 border rounded hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Save Address
        </button>
      </div>
    </div>
  );
}

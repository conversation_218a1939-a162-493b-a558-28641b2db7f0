import React from "react";
import { Contract } from "@/lib/interfaces/contract";
import useLanguage from "@/hooks/useLanguage";
import { formatCurrency, getContractDuration } from "./utils";

interface FinancialSummaryProps {
  contract: Contract;
  totalPaid: number;
  totalRemaining: number;
  isFinancialDataReady: boolean;
}

const FinancialSummary: React.FC<FinancialSummaryProps> = ({ 
  contract,
  totalPaid,
  totalRemaining,
  isFinancialDataReady
}) => {
  const { t } = useLanguage();

  return (
    // Empty component as we moved the content to the header
    <></>
  );
};

export default FinancialSummary;

"use client";
import React from "react";
import SettingsDetails from './SettingsDetails';
import UsersList from './UsersList';
import useLanguage from "@/hooks/useLanguage";
import { usePermissions } from "@/hooks/usePermissions";
import { LoadingComp } from "../common/Loading";

const Settings: React.FC = () => {
  const { t } = useLanguage();
  const { hasPermission , permissionsLoaded } = usePermissions();

  if (!permissionsLoaded) {
      return (
          <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
          <LoadingComp />
          </div>
      );
  
  }
  
  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4 sm:mb-6">{t("settings")}</h1>
        
        {/* Personal Information Section */}
        <div className="mb-6 sm:mb-8">
          <SettingsDetails />
        </div>
        
        {/* Users List Section */}
        {hasPermission("users" , "view") && (
          <div className="mb-6 sm:mb-8">
              <UsersList />
          </div>
        )}

      </div>
    </div>
  );
};

export default Settings;
// src/services/contactServices.ts
import { AxiosInstance } from "axios";
import { EventDetails } from "./interfaces/finaces";
import { Location } from "@/lib/types/location";
import { toast } from "react-hot-toast";

export interface CreateLocationPayload {
  name: string;
  address: string;
  description: string;
  is_active: boolean;
  is_reserved: boolean;
  capacity: number;
  our_percentage: number;
  ownership_shares: {
    contact_id: string;
    percentage: number;
    is_primary_owner?: boolean;
  }[];
  type_ids: string[]; // Required - not optional anymore
}

export interface UpdateLocationPayload {
  name?: string;
  address?: string;
  description?: string;
  is_active?: boolean;
  is_reserved?: boolean;
  capacity?: number;
  our_percentage?: number;
  ownership_shares?: {
    contact_id: string;
    percentage: number;
    is_primary_owner?: boolean;
  }[];
  type_ids?: string[]; // Keep as optional for partial updates
}

export interface LocationType {
  id: string;
  name: string;
  created_at: string;
}

export interface LocationTypesResponse {
  location_types: LocationType[];
  count: number;
}

export interface CreateLocationTypePayload {
  name: string;
}

export interface LocationTypeWithId {
  id: string;
  name: string;
}

export interface LocationFinancesResponse {
  incomes: EventDetails[];
  expenses: EventDetails[];
}

const createLocationServices = (apiClient: AxiosInstance) => ({
  getLocationDetails: async (locationId: number): Promise<Location> => {
    try {
      const response = await apiClient.get(`/locations/api/get/${locationId}/`);
      return response.data.location;
    } catch (error) {
      throw new Error("Error fetching location details");
    }
  },
  getLocationFinances: async (
    locationId: number,
    signal?: AbortSignal,
  ): Promise<LocationFinancesResponse> => {
    try {
      console.log(
        `Making API call to fetch location finances for ID: ${locationId}...`,
      );
      const response = await apiClient.post(
        "/locations/api/location-finances/",
        {
          location_id: locationId,
        },
        { signal: signal },
      );
      console.log("Raw location finances API response:", response);

      if (!response.data) {
        console.error("Location finances API response has no data property");
        return { incomes: [], expenses: [] };
      }

      if (
        response.data.finances?.income?.events &&
        response.data.finances?.expenses?.events
      ) {
        return {
          incomes: response.data.finances.income.events as EventDetails[],
          expenses: response.data.finances.expenses.events as EventDetails[],
        };
      }

      return { incomes: [], expenses: [] };
    } catch (error) {
      if (
        error instanceof Error &&
        (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
      ) {
        console.log("Location finances API request was cancelled");
        return { incomes: [], expenses: [] };
      }
      console.error("Error fetching location finances:", error);
      throw new Error("Error fetching location finances");
    }
  },

  createLocation: async (payload: CreateLocationPayload): Promise<Location> => {
    try {
      console.log(
        "Sending API request with payload:",
        JSON.stringify(payload, null, 2),
      );

      // Add request debugging
      const response = await apiClient.post("/locations/api/create/", payload);
      console.log("Response received:", response.data);
      return response.data.location;
    } catch (error: any) {
      console.error("Error creating location:", error);

      // Enhanced error logging
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error("Server responded with error:", {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers,
        });
      } else if (error.request) {
        // The request was made but no response was received
        console.error("No response received from server:", error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error("Error setting up request:", error.message);
      }

      throw error; // Re-throw to allow components to handle the error
    }
  },

  updateLocation: async (
    locationId: string,
    payload: UpdateLocationPayload,
  ): Promise<Location> => {
    try {
      const response = await apiClient.post(
        `/locations/api/updatelocation/${locationId}/`,
        payload,
      );
      return response.data.location;
    } catch (error) {
      throw new Error("Error updating location");
    }
  },

  deleteLocation: async (locationId: string): Promise<void> => {
    try {
      console.log("Sending soft delete request for location:", locationId);
      // Show loading indicator or buffer before the request
      const loadingToast = toast.loading("Deleting location...");

      try {
        const response = await apiClient.post("/locations/api/soft-delete/", {
          location_id: locationId,
        });

        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Check response status
        if (response.status === 200) {
          toast.success("Location has been deleted successfully");
        }
      } catch (error: any) {
        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Handle 409 conflict error specifically
        if (error.response && error.response.status === 409) {
          console.log("409 error data:", error.response.data);

          if (error.response.data.warning) {
            toast.error(error.response.data.warning);
          } else if (error.response.data.message) {
            toast.error(error.response.data.message);
          } else {
            toast.error("Conflict occurred while deleting location");
          }
        } else if (error.response && error.response.data) {
          // Handle other server errors with specific messages
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting location";
          toast.error(errorMessage);
        } else {
          // Handle other errors
          toast.error("Failed to delete location. Please try again.");
          console.error("Error soft deleting location:", error);
        }
        throw error; // Re-throw for the caller to handle if needed
      }
    } catch (error) {
      console.error("Error soft deleting location:", error);
      throw error;
    }
  },

  getAllLocationTypes: async (): Promise<LocationTypeWithId[]> => {
    try {
      const response = await apiClient.get("/locations/api/types/");
      console.log("Location types API response:", response.data);

      // Check if we have the expected structure
      if (!response.data) {
        console.error("Empty response from location types API");
        return [];
      }

      // Check if the response has the expected location_types property
      if (!response.data.location_types) {
        console.error("Missing location_types in API response:", response.data);

        // If data is directly an array, use it
        if (Array.isArray(response.data)) {
          return response.data.map((type: any) => {
            if (typeof type === "object" && type !== null) {
              return {
                id: type.id || "",
                name: type.name || type.toString(),
              };
            }
            return { id: type, name: type.toString() };
          });
        }

        // Try to find the data in a different property
        const possibleArrays = Object.values(response.data).filter(
          Array.isArray,
        );
        if (possibleArrays.length > 0) {
          const types = possibleArrays[0] as any[];
          return types.map((type: any) => {
            if (typeof type === "object" && type !== null) {
              return {
                id: type.id || "",
                name: type.name || type.toString(),
              };
            }
            return { id: type, name: type.toString() };
          });
        }

        return [];
      }

      // Normal flow - return full objects with id and name
      return response.data.location_types.map((type: LocationType) => ({
        id: type.id,
        name: type.name,
      }));
    } catch (error) {
      console.error("Error fetching location types:", error);
      return []; // Return empty array instead of throwing to prevent component crashes
    }
  },

  updateLocationReservationNotOurs: async (
    locationId: string,
    res_start_date: string,
    res_end_date: string,
  ): Promise<Location> => {
    try {
      const response = await apiClient.post(
        `/locations/api/update-reservation/${locationId}/`,
        {
          res_start_date,
          res_end_date,
        },
      );
      return response.data.location;
    } catch (error) {
      console.error("Error updating reservation for not ours:", error);
      throw new Error("Error updating reservation for not ours");
    }
  },

  createLocationType: async (
    payload: CreateLocationTypePayload,
  ): Promise<LocationType> => {
    try {
      const response = await apiClient.post(
        "/locations/api/types/create/",
        payload,
      );
      return response.data;
    } catch (error) {
      console.error("Error creating contact type:", error);
      throw error;
    }
  },
});

export default createLocationServices;

import { getRequestConfig } from 'next-intl/server';

// Define the supported locales
type SupportedLocale = 'ar' | 'en';

// Type guard to check if a locale is supported
const isSupportedLocale = (locale: string | undefined): locale is SupportedLocale => {
  return locale === 'ar' || locale === 'en';
};

export default getRequestConfig(async ({ requestLocale }) => {
  // Default locale if not specified
  let locale: SupportedLocale = 'en';

  // If a valid locale is provided, use it
  const resolvedLocale = await requestLocale;
  if (resolvedLocale && isSupportedLocale(resolvedLocale)) {
    locale = resolvedLocale;
  }

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default
  };
});
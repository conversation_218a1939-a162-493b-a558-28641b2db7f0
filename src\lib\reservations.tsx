import { Reservation } from "./interfaces/reservation";
import apiClient from "./api/apiClient";
import { start } from "repl";
import { EventDetails } from "./interfaces/finaces";
import { CreateIncomePayload } from "./income";
import { toast } from "react-hot-toast";

export interface ReservationsApiResponse {
  reservations: Reservation[];
  count: number;
}
export interface ReservationEventsApiResponse {
  incomes: EventDetails[];
  expenses: EventDetails[];
}

  
export interface ReservationsByLocationApiResponse {
  location: {
    id: string;
    name: string;
    address: string;
    totalCapacity: number;
    takenCapacity: number;
    availableCapacity: number;
  };
  reservations: Reservation[];
  count: number;
}

export interface CreateReservationPayload {
  contact_id: string;
  location_id: string;
  type_id?: string;
  reservation: {
    title: string;
    description: string;
    total_amount: number;
    start_date: string;
    end_date: string;
    required_capacity: number;
    status: string;
    notes: string;
  };
  income_events: {
    amount: number;
    due_date: string;
    notification_view_date?: string | null;
    status: string;
    priority: string;
    received_date?: string;
  }[];
}

export interface UpdateReservationPayload {
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  status: string;
  total_amount: number;
  required_capacity: number;
  contact_id: string;
  location_id: string;
  notes?: string;
  income_events: Array<{
    id?: string;
    title: string;
    description?: string;
    amount: string; // String format as required by API
    due_date: string;
    status: string;
    priority: string;
    received_date?: string;
    contact_id: string;
    location_id: string;
    type_id: string;
    is_deleted?: boolean;
  }>;
  expense_events: Array<{
    id?: string;
    title: string;
    description?: string;
    amount: string; // String format as required by API
    due_date: string;
    status: string;
    priority: string;
    paid_date?: string;
    contact_id: string;
    location_id: string;
    type_id: string;
    is_deleted?: boolean;
  }>;
}

const ReservationServices = () => ({
  getReservationEventsById: async (
    id: string | number,
    signal?: AbortSignal,
  ): Promise<ReservationEventsApiResponse> => {
    try {
      console.log(
        `Making API call to fetch reservation events for ID: ${id}...`,
      );
      const response = await apiClient.post(
        "/reservations/api/reservation-finances/",
        { 
          signal: signal ,
          reservation_id: id,
        
        },
      );
      console.log("Raw reservation events API response:", response);

      if (!response.data) {
        console.error("Reservation events API response has no data property");
        return { incomes: [], expenses: [] };
      }

      if (response.data.finances.income.events && response.data.finances.expenses.events) {
        return {
          incomes: response.data.finances.income.events as EventDetails[],
          expenses: response.data.finances.expenses.events as EventDetails[],
        };
      }

      return { incomes: [], expenses: [] };
    } catch (error) {
      if (
        error instanceof Error &&
        (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
      ) {
        console.log("Reservation events API request was cancelled");
        return { incomes: [], expenses: [] };
      }
      console.error("Error fetching reservation events:", error);
      throw new Error("Error fetching reservation events");
    }
  },

  getReservations: async (
    signal?: AbortSignal,
  ): Promise<ReservationsApiResponse> => {
    try {
      console.log("Making API call to fetch reservations...");
      const response = await apiClient.get("/reservations/api/getall/", {
        signal: signal,
      });
      console.log("Raw reservations API response:", response);

      if (!response.data) {
        console.error("Reservations API response has no data property");
        return { reservations: [], count: 0 };
      }

      // Return the properly formatted response
      return response.data as ReservationsApiResponse;
    } catch (error) {
      if (
        error instanceof Error &&
        (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
      ) {
        console.log("Reservations API request was cancelled");
        return { reservations: [], count: 0 };
      }
      console.error("Error fetching reservations:", error);
      throw new Error("Error fetching reservations");
    }
  },

  // Get reservations by location ID
  getReservationsByLocation: async (
    locationId: string | number,
    startDate: string,
    endDate: string,
    signal?: AbortSignal,
  ): Promise<ReservationsByLocationApiResponse> => {
    try {
      console.log(
        `Making API call to fetch reservations for location ID: ${locationId}...`,
      );
      const response = await apiClient.post(
        "/reservations/api/by-location-and-date/",
        {
          location_id: locationId,
          start_date: startDate,
          end_date: endDate,
        },
        { signal: signal },
      );
      console.log("Raw reservations by location API response:", response);

      if (!response.data) {
        console.error(
          "Reservations by location API response has no data property",
        );
        return {
          location: {
            id: "",
            name: "",
            address: "",
            totalCapacity: 0,
            takenCapacity: 0,
            availableCapacity: 0,
          },
          reservations: [],
          count: 0,
        };
      }

      // If response is in the expected format, return it
      if (response.data.reservations) {
        return response.data as ReservationsByLocationApiResponse;
      }

      console.error(
        "Could not find reservations data in the response:",
        response.data,
      );
      return {
        location: {
          id: "",
          name: "",
          address: "",
          totalCapacity: 0,
          takenCapacity: 0,
          availableCapacity: 0,
        },
        reservations: [],
        count: 0,
      };
    } catch (error) {
      if (
        error instanceof Error &&
        (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
      ) {
        console.log(
          `Reservations by location API request was cancelled for location: ${locationId}`,
        );
        return {
          location: {
            id: "",
            name: "",
            address: "",
            totalCapacity: 0,
            takenCapacity: 0,
            availableCapacity: 0,
          },
          reservations: [],
          count: 0,
        };
      }
      console.error("Error fetching reservations by location:", error);
      throw new Error("Error fetching reservations by location");
    }
  },

  // Get reservations by contact ID
  getReservationsByContact: async (
    contactId: string | number,
  ): Promise<ReservationsApiResponse> => {
    try {
      console.log(
        `Making API call to fetch reservations for contact ID: ${contactId}...`,
      );
      const response = await apiClient.post("/reservations/api/by-contact/", {
        contact_id: contactId,
      });
      console.log("Raw reservations by contact API response:", response);

      if (!response.data) {
        console.error(
          "Reservations by contact API response has no data property",
        );
        return { reservations: [], count: 0 };
      }

      // If response is in the expected format, return it
      if (response.data.reservations) {
        return response.data as ReservationsApiResponse;
      }

      // If data is just an array of reservations, wrap it in the proper format
      if (Array.isArray(response.data)) {
        return {
          reservations: response.data,
          count: response.data.length,
        };
      }

      console.error(
        "Could not find reservations data in the response:",
        response.data,
      );
      return { reservations: [], count: 0 };
    } catch (error) {
      console.error("Error fetching reservations by contact:", error);
      throw new Error("Error fetching reservations by contact");
    }
  },

  

  // Create a new reservation
  createReservation: async (
    payload: CreateReservationPayload,
  ): Promise<Reservation> => {
    try {
      console.log("Creating reservation with payload:", payload);
      const response = await apiClient.post(
        "/reservations/api/create-with-income/",
        payload,
      );
      console.log("Create reservation response:", response);
      return response.data as Reservation;
    } catch (error) {
      console.error("Error creating reservation:", error);
      throw new Error("Error creating reservation");
    }
  },

  // Update an existing reservation
  updateReservation: async (
    id: number,
    payload: Partial<CreateReservationPayload>,
  ): Promise<Reservation> => {
    try {
      console.log(`Updating reservation ID: ${id} with payload:`, payload);
      const response = await apiClient.put(
        `/reservations/api/update/${id}/`,
        payload,
      );
      console.log("Update reservation response:", response);
      return response.data as Reservation;
    } catch (error) {
      console.error(`Error updating reservation ID: ${id}:`, error);
      throw new Error("Error updating reservation");
    }
  },

  // Update an existing reservation with income and expense events
  updateReservationWithEvents: async (
    id: string,
    payload: UpdateReservationPayload,
  ): Promise<Reservation> => {
    try {
      console.log(`Updating reservation ID: ${id} with payload:`, payload);
      const response = await apiClient.post(
        `/reservations/api/updatereservation/${id}/`,
        payload,
      );
      console.log("Update reservation response:", response);
      return response.data as Reservation;
    } catch (error) {
      console.error(`Error updating reservation ID: ${id}:`, error);
      throw new Error("Error updating reservation");
    }
  },

  // Delete a reservation
  deleteReservation: async (id: number): Promise<void> => {
    try {
      console.log(`Deleting reservation ID: ${id}`);
      await apiClient.delete(`/reservations/api/delete/${id}/`);
      console.log(`Reservation ID: ${id} deleted successfully`);
    } catch (error) {
      console.error(`Error deleting reservation ID: ${id}:`, error);
      throw new Error("Error deleting reservation");
    }
  },

  // Soft delete a reservation
  softDeleteReservation: async (reservationId: string): Promise<void> => {
    try {
      console.log("Sending soft delete request for reservation:", reservationId);
      // Show loading indicator or buffer before the request
      const loadingToast = toast.loading("Deleting reservation...");

      try {
        const response = await apiClient.post("/reservations/api/soft-delete/", {
          reservation_id: reservationId,
        });

        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Check response status
        if (response.status === 200) {
          toast.success("Reservation has been deleted successfully");
        }
      } catch (error: any) {
        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Handle 409 conflict error specifically
        if (error.response && error.response.status === 409) {
          console.log("409 error data:", error.response.data);

          if (error.response.data.warning) {
            toast.error(error.response.data.warning);
          } else if (error.response.data.message) {
            toast.error(error.response.data.message);
          } else {
            toast.error("Conflict occurred while deleting reservation");
          }
        } else if (error.response && error.response.data) {
          // Handle other server errors with specific messages
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting reservation";
          toast.error(errorMessage);
        } else {
          // Handle other errors
          toast.error("Failed to delete reservation. Please try again.");
          console.error("Error soft deleting reservation:", error);
        }
        throw error; // Re-throw for the caller to handle if needed
      }
    } catch (error) {
      console.error("Error soft deleting reservation:", error);
      throw error;
    }
  },
});

export default ReservationServices;

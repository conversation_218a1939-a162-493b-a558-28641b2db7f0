{"welcome": "Welcome", "switchLanguage": "Switch Language", "logout": "Logout", "edit": "Edit", "save": "Save", "cancel": "Cancel", "amount": "Amount", "due date": "Due Date", "type": "Type", "title": "Title", "pending": "Pending", "completed": "Completed", "overdue": "Overdue", "upcoming": "Upcoming", "cancelled": "Cancelled", "high": "High", "medium": "Medium", "low": "Low", "none": "None", "Event Type": "Event Type", "Enter amount (integers only)": "Enter amount (integers only)", "Please select an end date": "Please select an end date", "Cannot create reservation: Required capacity exceeds available capacity": "Cannot create reservation: Required capacity exceeds available capacity", "loading": "Loading...", "delete": "Delete", "pdfGenerator": "PDF Generator", "pdfSettings": "PDF Settings", "language": "Language", "arabic": "Arabic", "english": "English", "documentTitle": "Document Title", "includeLogo": "Include Company Logo", "includeCompanyInfo": "Include Company Information", "includeDate": "Include Creation Date", "selectAndEditFields": "Select and Edit Fields", "documentInfo": "Document Information", "event": "Event", "contact": "Contact", "name": "Name", "email": "Email", "phone": "Phone", "company": "Company", "dueDate": "Due Date", "status": "Status", "description": "Description", "noValue": "No Value", "fieldsSelected": "fields selected", "total": "total", "generating": "Generating...", "generatePDF": "Generate PDF", "editIncome": "Edit Income", "Error generating PDF. Please try again.": "Error generating PDF. Please try again.", "generate pdf": "Generate PDF", "eventsTable": "Events Table", "contactsTable": "Contacts Table", "locationsTable": "Locations Table", "reservationsTable": "Reservations Table", "contractsTable": "Contracts Table", "includeEventsTableDesc": "Professional table with income and expense events", "includeContactsTableDesc": "Complete contacts information table", "includeLocationsTableDesc": "Locations details and capacity information", "includeReservationsTableDesc": "Reservations with dates and amounts", "includeContractsTableDesc": "Contracts with terms and amounts", "pdfPreview": "PDF Preview", "events": "events", "contacts": "contacts", "locations": "locations", "reservations": "reservations", "contracts": "contracts", "noData": "No data available", "includedSections": "Included Sections", "companyLogo": "Company Logo", "companyInformation": "Company Information", "creationDate": "Creation Date", "pdfWillInclude": "Your PDF will include:", "professionalTableFormat": "Professional table format", "properArabicSupport": "Proper Arabic text support", "colorCodedHeaders": "Color-coded table headers", "totalCalculations": "Automatic total calculations", "readyToGenerate": "Ready to generate PDF", "location": "Location", "address": "Address", "capacity": "Capacity", "active": "Active", "inactive": "Inactive", "priority": "Priority", "reservationReport": "Reservation Report", "contractReport": "Contract Report", "exportOptions": "Export Options", "selectSectionsToExport": "Select sections to include in the PDF export:", "locationDetails": "Location Details", "items": "items", "loading...": "loading...", "Loading data... Please wait": "Loading data... Please wait", "Loading...": "Loading..."}
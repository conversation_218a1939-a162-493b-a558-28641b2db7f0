import React, { useState, useMemo, useEffect, useRef } from "react";
import {
  Phone,
  Mail,
  User,
  Calendar,
  Trash2,
  Edit,
  Building,
  DollarSign,
  Users,
  Calendar as CalendarIcon,
  Percent,
  History,
  PieChart,
  BarChart3,
  ChevronDown,
  ChevronUp,
  X,
  ChevronLeft,
  ChevronRight,
  Clock,
  CheckCircle,
  Download,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/cards/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Location } from "@/lib/types/location";
import useLanguage from "@/hooks/useLanguage";
import DeleteConfirmationModal from "../Modals/DeleteConfirmationModal";
import { useRouter } from "next/navigation";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { useLocationServices } from "@/hooks/useLocations";
import { toast } from "react-hot-toast";
import { FinancialHistory } from "./FinancialHistory";
import { generatePDF } from "../Dashboard/UpcommingEventsPage/generatePDF";
import { EventDetails } from "@/lib/interfaces/finaces";
import { Reservation } from "@/lib/interfaces/reservation";
import { Contract } from "@/lib/interfaces/contract";
import PDFGeneratorModal from "../Dashboard/UpcommingEventsPage/PDFGeneratorModal";
import { usePDFGenerator } from "@/hooks/usePDFGenerator";

interface LocationDetailsProps {
  location?: Location;
  onEdit: () => void;
  onDelete: () => void;
  onClose?: () => void;
  canEdit?: boolean;
  canDelete?: boolean;
  canExport?: boolean;
}

const LocationDetails: React.FC<LocationDetailsProps> = ({
  location,
  onEdit,
  onDelete,
  onClose,
  canEdit = true,
  canDelete = true,
  canExport = true,
}) => {
  const { t, language } = useLanguage();
  const router = useRouter();
  const locationServices = useLocationServices();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [isDailyViewOpen, setIsDailyViewOpen] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const { modalProps, openPDFModal, closePDFModal } = usePDFGenerator();

  // SIMPLIFIED: Use refs to collect data from FinancialHistory tabs
  const financialDataRef = useRef<{
    incomes: EventDetails[];
    expenses: EventDetails[];
    reservations: Reservation[];
    contracts: Contract[];
    isLoading: boolean;
  }>({
    incomes: [],
    expenses: [],
    reservations: [],
    contracts: [],
    isLoading: true
  });

  // PDF Export section toggles
  const [exportSections, setExportSections] = useState({
    location: true,
    incomes: true,
    expenses: true,
    reservations: true,
    contracts: true
  });

  // State for export options modal
  const [isExportOptionsOpen, setIsExportOptionsOpen] = useState(false);

  // EFFICIENT: Callback to collect data from FinancialHistory tabs
  const updateFinancialData = (type: 'incomes' | 'expenses' | 'reservations' | 'contracts', data: any[], isLoading: boolean) => {
    financialDataRef.current = {
      ...financialDataRef.current,
      [type]: data,
      isLoading: financialDataRef.current.isLoading && isLoading
    };
  };

  const getStatusColor = (status: Location["status"]) =>
    status === "active"
      ? "bg-green-100 text-green-800"
      : "bg-red-100 text-red-800";

  const colors = {
    office: "bg-blue-100 text-blue-800",
    warehouse: "bg-yellow-100 text-yellow-800",
    retail: "bg-green-100 text-green-800",
    other: "bg-gray-100 text-gray-800",
  };

  const getTypeColor = (type: keyof typeof colors) => {
    return colors[type];
  };

  // Calculate capacity usage statistics
  const activeReservations =
    location?.reservations?.filter((r) => {
      // Check if reservation is active or pending
      const isActiveStatus = r.status === "active" || r.status === "pending";

      // Check if the end date is in the future
      const endDate = new Date(r.endDate);
      const isNotExpired = endDate >= new Date();

      // Only include reservations that are both active/pending AND not expired
      return isActiveStatus && isNotExpired;
    }) || [];

  const usedCapacity = activeReservations.reduce(
    (sum, res) => sum + res.capacity,
    0,
  );
  const availableCapacity = location ? location.capacity - usedCapacity : 0;
  const capacityUtilization = location
    ? (usedCapacity / location.capacity) * 100
    : 0;

  // Process monthly capacity data
  const capacityData = useMemo(() => {
    if (!location) return [];

    const now = new Date();
    const data = [];

    // Generate past 6 months and future 6 months
    for (let i = -6; i <= 6; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() + i, 1);
      const monthKey = date.toISOString().substring(0, 7); // YYYY-MM format
      const monthName = date.toLocaleString(
        language === "ar" ? "ar-EG" : "en-US",
        { month: "short" },
      );
      const year = date.getFullYear();

      // Calculate reserved capacity for this month
      let reservedCapacity = 0;
      if (location.reservations) {
        reservedCapacity = location.reservations
          .filter((res) => {
            const startDate = new Date(res.startDate);
            const endDate = new Date(res.endDate);

            // Check if reservation overlaps with this month
            const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
            const monthEnd = new Date(
              date.getFullYear(),
              date.getMonth() + 1,
              0,
            );

            return (
              startDate <= monthEnd &&
              endDate >= monthStart &&
              (res.status === "active" || res.status === "pending")
            );
          })
          .reduce((sum, res) => sum + res.capacity, 0);
      }

      const isPast = date < now;

      data.push({
        month: monthName,
        year,
        monthKey,
        totalCapacity: location.capacity,
        reservedCapacity,
        availableCapacity: location.capacity - reservedCapacity,
        isPast,
      });
    }

    return data;
  }, [location]);

  // Process daily capacity data for selected month
  const dailyCapacityData = useMemo(() => {
    if (!location || !selectedMonth) return [];

    const [year, month] = selectedMonth.split("-").map(Number);
    const daysInMonth = new Date(year, month, 0).getDate();
    const data = [];

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD

      // Calculate reserved capacity for this day
      let reservedCapacity = 0;
      const dayReservations: any[] = [];

      if (location.reservations) {
        location.reservations
          .filter((res) => {
            const startDate = new Date(res.startDate);
            const endDate = new Date(res.endDate);

            // Check if reservation includes this day
            return (
              startDate <= date &&
              endDate >= date &&
              (res.status === "active" || res.status === "pending")
            );
          })
          .forEach((res) => {
            reservedCapacity += res.capacity;
            dayReservations.push({
              id: res.id,
              clientName: res.clientName,
              capacity: res.capacity,
            });
          });
      }

      data.push({
        day,
        date: dateStr,
        weekday: date.toLocaleString("default", { weekday: "short" }),
        totalCapacity: location.capacity,
        reservedCapacity,
        availableCapacity: location.capacity - reservedCapacity,
        reservations: dayReservations,
      });
    }

    return data;
  }, [location, selectedMonth]);

  const handleMonthClick = (monthKey: string) => {
    if (selectedMonth === monthKey) {
      setIsDailyViewOpen(!isDailyViewOpen);
    } else {
      setSelectedMonth(monthKey);
      setIsDailyViewOpen(true);
    }
  };

  const handleCloseMonthView = () => {
    setSelectedMonth(null);
    setIsDailyViewOpen(false);
  };

  const handleDelete = async () => {
    if (location && location.id) {
      setIsDeleting(true);
      setDeleteError(null);

      try {
        await locationServices.deleteLocation(location.id);
        // Toast is now handled in the service, so we don't need to show it here
        onDelete(); // Update UI after successful deletion
        setIsDeleteModalOpen(false); // Close modal
      } catch (error: any) {
        console.error("Error deleting location:", error);
        // Handle error response for the modal display
        if (error.response && error.response.data) {
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting location";
          setDeleteError(errorMessage);
        } else {
          setDeleteError("Failed to delete location. Please try again.");
        }
      } finally {
        setIsDeleting(false);
      }
    }
  };

  // Check if all data is loaded - SIMPLIFIED
  const isAllDataLoaded = !financialDataRef.current.isLoading;

  // Filter data based on export sections - USING REF DATA
  const getFilteredExportData = () => {
    const data: any = {};

    if (exportSections.location) {
      data.locations = [location];
    }

    if (exportSections.incomes || exportSections.expenses) {
      const allEvents = [...financialDataRef.current.incomes, ...financialDataRef.current.expenses];
      const filteredEvents = allEvents.filter(event => {
        if (exportSections.incomes && event.category === 'income') return true;
        if (exportSections.expenses && event.category === 'expense') return true;
        return false;
      });
      data.events = filteredEvents;
    }

    if (exportSections.reservations) {
      data.reservations = financialDataRef.current.reservations;
    }

    if (exportSections.contracts) {
      data.contracts = financialDataRef.current.contracts;
    }

    return data;
  };

  // Export handlers - SIMPLIFIED AND EFFICIENT
  const handleExport = () => {
    if (!location || !isAllDataLoaded) return;

    // Open export options modal first
    setIsExportOptionsOpen(true);
  };

  // Handle final export after section selection
  const handleFinalExport = () => {
    if (!location) return;

    const exportData = getFilteredExportData();

    // Close options modal
    setIsExportOptionsOpen(false);

    // Open the PDF generator modal with selected data
    openPDFModal(
      exportData,
      `${t("locationReport")} - ${location.name}`,
      'locations'
    );
  };

  if (!location) {
    return (
      <Card className="h-full w-full">
        <CardContent className="flex flex-col items-center justify-center py-16">
          <Building className="h-16 w-16 text-gray-300 dark:text-gray-600" />
          <p className="mt-4 max-w-sm text-center text-gray-500 dark:text-gray-400">
            {t("noLocationSelected")}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="w-full">
        <CardHeader className="flex flex-col space-y-4 pb-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="flex items-center gap-2 sm:gap-3">
            {onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 sm:h-9 sm:w-9"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <div className="min-w-0 flex-1">
              <CardTitle className="truncate text-lg font-semibold sm:text-xl">
                {location?.name}
              </CardTitle>
              <p className="truncate text-xs text-gray-600 dark:text-gray-400 sm:text-sm">
                {location?.address}
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
            {/* <Button
              variant="outline"
              size="sm"
              onClick={() => {
                router.push(`/dashboard/locations/${location?.id}/history`);
              }}
              className="flex items-center gap-2"
            >
              <History className="h-4 w-4" />
              {t("viewFullHistory")}
            </Button> */}
            {canExport && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                disabled={!isAllDataLoaded}
                className={`flex items-center gap-2 border-gray-200 text-gray-700 dark:border-gray-800 dark:text-gray-400 ${
                  isAllDataLoaded
                    ? "bg-gray-50 hover:bg-gray-100 hover:text-gray-800 dark:bg-gray-900/20 dark:hover:bg-gray-900/30"
                    : "bg-gray-200 cursor-not-allowed opacity-50 dark:bg-gray-800"
                }`}
                title={
                  isAllDataLoaded
                    ? t("exportPDF")
                    : t("Loading data... Please wait")
                }
              >
                {!isAllDataLoaded ? (
                  <>
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-400 border-t-transparent"></div>
                    {t("Loading...")}
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    {t("exportPDF")}
                  </>
                )}
              </Button>
            )}
            {canEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className="flex items-center gap-2 border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30"
              >
                <Edit className="h-4 w-4" />
                {t("edit")}
              </Button>
            )}
            {canDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => setIsDeleteModalOpen(true)}
                className="flex items-center gap-2 bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800"
              >
                <Trash2 className="h-4 w-4" />
                {t("delete")}
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-6 p-4 pt-6 sm:space-y-8 sm:p-6">
          {/* Main Info Section */}
          <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-800/50 sm:p-4">
            <div className="flex items-start gap-3 sm:gap-4">
              <Building className="mt-1 h-5 w-5 flex-shrink-0 text-gray-500 dark:text-gray-400 sm:h-6 sm:w-6" />
              <div className="min-w-0 flex-1">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 sm:text-xl">
                  {location.name}
                </h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-300 sm:text-base">
                  {location.address}
                </p>
                <div className="mt-3 flex flex-wrap items-center gap-1.5 sm:gap-2">
                  <Badge
                    variant="secondary"
                    className={`px-2 py-1 text-xs sm:px-3 sm:text-sm ${getTypeColor(location.type as keyof typeof colors)}`}
                  >
                    {t(location.type)}
                  </Badge>
                  <Badge
                    variant="secondary"
                    className={`px-2 py-1 text-xs sm:px-3 sm:text-sm ${getStatusColor(location.status)}`}
                  >
                    {t(location.status)}
                  </Badge>
                  <Badge
                    variant="secondary"
                    className="bg-purple-100 px-2 py-1 text-xs text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 sm:px-3 sm:text-sm"
                  >
                    {t("capacity")}:{" "}
                    {Number(location.capacity).toLocaleString(
                      language === "ar" ? "ar-EG" : "en-US",
                    )}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Monthly Capacity Chart - New Section */}
          <div className="space-y-3 sm:space-y-4">
            <div className="mb-3 flex flex-col gap-2 sm:mb-4 sm:flex-row sm:items-center sm:justify-between sm:gap-0">
              <h4 className="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white sm:text-base">
                <BarChart3 className="h-4 w-4 text-gray-500 sm:h-5 sm:w-5" />
                {t("capacityTrend")}
              </h4>

              {/* Add close button for selected month */}
              {selectedMonth && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCloseMonthView}
                  className="w-full text-gray-500 hover:text-gray-700 sm:w-auto"
                >
                  <X className="mr-1.5 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="text-xs sm:text-sm">
                    {t("clearSelection")}
                  </span>
                </Button>
              )}
            </div>

            <div className="rounded-lg bg-white p-3 shadow-sm dark:bg-gray-800 sm:p-6">
              <div className="h-64 sm:h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={capacityData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    barGap={0}
                    barSize={20}
                    onClick={(data) =>
                      data &&
                      handleMonthClick(
                        data.activePayload?.[0]?.payload.monthKey,
                      )
                    }
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tickFormatter={(value, index) => {
                        const item = capacityData[index];
                        return `${value} ${item.year !== new Date().getFullYear() ? item.year.toString().substr(2, 2) : ""}`;
                      }}
                    />
                    <YAxis axisLine={false} tickLine={false} />
                    <Tooltip
                      formatter={(value, name) => {
                        const formattedValue = Number(value).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        );
                        if (name === "reservedCapacity")
                          return [formattedValue, t("reservedCapacity")];
                        if (name === "availableCapacity")
                          return [formattedValue, t("availableCapacity")];
                        return [formattedValue, name];
                      }}
                      labelFormatter={(label, payload) => {
                        if (payload && payload.length > 0) {
                          const { year, month } = payload[0].payload;
                          return `${month} ${Number(year).toLocaleString(
                            language === "ar" ? "ar-EG" : "en-US",
                          )}`;
                        }
                        return label;
                      }}
                    />
                    <Legend
                      formatter={(value) => {
                        if (value === "reservedCapacity")
                          return t("reservedCapacity");
                        if (value === "availableCapacity")
                          return t("availableCapacity");
                        return value;
                      }}
                    />
                    <Bar
                      dataKey="reservedCapacity"
                      name="reservedCapacity"
                      stackId="a"
                      fill="#3b82f6"
                    >
                      {capacityData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={
                            entry.monthKey === selectedMonth
                              ? "#2563eb"
                              : "#3b82f6"
                          }
                          cursor="pointer"
                        />
                      ))}
                    </Bar>
                    <Bar
                      dataKey="availableCapacity"
                      name="availableCapacity"
                      stackId="a"
                      fill="#e2e8f0"
                    >
                      {capacityData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={
                            entry.monthKey === selectedMonth
                              ? "#cbd5e1"
                              : "#e2e8f0"
                          }
                          cursor="pointer"
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <div className="mt-2 text-center text-sm text-gray-500 dark:text-gray-400">
                {selectedMonth
                  ? t("clickClearSelectionToResetView")
                  : t("clickOnMonthToViewDailyCapacity")}
              </div>
            </div>

            {/* Daily Capacity View */}
            {selectedMonth && isDailyViewOpen && (
              <div className="rounded-lg bg-white p-3 shadow-sm dark:bg-gray-800 sm:p-6">
                <div className="mb-4 flex flex-col gap-2 sm:mb-6 sm:flex-row sm:items-center sm:justify-between sm:gap-0">
                  <h5 className="text-sm font-semibold text-gray-900 dark:text-white sm:text-base">
                    {new Date(selectedMonth + "-01").toLocaleString(
                      language === "ar" ? "ar-EG" : "en-US",
                      {
                        month: "long",
                        year: "numeric",
                      },
                    )}{" "}
                    {t("dailyCapacity")}
                  </h5>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsDailyViewOpen(false)}
                    className="w-full sm:w-auto"
                  >
                    <X className="h-3 w-3 sm:h-4 sm:w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-7 gap-1 sm:gap-2">
                  {[
                    t("sun") || "Sun",
                    t("mon") || "Mon",
                    t("tue") || "Tue",
                    t("wed") || "Wed",
                    t("thu") || "Thu",
                    t("fri") || "Fri",
                    t("sat") || "Sat",
                  ].map((day) => (
                    <div
                      key={day}
                      className="p-0.5 text-center text-xs font-medium text-gray-500 dark:text-gray-400 sm:p-1"
                    >
                      {day}
                    </div>
                  ))}

                  {/* Calculate empty cells at the start of the month */}
                  {Array.from({
                    length: new Date(selectedMonth + "-01").getDay(),
                  }).map((_, i) => (
                    <div
                      key={`empty-start-${i}`}
                      className="h-16 rounded-lg bg-gray-50 dark:bg-gray-800/50 sm:h-24"
                    ></div>
                  ))}

                  {dailyCapacityData.map((dayData) => {
                    const utilization =
                      (dayData.reservedCapacity / dayData.totalCapacity) * 100;

                    return (
                      <div
                        key={dayData.date}
                        className={`h-16 rounded-lg border p-1.5 sm:h-24 sm:p-2 ${
                          dayData.date ===
                          new Date().toISOString().split("T")[0]
                            ? "border-blue-500 dark:border-blue-400"
                            : "border-gray-200 dark:border-gray-700"
                        } cursor-pointer transition-shadow hover:shadow-md`}
                      >
                        <div className="flex h-full flex-col">
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium sm:text-sm">
                              {Number(dayData.day).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}
                            </span>
                            {dayData.reservedCapacity > 0 && (
                              <span
                                className={`rounded-full px-1 py-0.5 text-xs sm:px-1.5 ${
                                  utilization > 80
                                    ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                                    : utilization > 50
                                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                                      : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                                }`}
                              >
                                {Number(utilization).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                  {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0,
                                  },
                                )}
                                %
                              </span>
                            )}
                          </div>

                          <div className="mt-auto">
                            {dayData.reservations
                              .slice(0, 2)
                              .map((res, idx) => (
                                <div
                                  key={res.id}
                                  className="truncate text-xs text-gray-600 dark:text-gray-400"
                                >
                                  {res.clientName.substring(0, 8)}...
                                </div>
                              ))}
                            {dayData.reservations.length > 2 && (
                              <div className="text-xs text-gray-500 dark:text-gray-500">
                                +
                                {Number(
                                  dayData.reservations.length - 2,
                                ).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}{" "}
                                {t("more")}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* Calculate empty cells at the end of the month */}
                  {Array.from({
                    length:
                      (7 -
                        ((dailyCapacityData.length +
                          new Date(selectedMonth + "-01").getDay()) %
                          7)) %
                      7,
                  }).map((_, i) => (
                    <div
                      key={`empty-end-${i}`}
                      className="h-24 rounded-lg bg-gray-50 dark:bg-gray-800/50"
                    ></div>
                  ))}
                </div>

                {dailyCapacityData.some(
                  (day) => day.reservations.length > 0,
                ) && (
                  <div className="mt-6 space-y-4">
                    <h6 className="font-medium text-gray-900 dark:text-white">
                      {t("monthReservations")}
                    </h6>
                    <div className="space-y-2">
                      {dailyCapacityData
                        .filter((day) => day.reservations.length > 0)
                        .map((day) => (
                          <div
                            key={day.date}
                            className="rounded-lg bg-gray-50 p-3 dark:bg-gray-800/50"
                          >
                            <div className="mb-2 flex items-center justify-between">
                              <div className="font-medium text-gray-900 dark:text-white">
                                {new Date(day.date).toLocaleDateString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}
                              </div>
                              <Badge>
                                {Number(day.reservedCapacity).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}
                                /
                                {Number(day.totalCapacity).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}{" "}
                                {t("units")}
                              </Badge>
                            </div>
                            <div className="space-y-1.5">
                              {day.reservations.map((res) => (
                                <div
                                  key={res.id}
                                  className="flex justify-between text-sm"
                                >
                                  <span className="text-gray-700 dark:text-gray-300">
                                    {res.clientName}
                                  </span>
                                  <span className="text-gray-600 dark:text-gray-400">
                                    {Number(res.capacity).toLocaleString(
                                      language === "ar" ? "ar-EG" : "en-US",
                                    )}{" "}
                                    {t("units")}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* New Capacity Allocation Section */}
          <div className="space-y-3 sm:space-y-4">
            <h4 className="mb-3 text-sm font-medium text-gray-900 dark:text-white sm:mb-4 sm:text-base">
              {t("capacityAllocation")}
            </h4>

            <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2">
              {/* Capacity Overview Card */}
              <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-6">
                <div className="mb-3 flex items-center justify-between sm:mb-4">
                  <div>
                    <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 sm:text-sm">
                      {t("totalCapacity")}
                    </h5>
                    <p className="mt-1 text-xl font-bold text-gray-900 dark:text-gray-100 sm:text-2xl">
                      {Number(location?.capacity || 0).toLocaleString(
                        language === "ar" ? "ar-EG" : "en-US",
                      )}
                    </p>
                  </div>
                  <PieChart className="h-6 w-6 text-blue-500 opacity-70 dark:text-blue-400 sm:h-8 sm:w-8" />
                </div>

                <div className="space-y-2">
                  {/* Capacity utilization bar */}
                  <div className="mt-2">
                    <div className="mb-1 flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        {t("capacityUtilization")}
                      </span>
                      <span className="font-medium">
                        {Number(capacityUtilization).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                          {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          },
                        )}
                        %
                      </span>
                    </div>
                    <div className="h-3 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                      <div
                        className="h-full rounded-full bg-blue-500 dark:bg-blue-600"
                        style={{ width: `${capacityUtilization}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Capacity breakdown */}
                  <div className="mt-3 grid grid-cols-1 gap-3 text-xs sm:mt-4 sm:grid-cols-3 sm:gap-4 sm:text-sm">
                    <div className="text-center sm:text-left">
                      <span className="text-gray-500 dark:text-gray-400">
                        {t("usedCapacity")}
                      </span>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {Number(usedCapacity).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        )}
                      </p>
                    </div>
                    <div className="text-center sm:text-left">
                      <span className="text-gray-500 dark:text-gray-400">
                        {t("availableCapacity")}
                      </span>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {Number(availableCapacity).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        )}
                      </p>
                    </div>
                    <div className="text-center sm:text-left">
                      <span className="text-gray-500 dark:text-gray-400">
                        {t("activeReservations")}
                      </span>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {Number(activeReservations.length).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Current Reservations Card */}
              <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-6">
                <h5 className="mb-3 text-xs font-medium text-gray-700 dark:text-gray-300 sm:mb-4 sm:text-sm">
                  {t("currentReservations")}
                </h5>

                {activeReservations.length > 0 ? (
                  <div className="space-y-3">
                    {/* Capacity segments visualization */}
                    <div className="flex h-6 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                      {activeReservations.map((res, idx) => {
                        const percentWidth =
                          (res.capacity / location!.capacity) * 100;
                        const colors = [
                          "bg-blue-500 dark:bg-blue-600",
                          "bg-green-500 dark:bg-green-600",
                          "bg-purple-500 dark:bg-purple-600",
                          "bg-yellow-500 dark:bg-yellow-600",
                          "bg-pink-500 dark:bg-pink-600",
                        ];
                        const color = colors[idx % colors.length];

                        return (
                          <div
                            key={res.id}
                            className={`${color} h-full`}
                            style={{ width: `${percentWidth}%` }}
                            title={`${res.clientName}: ${Number(
                              res.capacity,
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )} units (${Number(percentWidth).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                              {
                                minimumFractionDigits: 1,
                                maximumFractionDigits: 1,
                              },
                            )}%)`}
                          />
                        );
                      })}
                      {availableCapacity > 0 && (
                        <div
                          className="h-full bg-gray-300 dark:bg-gray-600"
                          style={{
                            width: `${(availableCapacity / location!.capacity) * 100}%`,
                          }}
                          title={`Available: ${Number(
                            availableCapacity,
                          ).toLocaleString(
                            language === "ar" ? "ar-EG" : "en-US",
                          )} units`}
                        />
                      )}
                    </div>

                    {/* Individual reservations list */}
                    <div className="mt-4 space-y-2">
                      {activeReservations.map((res, idx) => (
                        <div
                          key={res.id}
                          className="flex items-center justify-between rounded-md bg-gray-50 p-2 dark:bg-gray-700/50"
                        >
                          <div className="flex items-center space-x-2">
                            <div
                              className={`h-3 w-3 rounded-full 
                            ${idx % 2 === 0 ? "bg-blue-500" : "bg-green-500"}`}
                            />
                            <div>
                              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {res.clientName}
                              </p>
                              <p className="text-xs text-gray-500">
                                {new Date(res.startDate).toLocaleDateString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}{" "}
                                -{" "}
                                {new Date(res.endDate).toLocaleDateString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}
                              </p>
                            </div>
                          </div>
                          <div className="flex flex-col items-end">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {Number(res.capacity).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              {t("units")}
                            </span>
                            <span className="text-xs text-gray-500">
                              {language === "ar" ? "%" : ""}
                              {Number(
                                (res.capacity / location!.capacity) * 100,
                              ).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                                {
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0,
                                },
                              )}
                              {language === "ar" ? "" : "%"}
                            </span>
                          </div>
                        </div>
                      ))}

                      {/* Available capacity row */}
                      {availableCapacity > 0 && (
                        <div className="flex items-center justify-between rounded-md border border-dashed border-gray-300 bg-gray-50 p-2 dark:border-gray-600 dark:bg-gray-700/50">
                          <div className="flex items-center space-x-2">
                            <div className="h-3 w-3 rounded-full bg-gray-400" />
                            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                              {t("available")}
                            </p>
                          </div>
                          <div className="flex flex-col items-end">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {Number(availableCapacity).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              {t("units")}
                            </span>
                            <span className="text-xs text-gray-500">
                              {language === "ar" ? "%" : ""}
                              {Number(
                                (availableCapacity / location!.capacity) * 100,
                              ).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                                {
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0,
                                },
                              )}
                              {language === "ar" ? "" : "%"}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="flex h-32 items-center justify-center text-center">
                    <div className="text-gray-500 dark:text-gray-400">
                      <Calendar className="mx-auto mb-2 h-8 w-8 opacity-40" />
                      <p>{t("noActiveReservations")}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="mb-4 text-sm font-medium text-gray-900 dark:text-white">
              {t("ownershipDetails")}
            </h4>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div className="flex items-start gap-3">
                <Building className="mt-1 h-5 w-5 text-gray-500 dark:text-gray-400" />
                <div></div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("ownedBy")}
                </p>
                <p className="mt-1 text-gray-900 dark:text-gray-100">
                  {/* Display the name from primaryOwner object if available, fallback to ownedBy */}
                  {location.primaryOwner?.name ||
                    location.ownedBy ||
                    t("notSpecified")}
                  {location.primaryOwner?.percentage &&
                    ` (${language === "ar" ? "%" : ""}${Number(
                      location.primaryOwner.percentage,
                    ).toLocaleString(language === "ar" ? "ar-EG" : "en-US", {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    })}${language === "ar" ? "" : "%"})`}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Percent className="mt-1 h-5 w-5 text-gray-500 dark:text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("ourPercentage")}
                </p>
                <p className="mt-1 text-gray-900 dark:text-gray-100">
                  {language === "ar" ? "%" : ""}
                  {Number(location.ourPercentage).toLocaleString(
                    language === "ar" ? "ar-EG" : "en-US",
                    {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    },
                  )}
                  {language === "ar" ? "" : "%"}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Users className="mt-1 h-5 w-5 text-gray-500 dark:text-gray-400" />
              <div>
                {location.ownershipShares
                  ? location.ownershipShares.map((share, idx) => (
                      <p
                        key={idx}
                        className="pl-2 text-sm text-gray-600 dark:text-gray-400"
                      >
                        • {share.name}{" "}
                        <span className="font-medium">
                          ({language === "ar" ? "%" : ""}
                          {Number(share.percentage).toLocaleString(
                            language === "ar" ? "ar-EG" : "en-US",
                            {
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0,
                            },
                          )}
                          {language === "ar" ? "" : "%"})
                        </span>
                      </p>
                    ))
                  : location.sharedWith.map((partner, idx) => (
                      <p
                        key={idx}
                        className="pl-2 text-sm text-gray-600 dark:text-gray-400"
                      >
                        • {partner}
                      </p>
                    ))}
              </div>
            </div>
          </div>

          {/* Reservation Contacts - Enhanced UI */}
          {activeReservations.length > 0 && (
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
              <div className="mb-4 flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                  <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h5 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {t("reservationContacts")}
                  </h5>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {activeReservations.length} {t("activeReservations")}
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                {activeReservations.map((reservation, index) => (
                  <div
                    key={reservation.id}
                    className="group relative rounded-lg border border-gray-100 bg-gray-50 p-4 transition-all hover:border-blue-200 hover:bg-blue-50/50 hover:shadow-sm dark:border-gray-700 dark:bg-gray-700/30 dark:hover:border-blue-700 dark:hover:bg-blue-900/10"
                  >
                    {/* Reservation Color Indicator */}
                    <div
                      className={`absolute left-0 top-0 h-full w-1 rounded-l-lg ${
                        index % 3 === 0
                          ? "bg-blue-500"
                          : index % 3 === 1
                            ? "bg-green-500"
                            : "bg-purple-500"
                      }`}
                    />

                    <div className="ml-4">
                      {/* Header with Client Name and Capacity */}
                      <div className="mb-3 flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`flex h-8 w-8 items-center justify-center rounded-full text-xs font-medium text-white ${
                              index % 3 === 0
                                ? "bg-blue-500"
                                : index % 3 === 1
                                  ? "bg-green-500"
                                  : "bg-purple-500"
                            }`}
                          >
                            {reservation.clientName.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <p className="text-base font-semibold text-gray-900 dark:text-gray-100">
                              {reservation.clientName}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {t("client")}
                            </p>
                          </div>
                        </div>

                        <div className="flex flex-col items-end gap-2">
                          <Badge
                            variant="outline"
                            className={`border-none font-medium ${
                              index % 3 === 0
                                ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                                : index % 3 === 1
                                  ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                  : "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400"
                            }`}
                          >
                            {Number(reservation.capacity).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}{" "}
                            {t("units")}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {language === "ar" ? "%" : ""}
                            {Number(
                              (reservation.capacity / location!.capacity) * 100,
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                              {
                                minimumFractionDigits: 1,
                                maximumFractionDigits: 1,
                              },
                            )}
                            {language === "ar" ? "" : "%"}
                          </span>
                        </div>
                      </div>

                      {/* Reservation Details Grid */}
                      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                        {/* Duration */}
                        <div className="flex items-center gap-2 rounded-md bg-white p-3 dark:bg-gray-800/50">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              {t("duration")}
                            </p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {new Date(
                                reservation.startDate,
                              ).toLocaleDateString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              -{" "}
                              {new Date(reservation.endDate).toLocaleDateString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}
                            </p>
                          </div>
                        </div>

                        {/* Days Remaining */}
                        <div className="flex items-center gap-2 rounded-md bg-white p-3 dark:bg-gray-800/50">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              {t("daysRemaining")}
                            </p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {Number(
                                Math.max(
                                  0,
                                  Math.ceil(
                                    (new Date(reservation.endDate).getTime() -
                                      new Date().getTime()) /
                                      (1000 * 60 * 60 * 24),
                                  ),
                                ),
                              ).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              {t("days")}
                            </p>
                          </div>
                        </div>

                        {/* Reservation Status */}
                        <div className="flex items-center gap-2 rounded-md bg-white p-3 dark:bg-gray-800/50">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <div>
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              {t("status")}
                            </p>
                            <p className="text-sm font-medium text-green-600 dark:text-green-400">
                              {t("active")}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Footer Information */}
          <div className="mt-6 border-t pt-4 dark:border-gray-700"></div>
          <div className="flex items-start gap-3">
            <Calendar className="mt-1 h-5 w-5 text-gray-500 dark:text-gray-400" />
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("created")}
              </p>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {new Date(location.createdAt).toLocaleDateString(
                  language === "ar" ? "ar-EG" : "en-US",
                )}{" "}
                {t("by")} {location.createdBy}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Always show Financial History */}
      {location && (
        <div className="mt-4 sm:mt-6">
          <FinancialHistory
            locationId={location.id}
            isLoading={false}
            onDataUpdate={updateFinancialData}
          />
        </div>
      )}

      {/* Export Options Modal */}
      {isExportOptionsOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
            <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t("exportOptions")}
            </h3>
            <p className="mb-6 text-sm text-gray-600 dark:text-gray-400">
              {t("selectSectionsToExport")}
            </p>

            <div className="space-y-3">
              {/* Location Details */}
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={exportSections.location}
                  onChange={(e) => setExportSections(prev => ({ ...prev, location: e.target.checked }))}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t("locationDetails")} ({location?.name})
                </span>
              </label>

              {/* Income Events */}
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={exportSections.incomes}
                  onChange={(e) => setExportSections(prev => ({ ...prev, incomes: e.target.checked }))}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t("incomes")} ({financialDataRef.current.incomes.length} {t("events")})
                  {financialDataRef.current.isLoading && <span className="ml-2 text-xs text-blue-500">{t("loading...")}</span>}
                </span>
              </label>

              {/* Expense Events */}
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={exportSections.expenses}
                  onChange={(e) => setExportSections(prev => ({ ...prev, expenses: e.target.checked }))}
                  className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t("expenses")} ({financialDataRef.current.expenses.length} {t("events")})
                  {financialDataRef.current.isLoading && <span className="ml-2 text-xs text-red-500">{t("loading...")}</span>}
                </span>
              </label>

              {/* Reservations */}
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={exportSections.reservations}
                  onChange={(e) => setExportSections(prev => ({ ...prev, reservations: e.target.checked }))}
                  className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t("reservations")} ({financialDataRef.current.reservations.length} {t("items")})
                  {financialDataRef.current.isLoading && <span className="ml-2 text-xs text-purple-500">{t("loading...")}</span>}
                </span>
              </label>

              {/* Contracts */}
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={exportSections.contracts}
                  onChange={(e) => setExportSections(prev => ({ ...prev, contracts: e.target.checked }))}
                  className="h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t("contracts")} ({financialDataRef.current.contracts.length} {t("items")})
                  {financialDataRef.current.isLoading && <span className="ml-2 text-xs text-green-500">{t("loading...")}</span>}
                </span>
              </label>
            </div>

            <div className="mt-6 flex space-x-3">
              <button
                onClick={() => setIsExportOptionsOpen(false)}
                className="flex-1 rounded-lg bg-gray-300 px-4 py-2 text-gray-800 hover:bg-gray-400 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              >
                {t("cancel")}
              </button>
              <button
                onClick={handleFinalExport}
                disabled={!Object.values(exportSections).some(Boolean)}
                className="flex-1 rounded-lg bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {t("generatePDF")}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* PDF Generator Modal - NEW ADVANCED PDF GENERATOR */}
      <PDFGeneratorModal
        isOpen={modalProps.isOpen}
        onClose={modalProps.onClose}
        data={modalProps.data}
        title={modalProps.title}
        mode={modalProps.mode}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeleteError(null);
        }}
        onConfirm={handleDelete}
        message={`${t("deleteLocationWarning")} ${t("deleteLocationConditions")}`}
        title={t("deleteLocation")}
        itemName={location?.name || ""}
        isLoading={isDeleting}
        error={deleteError}
      />
    </>
  );
};

export default LocationDetails;

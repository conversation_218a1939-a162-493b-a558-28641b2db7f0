import { Reservation } from "@/lib/types/location";
import { useQuery } from "@tanstack/react-query";
import apiClient from "@/lib/api/apiClient";
import ReservationServices, {
  ReservationEventsApiResponse,
} from "@/lib/reservations";

export interface ReservationsApiResponse {
  reservations: Reservation[];
  count: number;
}

export const useReservations = () => {
  return useQuery<ReservationsApiResponse>({
    queryKey: ["reservations"],
    queryFn: async () => {
      const { data } = await apiClient.get("/reservations/api/getall/");
      return data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes cache
  });
};

export const useReservationEvents = (reservationId: string | undefined) => {
  return useQuery<ReservationEventsApiResponse>({
    queryKey: ["reservation-events", reservationId],
    queryFn: async () => {
      if (!reservationId) {
        return { incomes: [], expenses: [] };
      }
      const reservationService = ReservationServices();
      return await reservationService.getReservationEventsById(reservationId);
    },
    enabled: !!reservationId,
    staleTime: 5 * 60 * 1000, // 5 minutes cache
  });
};

import React, { useState, useEffect, useRef } from "react";
import { X, Search, User, Check } from "lucide-react";

export interface ContactSelectDropdownProps {
  options: string[];
  value: string;
  onSelect: (value: string) => void;
  placeholder: string;
  className?: string;
  disabled?: boolean;
}

const ContactSelectDropdown: React.FC<ContactSelectDropdownProps> = ({
  options,
  value,
  onSelect,
  placeholder,
  className = "",
  disabled = false,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const filteredOptions =
    searchTerm.trim() === ""
      ? options
      : options.filter((option) =>
          option.toLowerCase().includes(searchTerm.toLowerCase()),
        );

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div
        className={`flex cursor-pointer items-center overflow-hidden rounded-lg border focus-within:ring-2 focus-within:ring-blue-500 dark:border-gray-700 ${disabled ? "cursor-not-allowed opacity-50" : ""}`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setIsOpen(true)}
          placeholder={value ? value : placeholder}
          className="flex-1 border-none p-2 focus:outline-none dark:bg-gray-800 dark:text-gray-100"
          disabled={disabled}
        />
        <div className="px-2 text-gray-400">
          {isOpen ? <X className="h-4 w-4" /> : <Search className="h-4 w-4" />}
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-10 mt-1 max-h-48 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-800">
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option) => (
              <div
                key={option}
                className={`flex cursor-pointer items-center justify-between p-2 hover:bg-gray-100 dark:hover:bg-gray-700 ${value === option ? "bg-blue-50 dark:bg-blue-900/20" : ""}`}
                onClick={() => {
                  onSelect(option);
                  setSearchTerm("");
                  setIsOpen(false);
                }}
              >
                <div className="flex items-center">
                  <User className="mr-2 h-4 w-4 text-gray-400" />
                  <span>{option}</span>
                </div>
                {value === option && (
                  <Check className="h-4 w-4 text-blue-500" />
                )}
              </div>
            ))
          ) : (
            <div className="p-2 text-center text-gray-500">
              No matching contacts
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ContactSelectDropdown;

import React, { useState, useEffect } from "react";
import { DocumentUpload } from "@/lib/interfaces/contract";
import UploadFiles from "@/components/common/uploadFile";
import useLanguage from "@/hooks/useLanguage";
interface UploadContractPopUpProps {
  onSubmit: (data: DocumentUpload[]) => void;
  onClose: () => void;
  data: any;
}

const UploadContractPopUp: React.FC<UploadContractPopUpProps> = ({
  onSubmit,
  onClose,
  data,
}) => {
  const [files, setFiles] = useState<DocumentUpload[]>([]);
  useEffect(() => {
    if (data && data.documentUpload) {
      // Handle both document_uploaded (from API) and documentUpload (from form)
      const existingDocs = data.documentUpload || data.document_uploaded || [];
      setFiles(existingDocs);
    }
  }, [data]);

  const handleSubmit = () => {
    onSubmit(files);
    onClose();
  };

  const { t } = useLanguage();

  const handleFilesUpload = (urls: string[]) => {
    const uploadedFiles = urls.map((url, index) => ({
      id: `file-${Date.now()}-${index}`,
      name: `Document ${index + 1}`,
      url,
      uploadDate: new Date().toISOString(),
      type: "application/pdf", // Assuming PDF for simplicity, adjust as needed
      fileType: "document",
    }));

    setFiles((prevFiles) => [...prevFiles, ...uploadedFiles]);
    onSubmit(uploadedFiles);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
        <h2 className="mb-4 text-lg font-semibold">{t("Upload Documents")}</h2>
        <UploadFiles onUploadComplete={handleFilesUpload} />
        {files.length > 0 && (
          <ul className="mt-4 space-y-2">
            {files.map((file, index) => (
              <li key={file.id} className="text-sm text-blue-600">
                <a
                  href={file.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline"
                >
                  {file.name || `Document ${index + 1}`}
                </a>
              </li>
            ))}
          </ul>
        )}
        <div className="mt-6 flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="rounded bg-gray-200 px-4 py-2 text-gray-700 hover:bg-gray-300"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Submit
          </button>
        </div>
      </div>
    </div>
  );
};

export default UploadContractPopUp;

import React from "react";
import { Contract } from "@/lib/interfaces/contract";
import {
  FaRedo,
  FaTimes,
  FaPercentage,
  FaCalendarAlt,
  FaFileContract,
} from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import { formatDate, formatCurrency } from "../utils";

interface RenewalTabProps {
  contract: Contract;
}

const RenewalTab: React.FC<RenewalTabProps> = ({ contract }) => {
  const { t, language } = useLanguage();

  // Check if renewal_terms exists, if not create a default value
  const renewalTerms = contract.renewal_terms || {
    auto_renew: false,
    increase_percentage: 0,
    notice_period_days: 30,
  };

  // Calculate notice deadline date with proper error handling
  const getNoticeDeadlineDate = () => {
    try {
      if (!contract.end_date) {
        return "N/A";
      }

      const end_date = new Date(contract.end_date);

      // Check if end_date is valid
      if (isNaN(end_date.getTime())) {
        console.error("Invalid end date:", contract.end_date);
        return "N/A";
      }

      // Calculate the notice deadline by subtracting days
      const noticePeriodMs =
        Number(renewalTerms.notice_period_days) * 24 * 60 * 60 * 1000;
      const deadlineDate = new Date(end_date.getTime() - noticePeriodMs);

      // Check if result is valid
      if (isNaN(deadlineDate.getTime())) {
        console.error("Invalid calculation result with:", {
          end_date,
          noticePeriodDays: renewalTerms.notice_period_days,
          noticePeriodMs,
        });
        return "N/A";
      }

      return deadlineDate.toISOString();
    } catch (error) {
      console.error("Error calculating notice deadline:", error);
      return "N/A";
    }
  };

  // Local format functions with Arabic support
  const formatDateLocal = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === "ar" ? "ar-EG" : "en-US",
    );
  };

  const formatCurrencyLocal = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div>
      <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4">
        {t("Contract Renewal")}
      </h3>

      <div className="mb-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-700 sm:p-5">
        <div className="mb-4 flex items-center">
          <div
            className={`mr-4 rounded-full p-3 ${renewalTerms.auto_renew ? "bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-200" : "bg-orange-100 text-orange-600 dark:bg-orange-800 dark:text-orange-200"}`}
          >
            {renewalTerms.auto_renew ? (
              <FaRedo size={20} />
            ) : (
              <FaTimes size={20} />
            )}
          </div>
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {renewalTerms.auto_renew
                ? t("Auto-Renewal Enabled")
                : t("Manual Renewal Required")}
            </h4>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {renewalTerms.auto_renew
                ? t("This contract will automatically renew when it expires")
                : t("This contract requires manual renewal before expiration")}
            </p>
          </div>
        </div>

        <div className="mt-6 grid grid-cols-1 gap-4 md:grid-cols-2">
          {renewalTerms.auto_renew && (
            <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
              <div className="mb-3 flex items-center">
                <FaPercentage
                  className="mr-2 text-blue-500 dark:text-blue-400"
                  size={16}
                />
                <h5 className="text-md font-semibold text-gray-800 dark:text-gray-200">
                  {t("Rate Increase")}
                </h5>
              </div>
              <div className="pl-7">
                <p className="mb-1 text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {Number(renewalTerms.increase_percentage).toLocaleString(
                    language === "ar" ? "ar-EG" : "en-US",
                  )}
                  %
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("Rate will increase by this percentage upon renewal")}
                </p>
                <div className="mt-3 text-sm">
                  <div className="mb-1 flex items-center justify-between">
                    <span className="text-gray-500 dark:text-gray-400">
                      {t("Current monthly")}:
                    </span>
                    <span className="font-medium text-gray-900 dark:text-gray-200">
                      {formatCurrencyLocal(contract.monthlyAmount)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between font-medium">
                    <span className="text-gray-500 dark:text-gray-400">
                      {t("New monthly")}:
                    </span>
                    <span className="text-green-600 dark:text-green-400">
                      {formatCurrencyLocal(
                        contract.monthlyAmount *
                          (1 + renewalTerms.increase_percentage / 100),
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-3 flex items-center">
              <FaCalendarAlt
                className="mr-2 text-purple-500 dark:text-purple-400"
                size={16}
              />
              <h5 className="text-md font-semibold text-gray-800 dark:text-gray-200">
                {t("Notice Period")}
              </h5>
            </div>
            <div className="pl-7">
              <p className="mb-1 text-2xl font-bold text-gray-900 dark:text-gray-100">
                {Number(renewalTerms.notice_period_days).toLocaleString(
                  language === "ar" ? "ar-EG" : "en-US",
                )}{" "}
                {t("days")}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {renewalTerms.auto_renew
                  ? t("Notice required to cancel auto-renewal")
                  : t("Notice required to request renewal")}
              </p>

              <div className="mt-3 rounded border border-amber-200 bg-amber-50 p-2 dark:border-amber-800 dark:bg-amber-900">
                <p className="text-sm text-amber-700 dark:text-amber-300">
                  <strong>{t("Notice deadline")}:</strong>{" "}
                  {formatDateLocal(getNoticeDeadlineDate())}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RenewalTab;

// src/services/contactServices.ts
import { AxiosInstance } from "axios";
import { UserDetails, UserPermissions } from "@/lib/interfaces/userDetails";
import { toast } from "react-hot-toast";

import { Contact, contactLoaction } from "@/lib/types/contacts";
import { log } from "console";
import { EventDetails } from "./interfaces/finaces";
import { Reservation } from "./interfaces/reservation";

interface CreateContactPayload {
  name: string;
  email: string;
  phone: string;
  company: string;
  address: string;
  type: string[];
}

export interface ApiContact {
  contact_id: number;
  name: string;
  email: string;
  phone: string;
  company: string;
  address: string;
  type: string[];
  sharedLocations: contactLoaction[];
  ownedLocations: contactLoaction[];
  createdAt: string;
  createdBy: string;
  balance: number;
}

export interface UpdateContactPayload {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  address?: string;
  type?: string[];
}

export interface ContactApiResponse {
  contacts: Contact[];
}

export interface ContactDetailsResponse {
  contact: Contact;
}

export interface ContactDetailsPayload {
  contact_id: number;
}
export interface DeleteContactPayload {
  contact_id: number;
}

export interface ContactType {
  id: string;
  name: string;
  created_at: string;
}

export interface ContactTypesResponse {
  location_types: ContactType[];
  count: number;
}

export interface CreateContactTypePayload {
  name: string;
}

export interface ContactFinancesResponse {
  incomes: EventDetails[];
  expenses: EventDetails[];
}

const createContactServices = (apiClient: AxiosInstance) => ({
  getAllContacts: async (): Promise<Contact[]> => {
    try {
      const response = await apiClient.get("/contacts/api/getall/");
      return response.data.contacts;
    } catch (error) {
      console.error("Error fetching contacts:", error);
      throw error;
    }
  },
  getContactFinances: async (
    contactId: string,
    signal?: AbortSignal,
  ): Promise<ContactFinancesResponse> => {
    try {
      console.log(
        `Making API call to fetch contact finances for ID: ${contactId}...`,
      );
      const response = await apiClient.post(
        "/contacts/api/contact-finances/",
        {
          contact_id: contactId,
        },
        { signal: signal },
      );
      console.log("Raw contact finances API response:", response);

      if (!response.data) {
        console.error("Contact finances API response has no data property");
        return { incomes: [], expenses: [] };
      }

      if (
        response.data.finances?.income?.events &&
        response.data.finances?.expenses?.events
      ) {
        return {
          incomes: response.data.finances.income.events as EventDetails[],
          expenses: response.data.finances.expenses.events as EventDetails[],
        };
      }

      return { incomes: [], expenses: [] };
    } catch (error) {
      if (
        error instanceof Error &&
        (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
      ) {
        console.log("Contact finances API request was cancelled");
        return { incomes: [], expenses: [] };
      }
      console.error("Error fetching contact finances:", error);
      throw new Error("Error fetching contact finances");
    }
  },
  createContact: async (payload: CreateContactPayload): Promise<ApiContact> => {
    try {
      console.log("Creating contact with payload:", payload);
      const response = await apiClient.post("/contacts/api/create/", payload);
      console.log("Created contact response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error creating contact:", error);
      console.error(error);
      throw error;
    }
  },

  getAllContactTypes: async (): Promise<ContactType[]> => {
    try {
      const response = await apiClient.get("/contacts/api/types/");
      console.log("Contact types API response:", response.data);

      // Check if we have the expected structure
      if (!response.data) {
        console.error("Empty response from contact types API");
        return [];
      }

      // Check if the response has the expected location_types property
      if (!response.data.location_types) {
        console.error("Missing location_types in API response:", response.data);

        // If data is directly an array, use it
        if (Array.isArray(response.data)) {
          return response.data.map((type: any) => {
            if (typeof type === "string") {
              // Create an object with id and name both set to the string value
              return {
                id: type,
                name: type,
                created_at: new Date().toISOString(),
              };
            } else {
              // Assume it's already a ContactType or similar object
              return {
                id: type.id || type.name,
                name: type.name,
                created_at: type.created_at || new Date().toISOString(),
              };
            }
          });
        }

        // Try to find the data in a different property
        const possibleArrays = Object.values(response.data).filter(
          Array.isArray,
        );
        if (possibleArrays.length > 0) {
          const types = possibleArrays[0] as any[];
          return types.map((type: any) => {
            if (typeof type === "string") {
              return {
                id: type,
                name: type,
                created_at: new Date().toISOString(),
              };
            } else {
              return {
                id: type.id || type.name,
                name: type.name,
                created_at: type.created_at || new Date().toISOString(),
              };
            }
          });
        }

        return [];
      }

      // Normal flow - return full ContactType objects
      return response.data.location_types;
    } catch (error) {
      console.error("Error fetching contact types:", error);
      return []; // Return empty array instead of throwing to prevent component crashes
    }

  },

  // get contact Reservations
  getContactReservations: async (
    contactId: string,
  ): Promise<Reservation[]> => {
    try {
      const response = await apiClient.post(
        `/reservations/api/by-contact/`,
        { contact_id: contactId },
      );
      console.log("Contact reservations API response:", response.data);
      
      return response.data.reservations;
    } catch (error) {
      console.error("Error fetching contact reservations:", error);
      throw error;
    }
  },

  createContactType: async (
    payload: CreateContactTypePayload,
  ): Promise<ContactType> => {
    try {
      const response = await apiClient.post(
        "/contacts/api/types/create/",
        payload,
      );
      return response.data;
    } catch (error) {
      console.error("Error creating contact type:", error);
      throw error;
    }
  },

  updateContact: async (
    contactId: string,
    payload: UpdateContactPayload,
  ): Promise<ApiContact> => {
    try {
      const response = await apiClient.post(
        `/contacts/api/updatecontact/${contactId}/`,
        payload,
      );
      console.log("Updated contact response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Error updating contact:", error);
      throw error;
    }
  },
  getContactById: async (contactId: number): Promise<Contact> => {
    try {
      const response = await apiClient.get(`/contacts/api/get/${contactId}/`);
      return response.data.contact;
    } catch (error) {
      console.error("Error fetching contact:", error);
      throw error;
    }
  },

  deleteContact: async (contactId: number): Promise<void> => {
    try {
      await apiClient.delete(`/contacts/api/delete/${contactId}/`);
    } catch (error) {
      console.error("Error deleting contact:", error);
      throw error;
    }
  },

  softDeleteContact: async (contactId: string): Promise<void> => {
    try {
      console.log("Sending soft delete request for contact:", contactId);
      // Show loading indicator or buffer before the request
      const loadingToast = toast.loading("Deleting contact...");

      try {
        const response = await apiClient.delete(
          `/contacts/api/softdelete/${contactId}/`,
        );

        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Check response status
        if (response.status === 200) {
          toast.success("Contact has been deleted successfully");
        }
      } catch (error: any) {
        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Handle 409 conflict error specifically
        if (error.response && error.response.status === 409) {
          console.log("409 error data:", error.response.data);

          if (error.response.data.warning) {
            toast.error(error.response.data.warning);
          } else if (error.response.data.message) {
            toast.error(error.response.data.message);
          } else {
            toast.error("Conflict occurred while deleting contact");
          }
        } else if (error.response && error.response.data) {
          // Handle other server errors with specific messages
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting contact";
          toast.error(errorMessage);
        } else {
          // Handle other errors
          toast.error("Failed to delete contact. Please try again.");
          console.error("Error soft deleting contact:", error);
        }
        throw error; // Re-throw for the caller to handle if needed
      }
    } catch (error) {
      console.error("Error soft deleting contact:", error);
      throw error;
    }
  },
});

export default createContactServices;

import React, { useState } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";

interface IncomePopupProps {
    income: EventDetails;
    onClose: () => void;
    onSave: (updatedIncome: EventDetails) => void;
}

const IncomePopup: React.FC<IncomePopupProps> = ({ income, onClose, onSave }) => {
    const { t } = useLanguage();
    const [updatedIncome, setUpdatedIncome] = useState(income);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setUpdatedIncome({ ...updatedIncome, [name]: value });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSave(updatedIncome);
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white p-6 rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700 max-w-md w-full mx-4">
                <h2 className="text-xl font-semibold mb-4 dark:text-white">{t("editIncome")}</h2>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("title")}
                        </label>
                        <input
                            type="text"
                            name="title"
                            value={updatedIncome.title}
                            onChange={handleChange}
                            className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                            required
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("amount")}
                        </label>
                        <input
                            type="number"
                            name="amount"
                            value={updatedIncome.amount}
                            onChange={handleChange}
                            step="0.01"
                            min="0"
                            className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                            required
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("dueDate")}
                        </label>
                        <input
                            type="date"
                            name="dueDate"
                            value={updatedIncome.dueDate ? new Date(updatedIncome.dueDate).toISOString().split('T')[0] : ''}
                            onChange={handleChange}
                            className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                            required
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("status")}
                        </label>
                        <select
                            name="status"
                            value={updatedIncome.status}
                            onChange={handleChange}
                            className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                        >
                            <option value="pending">{t("pending")}</option>
                            <option value="completed">{t("completed")}</option>
                            <option value="cancelled">{t("cancelled")}</option>
                        </select>
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {t("description")}
                        </label>
                        <textarea
                            name="description"
                            value={updatedIncome.description || ''}
                            onChange={handleChange}
                            rows={3}
                            className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300"
                        />
                    </div>
                    
                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500 transition-colors"
                        >
                            {t("cancel")}
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                        >
                            {t("save")}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default IncomePopup;

// src/services/contactServices.ts
import { AxiosInstance } from "axios";

import { IncomeType } from "./interfaces/eventTypes";


interface CreateIncomeTypePayload {
    name: string;
}

const createIncomeTypeServices = (apiClient: AxiosInstance) => ({

    createIncomeType: async (payload: CreateIncomeTypePayload): Promise<IncomeType> => {
        try {
            const response = await apiClient.post("/income/api/types/create/", payload);
            return response.data;
        } catch (error) {
            console.error("Error creating income:", error);
            throw new Error("Error creating income");
        }
    },

});

export default createIncomeTypeServices;
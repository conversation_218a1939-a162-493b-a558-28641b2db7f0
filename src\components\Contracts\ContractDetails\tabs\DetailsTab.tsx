import React from "react";
import { Contract } from "@/lib/interfaces/contract";
import {
  FaFileAlt,
  FaCalendarAlt,
  FaClock,
  FaHistory,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaBuilding,
  FaMapMarkerAlt,
} from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import {
  formatDate,
  safeValue,
  getTimeUntilExpiration,
  getContractDuration,
} from "../utils";

interface DetailsTabProps {
  contract: Contract;
}

const DetailsTab: React.FC<DetailsTabProps> = ({ contract }) => {
  const { t, language } = useLanguage();

  // Local format functions with Arabic support
  const formatDateLocal = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === "ar" ? "ar-EG" : "en-US",
    );
  };

  const getContractDurationLocal = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const months =
      (end.getFullYear() - start.getFullYear()) * 12 +
      (end.getMonth() - start.getMonth());
    return Math.max(0, months);
  };

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      {/* Basic Information */}
      <div>
        <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4">
          {t("Contract Information")}
        </h3>
        <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700 sm:p-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="flex items-start">
              <FaFileAlt
                className="mr-2 mt-1 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Description")}
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                  {safeValue(
                    contract.description,
                    t("No description provided"),
                  )}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <FaCalendarAlt
                className="mr-2 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Contract Period")}
                </p>
                <div className="space-y-1">
                  <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                    {formatDateLocal(contract.start_date)} -{" "}
                    {formatDateLocal(contract.end_date)}
                  </p>
                  <span className="inline-block rounded-full bg-amber-100 px-2 py-1 text-xs font-semibold text-amber-800 dark:bg-amber-800 dark:text-amber-100">
                    {getTimeUntilExpiration(contract.end_date, t, language)}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center">
              <FaClock
                className="mr-2 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Contract Duration")}
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                  {Number(
                    getContractDurationLocal(
                      contract.start_date,
                      contract.end_date,
                    ),
                  ).toLocaleString(language === "ar" ? "ar-EG" : "en-US")}{" "}
                  {t("months")}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <FaHistory
                className="mr-2 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Created")}
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                  {formatDateLocal(contract.created_at)}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <FaHistory
                className="mr-2 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Last Updated")}
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                  {formatDateLocal(contract.updated_at)}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <FaFileAlt
                className="mr-2 mt-1 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Notes")}
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                  {contract.notes || t("No notes")}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Client & Location Details */}
      <div>
        <h3 className="mb-3 mt-4 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4 md:mt-0">
          {t("Client & Property")}
        </h3>
        <div className="mb-4 rounded-lg bg-gray-50 p-3 dark:bg-gray-700 sm:p-4">
          <h4 className="text-md mb-3 font-semibold text-gray-800 dark:text-gray-200">
            {t("Client Information")}
          </h4>
          <div className="grid grid-cols-1 gap-4">
            <div className="flex items-center">
              <FaUser
                className="mr-2 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Client Name")}
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                  {safeValue(contract.client?.name)}
                </p>
              </div>
            </div>

            {contract.client?.company && (
              <div className="flex items-center">
                <FaBuilding
                  className="mr-2 text-gray-500 dark:text-gray-400"
                  size={16}
                />
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {t("Company")}
                  </p>
                  <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                    {contract.client.company}
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-center">
              <FaEnvelope
                className="mr-2 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Email")}
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                  {safeValue(contract.client?.email)}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700 sm:p-4">
          <h4 className="text-md mb-3 font-semibold text-gray-800 dark:text-gray-200">
            {t("Property Information")}
          </h4>
          <div className="grid grid-cols-1 gap-4">
            <div className="flex items-start">
              <FaMapMarkerAlt
                className="mr-2 mt-1 text-gray-500 dark:text-gray-400"
                size={16}
              />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("Property")}
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                  {contract.location.name}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {contract.location.address}, {contract.location.city},{" "}
                  {contract.location.country}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;

import React from 'react';
import ContactOverviewCard from './ContactOverviewCard';
import { Eye, MapPin, Calendar, Users } from 'lucide-react';
import useLanguage from "@/hooks/useLanguage";

interface ContactStatisticsOverviewProps {
  totalRevenue: number;
  revenueGrowth: number;
  totalReservations: number;
  activeReservations: number;
  locationData: Array<{ name: string; percentage: number }>;
  adTypeData: Array<{ type: string; percentage: number }>;
}

const ContactStatisticsOverview: React.FC<ContactStatisticsOverviewProps> = ({
  totalRevenue,
  revenueGrowth,
  totalReservations,
  activeReservations,
  locationData,
  adTypeData
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Revenue Card */}
      <ContactOverviewCard
        title={t("totalRevenue")}
        value={`$${totalRevenue.toLocaleString()}`}
        changeValue={revenueGrowth}
        changeIsPositive={revenueGrowth >= 0}
        progressValue={Math.min(100, Math.abs(revenueGrowth) + 50)}
      />
      
      {/* Total Reservations Card */}
      <ContactOverviewCard
        title={t("totalReservations")}
        value={totalReservations}
        icon={<Calendar className="h-5 w-5" />}
        iconBgClass="text-blue-500 bg-blue-50 dark:bg-blue-900/20"
        details={[
          { label: t("active"), value: activeReservations },
          { label: t("completed"), value: totalReservations - activeReservations },
          { label: t("avgValue"), value: `$${Math.round(totalRevenue / totalReservations).toLocaleString()}` }
        ]}
      />
      
      {/* Locations Card */}
      <ContactOverviewCard
        title={t("preferredLocations")}
        value={locationData.length}
        icon={<MapPin className="h-5 w-5" />}
        iconBgClass="text-purple-500 bg-purple-50 dark:bg-purple-900/20"
        topItems={[
          { label: locationData[0]?.name || '', value: `${locationData[0]?.percentage || 0}%` },
          { label: locationData[1]?.name || '', value: `${locationData[1]?.percentage || 0}%` }
        ]}
      />
      
      {/* Ad Types Card */}
      <ContactOverviewCard
        title={t("adTypes")}
        value={adTypeData.length}
        icon={<Eye className="h-5 w-5" />}
        iconBgClass="text-green-500 bg-green-50 dark:bg-green-900/20"
        topItems={[
          { label: adTypeData[0]?.type || '', value: `${adTypeData[0]?.percentage || 0}%` },
          { label: adTypeData[1]?.type || '', value: `${adTypeData[1]?.percentage || 0}%` }
        ]}
      />
    </div>
  );
};

export default ContactStatisticsOverview;

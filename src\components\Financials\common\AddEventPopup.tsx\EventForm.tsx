import React, { useEffect, useState } from "react";
import { FaPlus, FaTrash } from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import { addDays, addWeeks, addMonths, format , addYears} from "date-fns";
import { RecurringDateRangeSelector } from "./RecurringDateRanges";
import { toast } from "react-toastify"; 

interface RecurringEvent {
  title: string;
  amount: number;
  dueDate: string;
  notificationDate: string;
}
interface EventFormProps {
  title: string;
  setTitle: (value: string) => void;
  amount: number;
  setAmount: (value: number) => void;
  dueDate: string;
  setDueDate: (value: string) => void;
  category: "income" | "expense";
  setCategory: (value: "income" | "expense") => void;
  type: string;
  setType: (value: string) => void;
  status: "completed" | "pending" | "cancelled" | "upcoming" | "overdue";
  setStatus: (value: "completed" | "pending" | "cancelled" | "upcoming" | "overdue") => void;
  isRecursive: boolean;
  setIsRecursive: (value: boolean) => void;
  recurrenceRange: string;
  setRecurrenceRange: (value: string) => void;
  recurrenceCount: number;
  setRecurrenceCount: (value: number) => void;
  types: { [key: string]: string[] };
  handleAddType: () => void;
  notificationDate: string;
  setNotificationDate: (value: string) => void;
  groupTypes: { [key: string]: { id: string; name: string }[] };
  typeId: string;
  setTypeId: (value: string) => void;
}

const EventForm: React.FC<EventFormProps> = ({
  title,
  setTitle,
  amount,
  setAmount,
  dueDate,
  setDueDate,
  category,
  setCategory,
  type,
  setType,
  status,
  setStatus,
  isRecursive,
  setIsRecursive,
  recurrenceRange,
  setRecurrenceRange,
  recurrenceCount,
  setRecurrenceCount,
  types,
  handleAddType,
  notificationDate,
  setNotificationDate,
  groupTypes,
  typeId,
  setTypeId,
}) => {
  const { t, language } = useLanguage();
  const locale = language === "ar" ? arSA : enUS;
  const [startDate, setStartDate] = useState<Date | null>(new Date());
  const [recurringEvents, setRecurringEvents] = useState<RecurringEvent[]>([]);
    const [count, setCount] = useState<number>(1);
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Allow only digits and remove any non-digit characters
    const numericValue = value.replace(/\D/g, '');

    // Convert to number and ensure it's a valid integer
    const parsedValue = parseInt(numericValue) || 0;

    // Set the amount as number
    setAmount(parsedValue);
  };

  useEffect(() => {
    if (isRecursive && startDate && recurrenceRange) {
      const events: RecurringEvent[] = [];
      let currentDate = new Date(startDate);

      let intervalFunction = (date: Date, amount: number) => date;
      
      switch (recurrenceRange) {
        case "everyMonth":
          intervalFunction = (date, i) => addMonths(date, i);
          break;
        case "every7Days":
          intervalFunction = (date, i) => addDays(date, i * 7);
          break;
        case "every30Days":
          intervalFunction = (date, i) => addDays(date, i * 30);
          break;
        case "every90Days":
          intervalFunction = (date, i) => addDays(date, i * 90);
          break;
        case "every3Months":
          intervalFunction = (date, i) => addMonths(date, i * 3);
          break;
        case "everyQuarter":
          intervalFunction = (date, i) => addMonths(date, i * 3);
          break;
        case "every6Months":
          intervalFunction = (date, i) => addMonths(date, i * 6);
          break;
        case "everyYear":
          intervalFunction = (date, i) => addYears(date, i);
          break;
      }

      for (let i = 0; i < count; i++) {
        const newDate = intervalFunction(new Date(startDate), i);
        events.push({
          title,
          amount,
          dueDate: newDate.toISOString(),
          notificationDate: newDate.toISOString(),
        });
      }
      
      setRecurringEvents(events);
      setRecurrenceCount(count);
      if (events.length > 0) {
        setDueDate(events[0].dueDate);
      }
    } else {
      setRecurringEvents([]);
    }
  }, [isRecursive, startDate, recurrenceRange, title, amount , count ]);

  const handleRecurringChange = (index: number, field: keyof RecurringEvent, value: string | number) => {
    const updated = [...recurringEvents];

    if (field === "amount") {
      // Handle amount field with integer validation
      const numericValue = String(value).replace(/\D/g, '');
      const parsedValue = parseInt(numericValue) || 0;
      updated[index] = {
        ...updated[index],
        [field]: parsedValue
      };
    } else {
      updated[index] = {
        ...updated[index],
        [field]: value
      };
    }

    setRecurringEvents(updated);

    if (index === 0) {
      if (field === "title") setTitle(value as string);
      if (field === "amount") setAmount(field === "amount" ? parseInt(String(value).replace(/\D/g, '')) || 0 : value as number);
      if (field === "dueDate") setDueDate(value as string);
    }
  };

  const handleRemoveEvent = (index: number) => {
    const updatedEvents = [...recurringEvents];
    updatedEvents.splice(index, 1);
    setRecurringEvents(updatedEvents);
    setRecurrenceCount(updatedEvents.length);
  };

  const handleCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Math.min(50, Math.max(1, parseInt(e.target.value) || 1));
    setCount(value);
    setRecurrenceCount(value);
  };

  const handleRecursiveChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { checked } = e.target;
    
    // Check if required fields are filled before allowing recursive
    if (checked && (!title || !amount || !dueDate || !type)) {
      toast.error(t("Please fill all fields (Title, Amount, Due Date, and Type) before enabling recursive"), {
        position: language === "ar" ? "top-left" : "top-right",
        rtl: language === "ar"
      });
      return;
    }
    
    setIsRecursive(checked);
  };

  return (
    <div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Title")}</label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Amount")}</label>
        <div className="relative mt-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <span className="text-gray-500 dark:text-gray-400 text-sm font-medium">
              {language === 'ar' ? 'ج.م' : 'EGP'}
            </span>
          </div>
          <input
            type="text"
            value={amount ? amount.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US') : ''}
            onChange={handleAmountChange}
            className="block w-full pl-12 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
            placeholder={t("Enter amount (integers only)")}
            inputMode="numeric"
            pattern="[0-9]*"
          />
        </div>
        {amount > 0 && (
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {t("Amount")}: {amount ? amount.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US') : '0'} {language === 'ar' ? 'جنيه مصري' : 'Egyptian Pounds'}
          </p>
        )}
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Due Date")}</label>
        <DatePicker
            selected={dueDate ? new Date(dueDate) : null}
            onChange={(date: Date | null) => setDueDate(date ? date.toISOString() : "")}
            className="mt-1 block w-[150%] px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
            dateFormat="yyyy-MM-dd"
            locale={locale}
            placeholderText={t("Select date")}
          />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Category")}</label>
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value as "income" | "expense")}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
        >
          <option value="income">{t("Income")}</option>
          <option value="expense">{t("Expense")}</option>
        </select>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Status")}</label>
        <select
          value={status}
          onChange={(e) => setStatus(e.target.value as any)}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 dark:text-gray-200"
        >
          <option value="completed">{t("Completed")}</option>
          <option value="pending">{t("Pending")}</option>
          <option value="cancelled">{t("Cancelled")}</option>
          <option value="upcoming">{t("Upcoming")}</option>
          <option value="overdue">{t("Overdue")}</option>
        </select>
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Type")}</label>
        <div className="flex items-center gap-2">
          <select
            value={type}
            onChange={(e) => {
              const selectedTypeName = e.target.value;
              setType(selectedTypeName);

              // Find the corresponding type ID
              const selectedTypeObj = groupTypes[category]?.find(
                (typeObj) => typeObj.name === selectedTypeName
              );
              if (selectedTypeObj) {
                setTypeId(selectedTypeObj.id);
              } else {
                setTypeId("");
              }
            }}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
          >
            <option value="">{t("Select Type")}</option>
            {types[category]?.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
          <button
            type="button"
            onClick={handleAddType}
            className="mt-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center gap-2"
          >
            <FaPlus />
          </button>
        </div>
      </div>
      <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Notification Date")}</label>
          <DatePicker
        selected={notificationDate ? new Date(notificationDate) : null}
        onChange={(date: Date | null) => setNotificationDate(date ? date.toISOString() : "")}
        className="mt-1 w-[100%] md:w-[120%] block px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
        dateFormat="yyyy-MM-dd"
        locale={locale}
        placeholderText={t("Select date")}
          />
        </div>
      <div className="flex flex-col items-start justify-between w-full px-4 py-2 rounded-lg bg-white dark:bg-gray-700">
        <div className="flex items-center justify-between w-full">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t("Recursive")}</label>
          <label className="relative inline-flex items-center cursor-pointer">
        <input
          type="checkbox"
          checked={isRecursive}
          onChange={handleRecursiveChange}
          className="sr-only peer"
        />
        <div className="w-14 h-8 bg-gray-200 peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-600 peer-checked:bg-blue-600"></div>
        <div className="absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition-transform peer-checked:translate-x-6"></div>
          </label>
        </div>
        <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          {t("Please fill all fields (Title, Amount, Due Date, and Type) before enabling recursive")}
        </p>
      </div>
    </div>


      {isRecursive && (
        <>
          <hr className="my-4 border-gray-300 dark:border-gray-700" />
          <div className="mt-4">
            <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">{t("Recurrence Settings")}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <RecurringDateRangeSelector 
                onChange={setRecurrenceRange}
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("Number of Events")} (max 50)
                </label>
                <input
                  type="number"
                  min="1"
                  max="50"
                  value={recurrenceCount}
                  onChange={handleCountChange}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 dark:text-gray-200"
                />
              </div>
            </div>

            <div className="mt-6">
              <h4 className="text-md font-semibold text-gray-700 dark:text-gray-200 mb-2">
                {t("Generated Events")} ({recurringEvents.length})
              </h4>
              <div className="space-y-3">
              {isRecursive && recurringEvents.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-2">{t("Recurring Events")}</h3>
                  <div className="grid grid-cols-4 font-semibold text-sm text-gray-600 dark:text-gray-300 border-b pb-1">
                    <span>{t("Title")}</span>
                    <span>{t("Due Date")}</span>
                    <span>{t("Notification Date")}</span>
                    <span>{t("Amount")}</span>
                  </div>
                  {recurringEvents.map((event, index) => (
                    <div key={index} className="grid grid-cols-4 items-center border-b py-2 text-sm">
                      <input
                        type="text"
                        value={event.title}
                        onChange={(e) => handleRecurringChange(index, "title", e.target.value)}
                        className="px-2 py-1 bg-white dark:bg-gray-800 border rounded"
                      />
                      <input
                        type="date"
                        value={event.dueDate.slice(0, 10)}
                        onChange={(e) => handleRecurringChange(index, "dueDate", e.target.value)}
                        className="px-2 py-1 bg-white dark:bg-gray-800 border rounded"
                      />
                      <input
                        type="date"
                        value={event.notificationDate.slice(0, 10)}
                        onChange={(e) => handleRecurringChange(index, "notificationDate", e.target.value)}
                        className="px-2 py-1 bg-white dark:bg-gray-800 border rounded"
                      />
                      <input
                        type="text"
                        value={event.amount ? event.amount.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US') : ''}
                        onChange={(e) => handleRecurringChange(index, "amount", e.target.value)}
                        className="px-2 py-1 bg-white dark:bg-gray-800 border rounded"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        placeholder="0"
                      />
                    </div>
                  ))}
                </div>
              )}

              </div>
            </div>
          </div>
          <hr className="my-4 border-gray-300 dark:border-gray-700" />
        </>
      )}
    </div>
  );
};

export default EventForm;
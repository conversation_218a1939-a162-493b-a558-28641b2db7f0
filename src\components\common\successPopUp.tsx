import { FC, useEffect } from 'react';
import { CheckCircleIcon, XMarkIcon } from '@heroicons/react/24/solid';

interface SuccessPopupProps {
  message: string;
  onClose: () => void;
  duration?: number;
}

export const SuccessPopup: FC<SuccessPopupProps> = ({ 
  message, 
  onClose, 
  duration = 3000 
}) => {
  useEffect(() => {
    const timer = setTimeout(onClose, duration);
    return () => clearTimeout(timer);
  }, [duration, onClose]);

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="flex items-center bg-green-50 border-l-4 border-green-500 rounded-lg p-4 shadow-lg animate-fade-in-up">
        <div className="flex-shrink-0">
          <CheckCircleIcon className="h-5 w-5 text-green-500" />
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-green-800">{message}</p>
        </div>
        <button
          onClick={onClose}
          className="ml-auto -mx-1.5 -my-1.5 p-1.5 rounded-lg inline-flex items-center justify-center hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-400"
        >
          <XMarkIcon className="h-5 w-5 text-green-500" />
        </button>
      </div>
    </div>
  );
};
import React from "react";

interface HistorySectionProps {
  history: any[];
  onExpand?: () => void; // Optional for non-expanded view
}

const HistorySection: React.FC<HistorySectionProps> = ({ history, onExpand }) => {
  return (
    <div className="pl-6">
      <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">Editing History</h3>
      {history.slice(0, 3).map((entry, index) => (
        <div key={index} className="flex justify-between items-center text-gray-600 dark:text-gray-400">
          <span>
            <strong>{entry.editedBy}</strong> - {new Date(entry.editedAt).toLocaleString()}
          </span>
          <span
  className="text-blue-500 hover:text-blue-600 cursor-pointer underline"
  onClick={() => alert("View history details")}
>
  View
</span>

        </div>
      ))}
      {history.length > 3 && onExpand && (
        <button
          className="w-full mt-4 text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 flex items-center justify-center gap-2"
          onClick={onExpand}
        >
          <span>View All Editing History</span>
        </button>
      )}
    </div>
  );
};

export default HistorySection;
{"name": "free-nextjs-admin-dashboard", "version": "1.3.5", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.3", "@react-spring/web": "^9.7.5", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.71.5", "@vitalets/google-translate-api": "^9.2.1", "axios": "^1.8.4", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "flatpickr": "^4.6.13", "framer-motion": "^12.0.6", "fs": "^0.0.1-security", "html2canvas": "^1.4.1", "i18next": "^24.2.1", "jspdf": "^3.0.0", "jspdf-autotable": "^5.0.2", "jsvectormap": "^1.6.0", "lucide-react": "^0.473.0", "next": "^14.2.4", "next-auth": "^4.24.11", "next-i18next": "^15.4.1", "next-intl": "^3.26.3", "papaparse": "^5.5.3", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.2.1", "react-day-picker": "^9.6.2", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "react-router-dom": "^7.3.0", "react-select": "^5.10.0", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "tailwind-merge": "^3.0.2", "tailwindcss-rtl": "^0.9.0", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "^5.7.3"}}
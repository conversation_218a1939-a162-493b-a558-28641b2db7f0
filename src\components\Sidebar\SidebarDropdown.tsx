import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import  useLanguage  from "@/hooks/useLanguage";

const SidebarDropdown = ({ item }: any) => {
  const pathname = usePathname();
  const { language, toggleLanguage, t } = useLanguage();

  return (
    <>
      <ul className="mb-5.5 mt-4 flex flex-col gap-2.5 pl-6">
        {item.map((item: any, index: number) => (
          <li key={index}>
            <Link
              href={item.route}
              className={`group relative flex items-center gap-2.5 rounded-md px-4 font-medium text-bodydark2 duration-300 ease-in-out hover:text-white ${
                pathname === item.route ? "text-white" : ""
              }`}
            >
              {t(item.label.toLowerCase())}
            </Link>
          </li>
        ))}
      </ul>
    </>
  );
};

export default SidebarDropdown;

import React, { useState, useMemo } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line,
} from "recharts";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";
import { useSpring, animated } from "@react-spring/web";
import useLanguage from "@/hooks/useLanguage";
import {
  formatCurrency,
  formatCurrencyCompact,
  formatNumber,
  processCategoryData,
  getChartConfig,
  filterValidEvents,
  CHART_COLORS,
} from "@/utils/analyticsUtils";
import { LoadingComp } from "@/components/common/Loading";

interface ExpenseSummaryProps {
  expenses: EventDetails[];
  loading?: boolean;
}

const ExpenseSummary: React.FC<ExpenseSummaryProps> = ({
  expenses,
  loading = false,
}) => {
  const { t, language } = useLanguage();
  const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(null);
  const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  // Process and validate expenses
  const validExpenses = useMemo(() => filterValidEvents(expenses), [expenses]);

  const filteredExpenses = useMemo(() => {
    return validExpenses.filter((expense) => {
      const expenseDate = new Date(expense.dueDate);
      if (selectedStartDate && selectedEndDate) {
        return (
          expenseDate >= selectedStartDate && expenseDate <= selectedEndDate
        );
      }
      if (selectedStartDate) {
        return expenseDate >= selectedStartDate;
      }
      if (selectedEndDate) {
        return expenseDate <= selectedEndDate;
      }
      return true;
    });
  }, [validExpenses, selectedStartDate, selectedEndDate]);

  const totalExpense = useMemo(() => {
    return filteredExpenses.reduce(
      (sum, expense) => sum + Number(expense.amount),
      0,
    );
  }, [filteredExpenses]);

  // Prepare data for charts - Monthly expense distribution
  const expenseDistributionData = useMemo(() => {
    const monthlyData: { [key: string]: number } = {};

    filteredExpenses.forEach((expense) => {
      const date = new Date(expense.dueDate);
      const monthKey = date.toLocaleDateString(
        language === "ar" ? "ar-EG" : "en-US",
        {
          month: "short",
          year: "numeric",
        },
      );
      monthlyData[monthKey] =
        (monthlyData[monthKey] || 0) + Number(expense.amount);
    });

    return Object.entries(monthlyData)
      .sort(([a], [b]) => {
        // Sort by date
        const dateA = new Date(a);
        const dateB = new Date(b);
        return dateA.getTime() - dateB.getTime();
      })
      .map(([month, amount]) => ({
        name: month,
        amount,
      }));
  }, [filteredExpenses, language]);

  // Process category data
  const categoryData = useMemo(() => {
    return processCategoryData(filteredExpenses, "expense");
  }, [filteredExpenses]);

  // Monthly expense data for trend chart
  const monthlyExpenseData = useMemo(() => {
    const monthlyData: { [key: string]: number } = {};

    filteredExpenses.forEach((expense) => {
      const date = new Date(expense.dueDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
      monthlyData[monthKey] =
        (monthlyData[monthKey] || 0) + Number(expense.amount);
    });

    return Object.entries(monthlyData)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([month, amount]) => {
        const [year, monthNum] = month.split("-");
        const date = new Date(parseInt(year), parseInt(monthNum) - 1);
        return {
          month: date.toLocaleDateString(
            language === "ar" ? "ar-EG" : "en-US",
            {
              month: "short",
              year: "numeric",
            },
          ),
          amount,
        };
      });
  }, [filteredExpenses, language]);

  // Cumulative expense data
  const cumulativeExpenseData = useMemo(() => {
    return filteredExpenses
      .sort(
        (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime(),
      )
      .reduce(
        (acc, expense, index) => {
          const cumulativeAmount =
            index === 0
              ? Number(expense.amount)
              : acc[index - 1].cumulativeAmount + Number(expense.amount);
          acc.push({
            date: new Date(expense.dueDate).toLocaleDateString(
              language === "ar" ? "ar-EG" : "en-US",
            ),
            cumulativeAmount,
          });
          return acc;
        },
        [] as { date: string; cumulativeAmount: number }[],
      );
  }, [filteredExpenses, language]);

  // Animation for total expense
  const { number: animatedTotalExpense } = useSpring({
    from: { number: 0 },
    number: totalExpense,
    delay: 200,
    config: { mass: 1, tension: 170, friction: 26 },
  });

  // Chart configuration
  const chartConfig = getChartConfig(language);

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-white p-4 shadow-lg dark:border-gray-700 dark:bg-gray-800">
          <p className="mb-2 font-semibold text-gray-900 dark:text-white">
            {label}
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {t(entry.name) || entry.name}: {formatCurrency(entry.value, language)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Show loading state while data is being fetched
  if (loading) {
    return (
      <div className="rounded-lg border bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="py-12 text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20">
            <LoadingComp />
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
            {t("Loading Expense Data")}
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {t("Please wait while we fetch your expense analytics")}
          </p>
        </div>
      </div>
    );
  }

  // Show no data message only when not loading and no expenses exist
  if (!expenses || expenses.length === 0) {
    return (
      <div className="rounded-lg border bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="py-12 text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20">
            <div className="h-8 w-8 rounded bg-red-500"></div>
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
            {t("No Expense Data")}
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {t("Add expense events to see analytics")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
      <div className="border-b p-6 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="space-y-2">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t("Total Expense")}
              </p>
              <div className="space-y-1">
                <animated.p
                  className="break-words text-2xl font-bold text-red-600 dark:text-red-400"
                  title={formatCurrency(totalExpense, language)}
                >
                  {animatedTotalExpense.to((n) =>
                    formatCurrencyCompact(n, language),
                  )}
                </animated.p>
                <p className="text-xs text-red-600/70 dark:text-red-400/70">
                  {formatCurrency(totalExpense, language)}
                </p>
              </div>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>
                {filteredExpenses.length} {t("transactions")}
              </p>
              <p>
                {t("Average")}:{" "}
                {formatCurrencyCompact(
                  totalExpense / (filteredExpenses.length || 1),
                  language,
                )}
              </p>
            </div>
          </div>
          <button
            className="flex items-center space-x-2 rounded-lg bg-gray-100 px-4 py-2 transition-colors hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {isExpanded ? t("Hide Details") : t("Show Details")}
            </span>
            {isExpanded ? (
              <FaChevronUp size={16} />
            ) : (
              <FaChevronDown size={16} />
            )}
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-6">
          {/* Monthly Expense Distribution */}
          <div className="mb-8">
            <h3 className="mb-4 text-lg font-semibold dark:text-white">
              {t("Monthly Expense Distribution")}
            </h3>
            <div className="h-80 w-full overflow-hidden">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={expenseDistributionData}
                  margin={{ top: 20, right: 30, left: 40, bottom: 60 }}
                >
                  <defs>
                    <linearGradient
                      id="expenseBarGradient"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop
                        offset="5%"
                        stopColor={CHART_COLORS.expense}
                        stopOpacity={0.8}
                      />
                      <stop
                        offset="95%"
                        stopColor={CHART_COLORS.expense}
                        stopOpacity={0.3}
                      />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis
                    dataKey="name"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 11, fill: "#6b7280" }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                    interval={0}
                  />
                  <YAxis
                    tickFormatter={(value) => formatNumber(value, language)}
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 11, fill: "#6b7280" }}
                    width={60}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar
                    dataKey="amount"
                    name={t("Expense")}
                    fill="url(#expenseBarGradient)"
                    radius={[4, 4, 0, 0]}
                    maxBarSize={60}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Expense Categories */}
            <div>
              <h3 className="mb-4 text-lg font-semibold dark:text-white">
                {t("Expense Categories")}
              </h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={categoryData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      label={({ name, percent }) =>
                        `${t(name) || name} ${(percent * 100).toFixed(0)}%`
                      }
                      labelLine={false}
                    >
                      {categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) =>
                        formatCurrency(value as number, language)
                      }
                    />
                    <Legend
                      formatter={(value) => t(value) || value}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Monthly Trend */}
            <div>
              <h3 className="mb-4 text-lg font-semibold dark:text-white">
                {t("Monthly Expense Trend")}
              </h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyExpenseData}
                    margin={chartConfig.margin}
                  >
                    <CartesianGrid {...chartConfig.cartesianGrid} />
                    <XAxis dataKey="month" {...chartConfig.xAxis} />
                    <YAxis
                      tickFormatter={(value) => formatNumber(value, language)}
                      {...chartConfig.yAxis}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Line
                      type="monotone"
                      dataKey="amount"
                      name={t("Amount")}
                      stroke={CHART_COLORS.expense}
                      strokeWidth={3}
                      dot={{ fill: CHART_COLORS.expense, strokeWidth: 2, r: 6 }}
                      activeDot={{
                        r: 8,
                        stroke: CHART_COLORS.expense,
                        strokeWidth: 2,
                      }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Cumulative Expense */}
          {cumulativeExpenseData.length > 0 && (
            <div className="mt-8">
              <h3 className="mb-4 text-lg font-semibold dark:text-white">
                {t("Cumulative Expense Over Time")}
              </h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={cumulativeExpenseData}
                    margin={chartConfig.margin}
                  >
                    <defs>
                      <linearGradient
                        id="cumulativeGradient"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor={CHART_COLORS.expense}
                          stopOpacity={0.6}
                        />
                        <stop
                          offset="95%"
                          stopColor={CHART_COLORS.expense}
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                    </defs>
                    <CartesianGrid {...chartConfig.cartesianGrid} />
                    <XAxis dataKey="date" {...chartConfig.xAxis} />
                    <YAxis
                      tickFormatter={(value) => formatNumber(value, language)}
                      {...chartConfig.yAxis}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Line
                      type="monotone"
                      dataKey="cumulativeAmount"
                      name={t("Cumulative Amount")}
                      stroke={CHART_COLORS.expense}
                      strokeWidth={3}
                      fill="url(#cumulativeGradient)"
                      dot={{ fill: CHART_COLORS.expense, strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}

          {/* Summary Statistics */}
          <div className="mt-8 border-t pt-6 dark:border-gray-700">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="text-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("Highest Month")}
                </p>
                <p className="text-lg font-semibold text-red-600 dark:text-red-400">
                  {expenseDistributionData.length > 0
                    ? expenseDistributionData.reduce(
                        (highest, current) =>
                          current.amount > highest.amount ? current : highest,
                        expenseDistributionData[0],
                      )?.name || "-"
                    : "-"}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("Average Monthly")}
                </p>
                <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                  {expenseDistributionData.length > 0
                    ? formatCurrency(
                        expenseDistributionData.reduce(
                          (sum, item) => sum + item.amount,
                          0,
                        ) / expenseDistributionData.length,
                        language,
                      )
                    : formatCurrency(0, language)}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("Categories")}
                </p>
                <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                  {categoryData.length}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("This Month")}
                </p>
                <p className="text-lg font-semibold text-purple-600 dark:text-purple-400">
                  {monthlyExpenseData.length > 0
                    ? formatCurrency(
                        monthlyExpenseData[monthlyExpenseData.length - 1]
                          ?.amount || 0,
                        language,
                      )
                    : formatCurrency(0, language)}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExpenseSummary;

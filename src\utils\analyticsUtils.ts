import { EventDetails } from "@/lib/interfaces/finaces";

export interface AnalyticsDataPoint {
  name: string;
  income: number;
  expense: number;
  actualIncome?: number;
  actualExpense?: number;
  expectedIncome?: number;
  expectedExpense?: number;
  difference: number;
  month: number;
  year: number;
  isUpcoming?: boolean;
  color?: string;
}

export interface MonthlyData {
  [key: string]: {
    income: number;
    expense: number;
    expectedIncome?: number;
    expectedExpense?: number;
    count: number;
  };
}

export interface CategoryData {
  name: string;
  value: number;
  color: string;
}

export interface TrendData {
  period: string;
  value: number;
  growth: number;
  trend: 'up' | 'down' | 'stable';
}

// Color palettes for charts
export const CHART_COLORS = {
  income: '#10b981', // Green
  expense: '#ef4444', // Red
  profit: '#3b82f6', // Blue
  neutral: '#6b7280', // Gray
  categories: [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', 
    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316',
    '#ec4899', '#14b8a6', '#f43f5e', '#8b5cf6'
  ]
};

// Number formatting utilities
export const formatNumber = (value: number, language: string = 'en'): string => {
  if (isNaN(value) || value === null || value === undefined) return '0';
  
  const absValue = Math.abs(value);
  const sign = value < 0 ? '-' : '';
  
  let formattedValue: string;
  
  if (absValue >= 1_000_000_000) {
    formattedValue = `${(absValue / 1_000_000_000).toFixed(1)}B`;
  } else if (absValue >= 1_000_000) {
    formattedValue = `${(absValue / 1_000_000).toFixed(1)}M`;
  } else if (absValue >= 1_000) {
    formattedValue = `${(absValue / 1_000).toFixed(1)}K`;
  } else {
    formattedValue = Math.round(absValue).toString();
  }
  
  if (language === 'ar') {
    formattedValue = convertToArabicNumerals(formattedValue);
  }
  
  return sign + formattedValue;
};

export const formatCurrency = (value: number, language: string = 'en', currency: string = 'EGP'): string => {
  if (isNaN(value) || value === null || value === undefined) return '0';

  try {
    if (language === 'ar') {
      return value.toLocaleString('ar-EG', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    }
    return value.toLocaleString('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  } catch (error) {
    // Fallback formatting
    const formatted = Math.round(value).toLocaleString();
    return language === 'ar' ? convertToArabicNumerals(formatted) : formatted;
  }
};

// Smart currency formatting for analytics cards that handles large numbers
export const formatCurrencyCompact = (value: number, language: string = 'en', currency: string = 'EGP'): string => {
  if (isNaN(value) || value === null || value === undefined) return '0';

  const absValue = Math.abs(value);
  const isNegative = value < 0;

  let formattedValue: string;
  let suffix = '';

  if (absValue >= 1_000_000_000) {
    formattedValue = (absValue / 1_000_000_000).toFixed(1);
    suffix = language === 'ar' ? 'مليار' : 'B';
  } else if (absValue >= 1_000_000) {
    formattedValue = (absValue / 1_000_000).toFixed(1);
    suffix = language === 'ar' ? 'مليون' : 'M';
  } else if (absValue >= 1_000) {
    formattedValue = (absValue / 1_000).toFixed(1);
    suffix = language === 'ar' ? 'ألف' : 'K';
  } else {
    formattedValue = absValue.toFixed(0);
  }

  // Remove unnecessary .0
  if (formattedValue.endsWith('.0')) {
    formattedValue = formattedValue.slice(0, -2);
  }

  if (language === 'ar') {
    formattedValue = convertToArabicNumerals(formattedValue);
    const currencySymbol = currency === 'EGP' ? 'ج.م' : currency;
    return `${isNegative ? '-' : ''}${formattedValue}${suffix ? ' ' + suffix : ''} ${currencySymbol}`;
  } else {
    const currencySymbol = currency === 'EGP' ? 'EGP' : currency;
    return `${isNegative ? '-' : ''}${currencySymbol} ${formattedValue}${suffix}`;
  }
};

export const convertToArabicNumerals = (value: string | number): string => {
  const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  return value
    .toString()
    .replace(/[0-9]/g, (digit) => arabicDigits[parseInt(digit)]);
};

// Date utilities
export const getMonthName = (monthIndex: number, language: string = 'en'): string => {
  const date = new Date(2000, monthIndex, 1);
  return date.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', { month: 'short' });
};

export const getMonthYear = (date: Date, language: string = 'en'): string => {
  const month = getMonthName(date.getMonth(), language);
  const year = date.getFullYear();
  return `${month} ${language === 'ar' ? convertToArabicNumerals(year) : year}`;
};

// Data processing utilities
export const processEventsToMonthlyData = (events: EventDetails[], language: string = 'en'): AnalyticsDataPoint[] => {
  if (!events || events.length === 0) return [];

  const monthlyData: MonthlyData = {};
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  // Initialize last 12 months
  for (let i = 11; i >= 0; i--) {
    const date = new Date(currentYear, currentMonth - i, 1);
    const key = `${date.getFullYear()}-${date.getMonth()}`;
    monthlyData[key] = { income: 0, expense: 0, count: 0 };
  }

  // Process events
  events.forEach(event => {
    if (!event.dueDate || !event.amount) return;

    const eventDate = new Date(event.dueDate);
    if (isNaN(eventDate.getTime())) return;

    const key = `${eventDate.getFullYear()}-${eventDate.getMonth()}`;

    if (!monthlyData[key]) {
      monthlyData[key] = { income: 0, expense: 0, count: 0 };
    }

    const amount = Number(event.amount) || 0;

    // Handle different statuses - non-completed events are treated as expected/forecasted
    const isCompleted = event.status === 'completed';
    const actualAmount = isCompleted ? amount : 0;
    const expectedAmount = !isCompleted ? amount : 0;

    if (event.category === 'income') {
      monthlyData[key].income += actualAmount;
      // Add expected income to a separate field if needed for visualization
      if (!isCompleted) {
        monthlyData[key].expectedIncome = (monthlyData[key].expectedIncome || 0) + expectedAmount;
      }
    } else if (event.category === 'expense') {
      monthlyData[key].expense += actualAmount;
      // Add expected expense to a separate field if needed for visualization
      if (!isCompleted) {
        monthlyData[key].expectedExpense = (monthlyData[key].expectedExpense || 0) + expectedAmount;
      }
    }
    monthlyData[key].count++;
  });

  // Convert to array format
  return Object.entries(monthlyData).map(([key, data]) => {
    const [year, month] = key.split('-').map(Number);
    const date = new Date(year, month, 1);
    const isUpcoming = date > currentDate;

    // Include expected amounts in the totals for display
    const totalIncome = data.income + (data.expectedIncome || 0);
    const totalExpense = data.expense + (data.expectedExpense || 0);

    return {
      name: getMonthYear(date, language),
      income: totalIncome,
      expense: totalExpense,
      actualIncome: data.income,
      actualExpense: data.expense,
      expectedIncome: data.expectedIncome || 0,
      expectedExpense: data.expectedExpense || 0,
      difference: totalIncome - totalExpense,
      month,
      year,
      isUpcoming,
      color: totalIncome - totalExpense >= 0 ? CHART_COLORS.income : CHART_COLORS.expense
    };
  }).sort((a, b) => {
    if (a.year !== b.year) return a.year - b.year;
    return a.month - b.month;
  });
};

export const processCategoryData = (events: EventDetails[], category: 'income' | 'expense'): CategoryData[] => {
  if (!events || events.length === 0) return [];

  const categoryMap: { [key: string]: number } = {};

  events
    .filter(event => event.category === category && event.amount)
    .forEach(event => {
      const type = event.type || 'Other';
      categoryMap[type] = (categoryMap[type] || 0) + Number(event.amount);
    });

  return Object.entries(categoryMap)
    .map(([name, value], index) => ({
      name,
      value,
      color: CHART_COLORS.categories[index % CHART_COLORS.categories.length]
    }))
    .sort((a, b) => b.value - a.value);
};

export const calculateTrend = (current: number, previous: number): TrendData['trend'] => {
  if (previous === 0) return current > 0 ? 'up' : 'stable';
  const change = ((current - previous) / previous) * 100;
  if (Math.abs(change) < 5) return 'stable';
  return change > 0 ? 'up' : 'down';
};

export const calculateGrowthRate = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

// Validation utilities
export const validateEventData = (event: EventDetails): boolean => {
  return !!(
    event &&
    event.dueDate &&
    !isNaN(new Date(event.dueDate).getTime()) &&
    event.amount !== undefined &&
    event.amount !== null &&
    !isNaN(Number(event.amount)) &&
    (event.category === 'income' || event.category === 'expense')
  );
};

export const filterValidEvents = (events: EventDetails[]): EventDetails[] => {
  return events.filter(validateEventData);
};

// Chart configuration utilities
export const getChartConfig = (language: string = 'en') => ({
  margin: {
    top: 20,
    right: 30,
    left: language === 'ar' ? 40 : 20,
    bottom: language === 'ar' ? 80 : 60
  },
  cartesianGrid: { strokeDasharray: '3 3', opacity: 0.3 },
  xAxis: {
    axisLine: false,
    tickLine: false,
    tick: {
      fontSize: 12,
      fill: '#6b7280',
      textAnchor: language === 'ar' ? 'start' : 'middle',
      dy: language === 'ar' ? 10 : 0
    },
    angle: language === 'ar' ? -45 : 0,
    textAnchor: language === 'ar' ? 'start' : 'middle',
    height: language === 'ar' ? 80 : 60
  },
  yAxis: {
    axisLine: false,
    tickLine: false,
    tick: {
      fontSize: 12,
      fill: '#6b7280',
      dx: language === 'ar' ? -10 : 0
    }
  },
  tooltip: {
    contentStyle: {
      backgroundColor: '#ffffff',
      border: '1px solid #e5e7eb',
      borderRadius: '8px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      textAlign: language === 'ar' ? 'right' : 'left',
      direction: language === 'ar' ? 'rtl' : 'ltr'
    }
  },
  legend: {
    wrapperStyle: {
      paddingTop: '20px'
    }
  }
});

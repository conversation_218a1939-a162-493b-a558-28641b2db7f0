import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Loader2,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Building,
} from "lucide-react";

// Import the tab components
import ContractsTabComponent from "./Tabs/ContractsTab";
import ExpenseEventsTabComponent from "./Tabs/ExpenseEventsTab";
import ReservationsTabComponent from "./Tabs/ReservationsTab";
import IncomeEventsTabComponent from "./Tabs/IncomeEventsTab";
import useLanguage from "@/hooks/useLanguage";

export interface FinancialHistoryProps {
  locationId: string;
  isLoading?: boolean;
  onDataUpdate?: (type: 'incomes' | 'expenses' | 'reservations' | 'contracts', data: any[], isLoading: boolean) => void;
}

export const FinancialHistory: React.FC<FinancialHistoryProps> = ({
  locationId,
  isLoading = false,
  onDataUpdate,
}) => {
  const { t } = useLanguage();
  const [activeSection, setActiveSection] = useState<
    "reservations" | "income" | "contracts" | "expenses"
  >("reservations");

  // Display loading state if data is being fetched
  if (isLoading) {
    return (
      <div className="w-full max-w-full overflow-hidden">
        <Card className="w-full max-w-full overflow-hidden border-0 bg-gradient-to-br from-blue-50 to-indigo-100 shadow-lg dark:from-gray-800 dark:to-gray-900">
          <CardHeader className="px-6 py-6 sm:px-8">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-blue-500 p-2">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-xl font-bold text-transparent sm:text-2xl">
                  {t("financialHistory")}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 dark:text-gray-400">
                  {t("Loading financial data...")}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex items-center justify-center p-8 sm:p-12">
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t("Fetching your financial records...")}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-full overflow-hidden">
      <Card className="w-full max-w-full overflow-hidden border-0 bg-gradient-to-br from-white via-gray-50 to-blue-50 shadow-xl dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <CardHeader className="border-b border-gray-200 px-4 py-4 dark:border-gray-700 sm:px-6 sm:py-6">
          <div className="flex items-center gap-3">
            <div className="rounded-lg bg-blue-500 p-2">
              <DollarSign className="h-5 w-5 text-white sm:h-6 sm:w-6" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white sm:text-xl">
                {t("Financial History")}
              </CardTitle>
              <CardDescription className="text-sm text-gray-600 dark:text-gray-400">
                {t("Overview of financial records for this location")}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {/* Simple Tab Navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-0" aria-label="Tabs">
              <button
                onClick={() => setActiveSection("reservations")}
                className={`flex-1 border-b-2 px-4 py-3 text-sm font-medium transition-colors ${
                  activeSection === "reservations"
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{t("Reservations")}</span>
                </div>
              </button>
              <button
                onClick={() => setActiveSection("income")}
                className={`flex-1 border-b-2 px-4 py-3 text-sm font-medium transition-colors ${
                  activeSection === "income"
                    ? "border-green-500 text-green-600 dark:text-green-400"
                    : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span>{t("Income")}</span>
                </div>
              </button>
              <button
                onClick={() => setActiveSection("contracts")}
                className={`flex-1 border-b-2 px-4 py-3 text-sm font-medium transition-colors ${
                  activeSection === "contracts"
                    ? "border-red-500 text-red-600 dark:text-red-400"
                    : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <Building className="h-4 w-4" />
                  <span>{t("Contracts")}</span>
                </div>
              </button>
              <button
                onClick={() => setActiveSection("expenses")}
                className={`flex-1 border-b-2 px-4 py-3 text-sm font-medium transition-colors ${
                  activeSection === "expenses"
                    ? "border-orange-500 text-orange-600 dark:text-orange-400"
                    : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <TrendingDown className="h-4 w-4" />
                  <span>{t("Expenses")}</span>
                </div>
              </button>
            </nav>
          </div>

          {/* Content Area */}
          <div className="p-4 sm:p-6">
            {activeSection === "reservations" && (
              <div className="w-full overflow-hidden">
                <ReservationsTabComponent
                  locationId={locationId}
                  onDataUpdate={(data, isLoading) => onDataUpdate?.('reservations', data, isLoading)}
                />
              </div>
            )}
            {activeSection === "income" && (
              <div className="w-full overflow-hidden">
                <IncomeEventsTabComponent
                  locationId={locationId}
                  onDataUpdate={(data, isLoading) => onDataUpdate?.('incomes', data, isLoading)}
                />
              </div>
            )}
            {activeSection === "contracts" && (
              <div className="w-full overflow-hidden">
                <ContractsTabComponent
                  locationId={locationId}
                  onDataUpdate={(data, isLoading) => onDataUpdate?.('contracts', data, isLoading)}
                />
              </div>
            )}
            {activeSection === "expenses" && (
              <div className="w-full overflow-hidden">
                <ExpenseEventsTabComponent
                  locationId={locationId}
                  onDataUpdate={(data, isLoading) => onDataUpdate?.('expenses', data, isLoading)}
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FinancialHistory;

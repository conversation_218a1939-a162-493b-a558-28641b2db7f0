// hooks/useUserServices.ts
import { useSession , getSession } from "next-auth/react";
import { createApiClientWithSessionUpdate } from "@/lib/api/apiClient";
import createUserServices from "@/lib/users";
import { authService } from "@/lib/api/auth";

export function useUserServices() {
  const { data: session, update } = useSession();

  
  const apiClient = createApiClientWithSessionUpdate(async () => {
  });
  return createUserServices(apiClient);
}
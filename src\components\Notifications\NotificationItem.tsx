import React, { useState } from "react";
import { Notification } from "@/lib/types/notification";
import { formatDistanceToNow } from "date-fns";
import useLanguage from "@/hooks/useLanguage";
import { ar } from "date-fns/locale";
import EventStatusModal from "./EventStatusModal";

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onMarkAsUnread?: (id: string) => void;
  onDelete?: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onMarkAsUnread,
  onDelete,
}) => {
  const { t, language } = useLanguage();
  const isRTL = language === "ar";
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Check if this is an event-related notification
  const isEventNotification =
    notification.category === "income_due_date" ||
    notification.category === "expense_due_date";

  // Get the priority color
  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-amber-500";
      case "low":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Format the date to a relative time
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: language === "ar" ? ar : undefined,
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  // Handle marking notification as read
  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification.id);
  };

  // Handle marking notification as unread
  const handleMarkAsUnread = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsUnread) onMarkAsUnread(notification.id);
  };

  // Handle deleting notification
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) onDelete(notification.id);
  };

  // Handle event status update
  const handleEventClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEventNotification) {
      setIsModalOpen(true);
    }
  };

  return (
    <>
      <div
        className={`group relative border-b border-gray-100 p-5 last:border-b-0 dark:border-gray-700/50
                    ${
                      !notification.is_read
                        ? "border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50/50 to-indigo-50/30 dark:from-blue-900/20 dark:to-indigo-900/10"
                        : "bg-white dark:bg-gray-800/50"
                    }
                    cursor-pointer transition-all duration-300 hover:bg-gray-50/80 hover:shadow-sm dark:hover:bg-gray-700/30
                    ${isRTL ? "rtl" : "ltr"}`}
        onClick={!notification.is_read ? handleMarkAsRead : undefined}
      >
        <div
          className={`flex gap-4 ${isRTL ? "flex-row-reverse text-right" : "text-left"}`}
        >
          {/* Priority indicator with icon */}
          <div className="flex flex-shrink-0 flex-col items-center">
            <div
              className={`h-3 w-3 rounded-full ${getPriorityColor(notification.priority)} shadow-sm ring-2 ring-white dark:ring-gray-800`}
            ></div>
            <div className="mt-1 h-6 w-px bg-gray-200 dark:bg-gray-600"></div>
          </div>

          {/* Content */}
          <div className="min-w-0 flex-grow">
            <div className="mb-2 flex items-start justify-between gap-3">
              <div className="min-w-0 flex-grow">
                <h4 className="truncate text-sm font-semibold leading-tight text-gray-900 dark:text-white">
                  {language === "ar"
                    ? notification.title_ar || notification.title
                    : notification.title}
                </h4>
                {/* Type badge */}
                <div className="mt-1 flex items-center gap-2">
                  <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                    {t(notification.type.name)}
                  </span>
                  <span className="text-xs text-gray-400 dark:text-gray-500">
                    {notification.view_date
                      ? formatDate(notification.view_date)
                      : t("unknownDate")}
                  </span>
                </div>
              </div>
              {isEventNotification && (
                <button
                  onClick={handleEventClick}
                  className="inline-flex flex-shrink-0 items-center rounded-lg bg-blue-50 px-2.5 py-1.5 text-xs font-medium text-blue-700 transition-all hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50"
                  title={
                    notification.category === "income_due_date"
                      ? t("updateIncomeStatus")
                      : t("updateExpenseStatus")
                  }
                >
                  <svg
                    className="mr-1.5 h-3 w-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                  {t("updateStatus")}
                </button>
              )}
            </div>
            <p className="line-clamp-2 text-sm leading-relaxed text-gray-600 dark:text-gray-300">
              {language === "ar"
                ? notification.message_ar || notification.message
                : notification.message}
            </p>
          </div>

          {/* Actions */}
          <div className="flex flex-shrink-0 gap-2 self-start opacity-0 transition-opacity duration-200 group-hover:opacity-100">
            {!notification.is_read ? (
              <button
                className="rounded-lg p-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700 dark:text-blue-400 dark:hover:bg-blue-900/30 dark:hover:text-blue-300"
                onClick={handleMarkAsRead}
                title={t("markAsRead")}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </button>
            ) : (
              onMarkAsUnread && (
                <button
                  className="rounded-lg p-2 text-gray-500 transition-all duration-200 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
                  onClick={handleMarkAsUnread}
                  title={t("markAsUnread")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </button>
              )
            )}

            {onDelete && (
              <button
                className="rounded-lg p-2 text-gray-400 transition-all duration-200 hover:bg-red-50 hover:text-red-500 dark:text-gray-500 dark:hover:bg-red-900/30 dark:hover:text-red-400"
                onClick={handleDelete}
                title={t("delete")}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Event Status Modal */}
      {isEventNotification && (
        <EventStatusModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          category={notification.category}
          category_id={notification.category_id}
          eventTitle={
            language === "ar"
              ? notification.title_ar || notification.title
              : notification.title
          }
        />
      )}
    </>
  );
};

export default NotificationItem;

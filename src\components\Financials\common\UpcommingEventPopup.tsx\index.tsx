import React, { useState } from "react";
import { FaTimes, FaEdit, FaSave, FaChevronLeft } from "react-icons/fa";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces"; // Import EventDetails interface
import DetailsSection from "./Sections/DetailsSection";
import HistorySection from "./Sections/HistorySection";
import ConfirmationPopup from "./ConfirmationPopup";

interface UpcomingEventPopupProps {
  event: EventDetails;
  onClose: () => void;
  onSave: (updatedEvent: EventDetails) => void;
}

const UpcomingEventPopup: React.FC<UpcomingEventPopupProps> = ({ event, onClose, onSave }) => {
  const { t, language } = useLanguage();
  const isRTL = language === "ar";
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<EventDetails>(event);
  const [showRemoveConfirmation, setShowRemoveConfirmation] = useState<boolean>(false);
  const [expandedSection, setExpandedSection] = useState<"history" | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = () => {
    if (!formData.title || !formData.amount || !formData.dueDate) {
      alert(t("Please fill in all required fields"));
      return;
    }

    const updatedEvent: EventDetails = {
      ...formData,
      lastEdited: new Date().toISOString(),
      lastEditedBy: "Current User", // Replace with actual user
      editingHistory: [
        ...(formData.editingHistory || []),
        {
          editedBy: "Current User", // Replace with actual user
          editedAt: new Date().toISOString(),
          changes: formData,
        },
      ],
    };

    onSave(updatedEvent);
    setIsEditing(false);
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[1000] p-4">
      <div
        className={`bg-white rounded-xl shadow-xl w-full max-w-[95vw] md:max-w-4xl p-6 md:p-8 relative dark:bg-gray-900 animate-fade-in border border-gray-300 dark:border-gray-700 overflow-y-auto max-h-[80vh] md:max-h-[90vh] ${
          isRTL ? "lg:mr-72.5" : "lg:ml-72.5"
        }`}
      >
        {/* Back Button (Visible only when a section is expanded) */}
        {expandedSection && (
          <button
            className="absolute top-4 left-4 text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            onClick={() => setExpandedSection(null)}
          >
            <FaChevronLeft size={20} />
          </button>
        )}

        {/* Close Button */}
        <button
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          onClick={onClose}
          aria-label="Close"
        >
          <FaTimes size={20} />
        </button>

        {/* Header */}
        <div
          className={`border-b border-gray-300 dark:border-gray-700 pb-4 mb-6 text-center transition-all duration-500 ${
            expandedSection ? "text-left -top-10" : ""
          }`}
        >
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white uppercase">
            {isEditing ? (
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-lg dark:bg-gray-700 dark:text-white"
              />
            ) : (
              event.title
            )}
          </h2>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 text-center">{t("Latest Updates")}</h3>
          <p className="text-gray-600 dark:text-gray-400 text-center">
            {t("last updated")}: {new Date(event.lastEdited).toLocaleString()} by {event.lastEditedBy}
          </p>
        </div>

        {!expandedSection && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <DetailsSection event={formData} isEditing={isEditing} onInputChange={handleInputChange} />
            <HistorySection
              history={formData.editingHistory || []}
              onExpand={() => setExpandedSection("history")}
            />
          </div>
        )}

        {!expandedSection && (
          <div className="flex justify-end mt-6 space-x-4 border-t border-gray-300 dark:border-gray-700 pt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg shadow hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            >
              {t("Close")}
            </button>
            {isEditing ? (
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-green-500 text-white rounded-lg shadow hover:bg-green-600 flex items-center gap-2 transition-colors"
              >
                <FaSave size={16} /> {t("Save")}
              </button>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg shadow hover:bg-blue-600 flex items-center gap-2 transition-colors"
              >
                <FaEdit size={16} /> {t("Edit")}
              </button>
            )}
          </div>
        )}
      </div>

      <ConfirmationPopup
        isOpen={showRemoveConfirmation}
        onConfirm={() => setShowRemoveConfirmation(false)}
        onCancel={() => setShowRemoveConfirmation(false)}
      />
    </div>
  );
};

export default UpcomingEventPopup;
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import useLanguage from "@/hooks/useLanguage";
import { ArrowUpRight } from 'lucide-react';
import {
  Bar<PERSON><PERSON> as ReChartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ReChartsTooltip,
  Legend as ReChartsLegend,
  ResponsiveContainer,
  AreaChart,
  Area,
  PieChart as ReChartsPieChart,
  Pie,
  Cell,
} from 'recharts';

interface RevenueDashboardProps {
  revenueData: Array<{
    period: string;
    revenue: number;
    projectedRevenue: number;
    lastYearRevenue: number;
  }>;
  seasonalChartData: Array<{
    name: string;
    revenue: number;
    occupancy: number;
  }>;
  locationPieData: Array<{
    name: string;
    value: number;
  }>;
  topLocations: Array<{
    name: string;
    revenue: number;
    reservations: number;
    percentage: number;
  }>;
  totalRevenue: number;
  revenueGrowth: number;
  renderActiveShape: (props: any) => JSX.Element;
  COLORS: string[];
  getRevenueDataByTimeframe: () => any[];
}

const RevenueDashboard: React.FC<RevenueDashboardProps> = ({
  revenueData,
  seasonalChartData,
  locationPieData,
  topLocations,
  totalRevenue,
  revenueGrowth,
  renderActiveShape,
  COLORS,
  getRevenueDataByTimeframe
}) => {
  const { t } = useLanguage();
  const [selectedView, setSelectedView] = useState("revenue");
  const [activePieIndex, setActivePieIndex] = useState(0);

  return (
    <div className="space-y-6">
      {/* Revenue Analysis Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-6">
          <h3 className="text-base sm:text-lg font-medium">{t("revenueAnalysis")}</h3>
          <div className="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" className="h-8 text-xs sm:text-sm">
              {t("exportData")}
            </Button>
            <Select
              value={selectedView}
              onValueChange={setSelectedView}
            >
              <SelectTrigger className="w-[150px] sm:w-[180px] h-8 text-xs sm:text-sm">
                <SelectValue placeholder={t("selectView")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="revenue">{t("actualRevenue")}</SelectItem>
                <SelectItem value="comparison">{t("yearOverYear")}</SelectItem>
                <SelectItem value="seasonal">{t("seasonalTrends")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="h-64 sm:h-72 md:h-80 w-full">
          <ResponsiveContainer width="99%" height="100%">
            {selectedView === "comparison" ? (
              <AreaChart
                data={getRevenueDataByTimeframe()}
                margin={{ top: 10, right: 10, left: 0, bottom: 5 }}
              >
                <defs>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                  </linearGradient>
                  <linearGradient id="colorLastYear" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#9333ea" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#9333ea" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis 
                  dataKey="period" 
                  axisLine={false}
                  tickLine={false}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tickFormatter={(value) => `$${value/1000}K`} 
                />
                <ReChartsTooltip 
                  formatter={(value: any) => [`$${value.toLocaleString()}`, ""]}
                  labelFormatter={(label) => `Period: ${label}`}
                />
                <ReChartsLegend />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  name={t("thisYear")}
                  stroke="#3b82f6" 
                  fillOpacity={1}
                  fill="url(#colorRevenue)" 
                />
                <Area 
                  type="monotone" 
                  dataKey="lastYearRevenue" 
                  name={t("lastYear")}
                  stroke="#9333ea" 
                  fillOpacity={1}
                  fill="url(#colorLastYear)" 
                />
              </AreaChart>
            ) : selectedView === "seasonal" ? (
              <ReChartsBarChart
                data={seasonalChartData}
                margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="name" axisLine={false} tickLine={false} />
                <YAxis axisLine={false} tickLine={false} tickFormatter={(value) => `$${value/1000}K`} />
                <ReChartsTooltip formatter={(value: any) => [`$${value.toLocaleString()}`, ""]} />
                <ReChartsLegend />
                <Bar dataKey="revenue" name={t("revenue")} fill="#3b82f6" radius={[4, 4, 0, 0]} />
              </ReChartsBarChart>
            ) : (
              <ReChartsBarChart
                data={getRevenueDataByTimeframe()}
                margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis 
                  dataKey="period" 
                  axisLine={false}
                  tickLine={false}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tickFormatter={(value) => `$${value/1000}K`} 
                />
                <ReChartsTooltip 
                  formatter={(value: any) => [`$${value.toLocaleString()}`, ""]}
                  labelFormatter={(label) => `Period: ${label}`}
                />
                <ReChartsLegend />
                <Bar 
                  dataKey="revenue" 
                  name={t("revenue")}
                  fill="#3b82f6"
                  radius={[4, 4, 0, 0]}
                />
              </ReChartsBarChart>
            )}
          </ResponsiveContainer>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mt-6">
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("totalRevenue")}</p>
            <p className="text-xl font-bold mt-1">${totalRevenue.toLocaleString()}</p>
            <div className="flex items-center mt-2 text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-500 font-medium">+{revenueGrowth}%</span>
              <span className="text-gray-500 dark:text-gray-400 ml-1">{t("fromLastPeriod")}</span>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("projectedRevenue")}</p>
            <p className="text-xl font-bold mt-1">$185,000</p>
            <div className="flex items-center mt-2 text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-500 font-medium">+12%</span>
              <span className="text-gray-500 dark:text-gray-400 ml-1">{t("fromLastPeriod")}</span>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{t("averageDealSize")}</p>
            <p className="text-xl font-bold mt-1">$3,500</p>
            <div className="flex items-center mt-2 text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-500 font-medium">+8%</span>
              <span className="text-gray-500 dark:text-gray-400 ml-1">{t("fromLastPeriod")}</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Revenue Distribution by Location */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm">
        <h3 className="text-lg font-medium mb-6">{t("revenueDistribution")}</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
              {t("byLocation")}
            </h4>
            <div className="h-72 w-full">
              <ResponsiveContainer width="99%" height="100%">
                <ReChartsPieChart>
                  <Pie
                    activeIndex={activePieIndex}
                    activeShape={renderActiveShape}
                    data={locationPieData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    dataKey="value"
                    onMouseEnter={(_, index) => setActivePieIndex(index)}
                  >
                    {locationPieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <ReChartsTooltip />
                </ReChartsPieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
        
        {/* Top Performing Locations Table */}
        {/* <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
            {t("topPerformingLocations")}
          </h4>
          
          <div className="w-full overflow-x-auto">
            <div className="min-w-[640px]">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b dark:border-gray-700">
                    <th className="text-left py-3 px-4">{t("location")}</th>
                    <th className="text-right py-3 px-4">{t("revenue")}</th>
                    <th className="text-right py-3 px-4">{t("reservations")}</th>
                    <th className="text-right py-3 px-4">{t("shareOfTotal")}</th>
                  </tr>
                </thead>
                <tbody>
                  {topLocations.map((location, idx) => (
                    <tr 
                      key={location.name} 
                      className={idx < topLocations.length - 1 ? "border-b dark:border-gray-700" : ""}
                    >
                      <td className="py-3 px-4 font-medium">{location.name}</td>
                      <td className="text-right py-3 px-4 font-medium">${location.revenue.toLocaleString()}</td>
                      <td className="text-right py-3 px-4">{location.reservations}</td>
                      <td className="text-right py-3 px-4">
                        <div className="flex items-center justify-end">
                          <span className="font-medium mr-2">{location.percentage}%</span>
                          <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-blue-500 rounded-full" 
                              style={{ width: `${location.percentage}%` }} 
                            />
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div> 
        </div>*/}
        
        {/* Ad Type Distribution */}
      </div>
    </div>
  );
};

export default RevenueDashboard;

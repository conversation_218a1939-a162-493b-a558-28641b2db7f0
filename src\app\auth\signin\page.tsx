"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Lock, Mail, ChevronRight } from "lucide-react";
import { useAuth } from "@/app/context/AuthContext";
import { ErrorPopup } from "@/components/common/errorPopUp"
import { SuccessPopup } from "@/components/common/successPopUp";
import { LoadingComp } from "@/components/common/Loading";

const SignIn: React.FC = () => {
  const { login } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showErrorPopup, setShowErrorPopup] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    
    try {
      const success = await login(email, password);
      if (success) {
        setShowSuccess(true);
      } else {
        setError("Invalid email or password");
        setShowErrorPopup(true);
      }
    } catch (err) {
      setError("An unexpected error occurred");
      setShowErrorPopup(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-blue-100 flex items-center justify-center px-4 py-8">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/10 z-50 flex items-center justify-center">
          <LoadingComp />
        </div>
      )}

      {showSuccess && (
        <SuccessPopup 
          message="Login successful! Redirecting..." 
          onClose={() => setShowSuccess(false)} 
        />
      )}

      {showErrorPopup && (
        <ErrorPopup 
          message={error}
          onClose={() => setShowErrorPopup(false)} 
        />
      )}

      <div className="w-full max-w-4xl bg-white rounded-2xl shadow-2xl overflow-hidden grid grid-cols-1 md:grid-cols-2">

        <div className="hidden md:flex flex-col items-center justify-center bg-gradient-to-br from-primary/90 to-primary/70 p-12 text-white">
          <div className="mb-8">
            <Image 
              src="/images/logo/saray_vera_white.png" 
              alt="Saray Vera Logo" 
              width={400} 
              height={100} 
              className="mx-auto mb-6"
            />
          </div>
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Welcome Back!</h3>
            <p className="text-white/80 mb-6">
              Sign in to access SARAY VERA dashboard and continue your journey.
            </p>
          </div>
        </div>

        {/* Right Side - Sign In Form */}
        <div className="flex flex-col justify-center p-8 md:p-12">
          <div className="mb-8 text-center md:hidden">
            <Image 
              src="/images/logo/saray-vera-logo.png" 
              alt="Saray Vera Logo" 
              width={150} 
              height={60} 
              className="mx-auto mb-4"
            />
          </div>
          <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center">
            Sign In
          </h2>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-gray-700 mb-2 flex items-center">
                <Mail className="mr-2 text-primary" size={20} />
                Email Address
              </label>
              <div className="relative">
                <input
                  type="email"
                  placeholder="Enter your email"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition duration-300"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label className="block text-gray-700 mb-2 flex items-center">
                <Lock className="mr-2 text-primary" size={20} />
                Password
              </label>
              <div className="relative">
                <input
                  type={isPasswordVisible ? "text" : "password"}
                  placeholder="Enter your password"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition duration-300"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <button
                  type="button"
                  onClick={() => setIsPasswordVisible(!isPasswordVisible)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-primary"
                >
                  {isPasswordVisible ? "Hide" : "Show"}
                </button>
              </div>
            </div>

            <button
              type="submit"
              className="w-full bg-primary text-white py-3 rounded-lg hover:bg-primary/90 transition duration-300 flex items-center justify-center group"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <LoadingComp />
                </span>
              ) : (
                <>
                  Sign In
                  <ChevronRight 
                    className="ml-2 group-hover:translate-x-1 transition" 
                    size={20} 
                  />
                </>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
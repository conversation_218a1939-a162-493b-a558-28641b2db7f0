import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";

// Import the tab components
import ContractsTabComponent from './Tabs/ContractsTab';
import ExpenseEventsTabComponent from './Tabs/ExpenseEventsTab';
import ReservationsTabComponent from './Tabs/ReservationsTab';
import IncomeEventsTabComponent from './Tabs/IncomeEventsTab';

export interface FinancialHistoryProps {
  ContactId: string;
  isLoading?: boolean;
}

export const FinancialHistory: React.FC<FinancialHistoryProps> = ({ 
  ContactId,
  isLoading = false
}) => {
  const { t } = useLanguage();
  // State for active tabs
  const [activeMainTab, setActiveMainTab] = useState<string>("income");
  const [activeIncomeTab, setActiveIncomeTab] = useState<string>("reservations");
  const [activeExpenseTab, setActiveExpenseTab] = useState<string>("contracts");
  
  // Track screen size for responsive adjustments
  const [isMobile, setIsMobile] = useState<boolean>(false);
  
  // Check screen size on component mount and window resize
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640);
    };
    
    // Initial check
    checkScreenSize();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkScreenSize);
    
    // Clean up
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);
  
  // Display loading state if data is being fetched
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader className="px-4 sm:px-6">
          <CardTitle>{t("Financial History")}</CardTitle>
          <CardDescription>{t("Loading financial data...")}</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center p-6 sm:p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader className="px-4 sm:px-6">
        <CardTitle>{t("Financial History")}</CardTitle>
        <CardDescription>{t("View all financial records for this contact")}</CardDescription>
      </CardHeader>
      <CardContent className="p-3 sm:p-5">
        {/* Main tabs: Income and Expenses */}
        <Tabs 
          defaultValue="income" 
          value={activeMainTab} 
          onValueChange={setActiveMainTab}
          className="w-full"
        >
          <TabsList className={`grid w-full ${isMobile ? 'grid-cols-1 gap-2' : 'grid-cols-2'} mb-4`}>
            <TabsTrigger value="income" className="text-sm sm:text-base">{t("Income")}</TabsTrigger>
            <TabsTrigger value="expenses" className="text-sm sm:text-base">{t("Expenses")}</TabsTrigger>
          </TabsList>
          
          {/* Income Tab Content */}
          <TabsContent value="income">
            <Tabs 
              defaultValue="reservations" 
              value={activeIncomeTab} 
              onValueChange={setActiveIncomeTab}
              className="w-full"
            >
              <TabsList className={`grid w-full ${isMobile ? 'grid-cols-1 gap-2' : 'grid-cols-2'} mb-4`}>
                <TabsTrigger value="reservations" className="text-sm sm:text-base">{t("Reservations")}</TabsTrigger>
                <TabsTrigger value="events" className="text-sm sm:text-base">{t("Events")}</TabsTrigger>
              </TabsList>
              
              <div className="mt-4 overflow-x-auto">
                <TabsContent value="reservations" className="min-h-[200px]">
                  <ReservationsTabComponent ContactId={ContactId} />
                </TabsContent>
                
                <TabsContent value="events" className="min-h-[200px]">
                  <IncomeEventsTabComponent ContactId={ContactId} />
                </TabsContent>
              </div>
            </Tabs>
          </TabsContent>
          
          {/* Expenses Tab Content */}
          <TabsContent value="expenses">
            <Tabs 
              defaultValue="contracts" 
              value={activeExpenseTab} 
              onValueChange={setActiveExpenseTab}
              className="w-full"
            >
              <TabsList className={`grid w-full ${isMobile ? 'grid-cols-1 gap-2' : 'grid-cols-2'} mb-4`}>
                <TabsTrigger value="contracts" className="text-sm sm:text-base">{t("Contracts")}</TabsTrigger>
                <TabsTrigger value="events" className="text-sm sm:text-base">{t("Events")}</TabsTrigger>
              </TabsList>
              
              <div className="mt-4 overflow-x-auto">
                <TabsContent value="contracts" className="min-h-[200px]">
                  <ContractsTabComponent ContactId={ContactId} />
                </TabsContent>
                
                <TabsContent value="events" className="min-h-[200px]">
                  <ExpenseEventsTabComponent ContactId={ContactId} />
                </TabsContent>
              </div>
            </Tabs>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default FinancialHistory;

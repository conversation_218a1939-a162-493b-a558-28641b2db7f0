import React from 'react';
import NotificationItem from './NotificationItem';
import { Notification, NotificationFilterOptions } from '@/lib/types/notification';
import useLanguage from '@/hooks/useLanguage';

interface NotificationListProps {
  notifications: Notification[];
  filterOptions: NotificationFilterOptions;
  onMarkAsRead: (id: string) => void;
  onMarkAsUnread?: (id: string) => void;
  onDelete?: (id: string) => void;
  compact?: boolean;
  isLoading?: boolean;
}

const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  filterOptions,
  onMarkAsRead,
  onMarkAsUnread,
  onDelete,
  compact = false,
  isLoading = false,
}) => {
  const { t } = useLanguage();

  const filteredNotifications = notifications.filter((notification) => {
    // Filter by type
    if (filterOptions.type && filterOptions.type !== 'all' && notification.type.id !== filterOptions.type) {
      return false;
    }

    // Filter by read status
    if (filterOptions.read !== null && filterOptions.read !== undefined && notification.is_read === filterOptions.read) {
      return false;
    }

    // Filter by priority
    if (filterOptions.priority && filterOptions.priority !== 'all' && notification.priority !== filterOptions.priority) {
      return false;
    }

    // Filter by timeframe
    if (filterOptions.timeframe && filterOptions.timeframe !== 'all') {
      const now = new Date();
      const notificationDate = new Date(notification.created_at);
      
      switch (filterOptions.timeframe) {
        case 'today':
          if (
            notificationDate.getDate() !== now.getDate() ||
            notificationDate.getMonth() !== now.getMonth() ||
            notificationDate.getFullYear() !== now.getFullYear()
          ) {
            return false;
          }
          break;
        case 'week':
          const oneWeekAgo = new Date();
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
          if (notificationDate < oneWeekAgo) {
            return false;
          }
          break;
        case 'month':
          const oneMonthAgo = new Date();
          oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
          if (notificationDate < oneMonthAgo) {
            return false;
          }
          break;
      }
    }

    // Filter by search
    if (filterOptions.search && filterOptions.search.trim() !== '') {
      const searchTerm = filterOptions.search.toLowerCase();
      const title = notification.title.toLowerCase();
      const message = notification.message.toLowerCase();
      
      if (!title.includes(searchTerm) && !message.includes(searchTerm)) {
        return false;
      }
    }

    return true;
  });

  // For compact view, limit to 5 most recent notifications
  const displayedNotifications = compact 
    ? filteredNotifications.slice(0, 5) 
    : filteredNotifications;

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
          {t('loadingNotifications')}
        </p>
      </div>
    );
  }

  if (displayedNotifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="rounded-full bg-gray-100 dark:bg-gray-700 p-3 mb-2">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6 text-gray-400 dark:text-gray-500" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" 
            />
          </svg>
        </div>
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
          {t('noNotifications')}
        </h3>
        <p className="text-xs text-gray-400 dark:text-gray-500 max-w-sm mt-1">
          {t('noNotificationsMessage')}
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-1 ${compact ? 'max-h-80' : ''} overflow-y-auto`}>
      {displayedNotifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onMarkAsRead={onMarkAsRead}
          onMarkAsUnread={onMarkAsUnread}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
};

export default NotificationList;

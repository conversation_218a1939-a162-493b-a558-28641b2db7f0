
import { AxiosInstance } from "axios";

import { ExpenseType } from "./interfaces/eventTypes";


interface CreateExpenseTypePayload {
    name: string;
}

const createExpenseTypeServices = (apiClient: AxiosInstance) => ({

    createExpenseType: async (payload: CreateExpenseTypePayload): Promise<ExpenseType> => {
        try {
            const response = await apiClient.post("/expenses/api/types/create/", payload);
            return response.data;
        } catch (error) {
            console.error("Error creating location:", error);
            throw new Error("Error creating location");
        }
    },

});

export default createExpenseTypeServices;
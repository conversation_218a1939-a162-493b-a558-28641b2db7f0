import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api/apiClient';
import { Location } from '@/lib/types/location';
import createLocationServices from '@/lib/locations';

export interface LocationApiResponse {
    locations: Location[];
  }


  export const useLocations= () => {
    return useQuery<LocationApiResponse>({
      queryKey: ['locations'],
      queryFn: async () => {
        const { data } = await apiClient.get('/locations/api/getall/');
        return data;
      },
      staleTime: 10 * 60 * 1000, 
    });
  };

  export function useLocationServices() {
    return createLocationServices(apiClient);
  }

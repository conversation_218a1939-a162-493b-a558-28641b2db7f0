"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation"; // Import useSearchParams
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import UpcomingEventPopup from "../common/UpcommingEventPopup.tsx";
import Analytics from "./Sections/Analytic";
import UpcomingEventsPage from "@/components/Dashboard/UpcommingEventsPage/UpcommingEvents";
import {
  getDateRange,
  DateRangeSelector,
} from "@/components/Financials/common/dateRanges";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import { usePermissions } from "@/hooks/usePermissions";
import { Card, CardContent } from "../../cards/card";
import { TrendingUp } from "lucide-react";
import getCombinedEvents from "@/lib/events";
import { LoadingComp } from "@/components/common/Loading";
import { format } from "date-fns";

const Overview: React.FC = () => {
  const { t, language } = useLanguage();
  const searchParams = useSearchParams();
  const { hasPermission, permissionsLoaded } = usePermissions();

  // Calculate date range
  const currentDate = new Date();
  const startRangeDate = new Date(currentDate);
  startRangeDate.setDate(currentDate.getDate() - 15);
  const endRangeDate = new Date(currentDate);
  endRangeDate.setDate(currentDate.getDate() + 15);

  const formatDate = (date: Date) => {
    return date.toISOString().split("T")[0];
  };

  const [upcomingEvents, setUpcomingEvents] = useState<EventDetails[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<EventDetails | null>(null);
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [startDate, setStartDate] = useState<Date>(startRangeDate);
  const [endDate, setEndDate] = useState<Date>(endRangeDate);
  const [loading, setLoading] = useState<boolean>(true); // Loading state

  // Get month and year from URL parameters - add null check
  const monthParam = searchParams ? searchParams.get("month") : null;
  const yearParam = searchParams ? searchParams.get("year") : null;

  useEffect(() => {
    if (monthParam && yearParam) {
      const month = parseInt(monthParam, 10) - 1; // Convert to zero-indexed month
      const year = parseInt(yearParam, 10);
      const start = new Date(year, month, 1);
      const end = new Date(year, month + 1, 0); // Last day of the month

      setStartDate(start);
      setEndDate(end);
    }
  }, [monthParam, yearParam]);
  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      try {
        const events = await getCombinedEvents();
        console.log("sdlfkasdjkhfkasdgjk");

        setUpcomingEvents(events);
      } catch (error) {
        console.error("Error fetching events:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const filterEventsByDate = (events: EventDetails[]) => {
    return events.filter((event) => {
      // Normalize dates to remove time component for accurate comparison
      const eventDate = new Date(event.dueDate);
      eventDate.setHours(0, 0, 0, 0);

      const start = startDate ? new Date(startDate) : null;
      if (start) start.setHours(0, 0, 0, 0);

      const end = endDate ? new Date(endDate) : null;
      if (end) end.setHours(23, 59, 59, 999); // Set to end of day for inclusive comparison

      if (start && end) {
        return eventDate >= start && eventDate <= end;
      } else if (start) {
        return eventDate >= start;
      } else if (end) {
        return eventDate <= end;
      } else {
        return true;
      }
    });
  };
  const convertToArabicNumerals = (num: number | string | undefined | null) => {
    if (num === undefined || num === null) return "";
    return Math.floor(Number(num))
      .toString()
      .replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[parseInt(d)]);
  };
  const formatDateLocalized = (date: Date | null) => {
    if (!date) return "";
    return language === "ar"
      ? `${convertToArabicNumerals(date.getDate())}/${convertToArabicNumerals(date.getMonth() + 1)}/${convertToArabicNumerals(date.getFullYear())}`
      : date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        });
  };

  // Event handlers
  const handleEditClick = useCallback(
    (id: string) => {
      const event = upcomingEvents.find((event) => event.id === id);
      if (event) {
        setSelectedEvent(event);
      }
    },
    [upcomingEvents],
  );

  const handlePopupClose = useCallback(() => setSelectedEvent(null), []);
  const handleSave = useCallback(
    (updatedEvent: EventDetails) => {
      setUpcomingEvents((prevEvents) =>
        prevEvents.map((event) =>
          event.id === updatedEvent.id ? updatedEvent : event,
        ),
      );
      handlePopupClose();
    },
    [handlePopupClose],
  );

  const handleDateRangeChange = (range: string) => {
    const { start, end } = getDateRange(range);
    setStartDate(new Date(start));
    setEndDate(new Date(end));
  };

  const locale = language === "ar" ? arSA : enUS;

  // Check permissions loading state
  if (!permissionsLoaded) {
    return (
      <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }

  // Check if user has view permission for financials
  if (!hasPermission("financials", "view")) {
    return (
      <>
        <Breadcrumb pageName={t("overview")} />
        <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
          <div className="flex items-center justify-center h-full">
            <Card className="w-full max-w-md">
              <CardContent className="text-center py-12">
                <TrendingUp size={48} className="mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  {t("accessDenied")}
                </h2>
                <p className="text-gray-500 dark:text-gray-400">
                  {t("noPermissionToViewFinancials")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 lg:space-y-8">
      <Breadcrumb pageName={t("overview")} />

      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex flex-col space-y-3 sm:space-y-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-400 sm:text-2xl lg:text-3xl">
            {t("financialOverview")}
          </h2>

          {/* Date Range Controls */}
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400 sm:text-base">
              {t("as of")}
            </span>
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
              <DatePicker
                selected={startDate}
                onChange={(date: Date | null) => date && setStartDate(date)}
                locale={locale}
                calendarStartDay={6}
                customInput={
                  <input
                    type="text"
                    className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 sm:w-auto"
                    value={formatDateLocalized(startDate)}
                    readOnly
                  />
                }
              />
              <span className="text-sm text-gray-500 dark:text-gray-400 sm:text-base">
                {t("to")}
              </span>
              <DatePicker
                selected={endDate}
                onChange={(date: Date | null) => date && setEndDate(date)}
                locale={locale}
                customInput={
                  <input
                    type="text"
                    className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 sm:w-auto"
                    value={formatDateLocalized(endDate)}
                    readOnly
                  />
                }
              />
            </div>
          </div>
        </div>

        {/* Date Range Selector */}
        <DateRangeSelector onChange={handleDateRangeChange} />
      </div>

      {/* Analytics Section */}
      <div className="w-full">
        <Analytics events={filterEventsByDate(upcomingEvents)} />
      </div>

      {/* Events Section */}
      <div className="w-full">
        {loading ? (
          <div className="flex min-h-[400px] items-center justify-center rounded-lg bg-white dark:bg-gray-800">
            <LoadingComp />
          </div>
        ) : (
          <UpcomingEventsPage
            events={filterEventsByDate(upcomingEvents)}
            setEvents={setUpcomingEvents}
          />
        )}
      </div>

      {/* Popup */}
      {selectedEvent && (
        <UpcomingEventPopup
          event={selectedEvent}
          onClose={handlePopupClose}
          onSave={handleSave}
        />
      )}
    </div>
  );
};

export default Overview;

import React, { useState , useEffect } from "react";
import { Percent, AlertCircle, X, PlusCircle } from "lucide-react";
import ContactSelectDropdown from "./ContactSelectDropdown";
import { OwnershipShare } from "./types";
import { useContacts } from "@/hooks/useContact";

interface OwnershipPopupProps {
  onSubmit: (data: { 
    primaryOwner: string; 
    ownershipShares: OwnershipShare[] ;
    primaryOwnerEgyComm?: boolean;
    ourPercentage?: number;
    totalPartnerPercentage?: number;

  }) => void;
  onClose: () => void;
  data: any;
}


const OwnershipPopup: React.FC<OwnershipPopupProps> = ({ onSubmit, onClose , data }) => {
  const t = (key: string) => key; // Replace with actual translation if needed



  const [isEgyCommPrimaryOwner, setIsEgyCommPrimaryOwner] = useState(false);
  const [ownedBy, setOwnedBy] = useState<string>("");
  const [ourPercentage, setOurPercentage] = useState<number>(100);
  const [ownershipShares, setOwnershipShares] = useState<OwnershipShare[]>([]);
  const [percentageError, setPercentageError] = useState<string>("");
  const { data: contacts, isLoading, isError } = useContacts();
  const [contactOptions, setContactOptions] = useState<string[]>([]);

  useEffect(() => {
    if (data) {
      setIsEgyCommPrimaryOwner(data.primaryOwnerEgyComm || false);
      setOwnedBy(data.primaryOwner || "");
      setOurPercentage(data.ourPercentage || 100);
      setOwnershipShares(data.ownershipShares || []);
    }
  }, [data]);

  
  const totalPartnerPercentage = ownershipShares.reduce((sum, share) => sum + share.percentage, 0);
  const combinedTotal = ourPercentage + totalPartnerPercentage;

  const validatePercentages = () => {
    if (combinedTotal !== 100) {
      setPercentageError("Total must equal 100%");
      return false;
    }
    setPercentageError("");
    return true;
  };

  const handleEgyCommToggle = (checked: boolean) => {
    setIsEgyCommPrimaryOwner(checked);
    if (checked) {
      setOwnershipShares([]);
      setOwnedBy("EgyComm");
      setOurPercentage(100);
    } else {
      setOwnedBy("");
    }
  };
  
    // Initialize contact options
    useEffect(() => {
      if (contacts && contacts.contacts) {
        var contactsnames = contacts.contacts.map((contact) => contact.name);
        setContactOptions(contactsnames);
      }
    }, [contacts]);
  

  const handleOurPercentageChange = (value: number) => {
    setOurPercentage(value);
    if (!isEgyCommPrimaryOwner) {
      const remaining = 100 - value;
      const currentTotal = ownershipShares.reduce((sum, s) => sum + s.percentage, 0);
      const updatedShares = [...ownershipShares];

      if (currentTotal > 0) {
        updatedShares.forEach((s) => {
          s.percentage = parseFloat(((s.percentage / currentTotal) * remaining).toFixed(2));
        });

        const adjustedTotal = updatedShares.reduce((sum, s) => sum + s.percentage, 0);
        updatedShares[0].percentage += parseFloat((remaining - adjustedTotal).toFixed(2));
      }
      setOwnershipShares(updatedShares);
    }
  };

  const handleAddPartner = () => {
    setOwnershipShares([...ownershipShares, { name: "", percentage: 0 }]);
  };

  const handlePartnerChange = (index: number, field: "name" | "percentage", value: string | number) => {
    const updatedShares = [...ownershipShares];
    updatedShares[index] = { ...updatedShares[index], [field]: value };
    setOwnershipShares(updatedShares);
  };

  const handleOwnedBySelect = (owner: string) => {
    const isEgyComm = owner === "EgyComm";
    setIsEgyCommPrimaryOwner(isEgyComm);
    setOwnedBy(owner);

    if (isEgyComm) {
      setOwnershipShares([]);
      setOurPercentage(100);
    } else {
      const newShares = [];
      newShares.push({
        name: owner,
        percentage: 50,
      });
      setOwnershipShares(newShares);
      setOurPercentage(50);
    }
  };

  const handleSubmit = () => {
    if (!validatePercentages()) return;
    onSubmit({ 
      primaryOwner: ownedBy, 
      ownershipShares,
      primaryOwnerEgyComm: isEgyCommPrimaryOwner,
      ourPercentage: isEgyCommPrimaryOwner ? 100 : ourPercentage,
      totalPartnerPercentage: totalPartnerPercentage
      
     });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-2xl rounded-lg bg-white p-6 shadow-xl dark:bg-gray-900">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 dark:text-gray-300"
        >
          <X size={20} />
        </button>

        <h2 className="mb-4 text-xl font-semibold text-gray-800 dark:text-white">
          {t("ownershipStructure")}
        </h2>

        {/* Primary Owner Toggle */}
        <div className="mb-4 flex items-center space-x-2 rounded bg-blue-50 p-3 dark:bg-blue-900/20">
          <input
            type="checkbox"
            checked={isEgyCommPrimaryOwner}
            onChange={(e) => handleEgyCommToggle(e.target.checked)}
            className="h-4 w-4"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            {t("egyCommIsPrimaryOwner")}
          </span>
        </div>

        {/* Primary Owner Dropdown */}
        {!isEgyCommPrimaryOwner && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("primaryOwner")}
            </label>
            <ContactSelectDropdown
              value={ownedBy}
              options={contactOptions}
              placeholder={t("selectPrimaryOwner")}
              onSelect={(value) => handleOwnedBySelect(value)}
            />
          </div>
        )}

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("egyCommPercentage")}
          </label>
          <div className="relative">
            <input
              type="number"
              value={ourPercentage}
              onChange={(e) => handleOurPercentageChange(parseFloat(e.target.value) || 0)}
              className="w-full rounded-lg border p-2 pr-10 dark:border-gray-700 dark:bg-gray-800 dark:text-white"
            />
            <Percent className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Additional Partners */}
        <div className="mb-4">
          <h3 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("additionalPartners")}
          </h3>
          {ownershipShares.map((share, index) => (
            <div key={index} className="mb-2 flex items-center space-x-2">
              <ContactSelectDropdown
                value={share.name}
                options={contactOptions}
                placeholder={t("selectPartner")}
                onSelect={(value) => handlePartnerChange(index, "name", value)}
                className="flex-1"
              />
              <input
                type="number"
                placeholder={t("percentage")}
                value={share.percentage}
                onChange={(e) => handlePartnerChange(index, "percentage", parseFloat(e.target.value) || 0)}
                className="w-24 rounded-lg border p-2 dark:border-gray-700 dark:bg-gray-800 dark:text-white"
              />
              <Percent className="h-4 w-4 text-gray-400" />
            </div>
          ))}
          <button
            onClick={handleAddPartner}
            className="mt-2 flex items-center text-sm text-blue-600 hover:underline"
          >
            <PlusCircle className="mr-1 h-4 w-4" />
            {t("addPartner")}
          </button>
        </div>

        {percentageError && (
          <div className="mb-4 flex items-center text-sm text-red-500">
            <AlertCircle className="mr-2 h-4 w-4" />
            {percentageError}
          </div>
        )}

        {/* Summary Section */}
        <div className="mt-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("summary")}
          </h4>
          <ul className="mt-2 space-y-1 text-sm">
            <li className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {t("primaryOwner")}:
              </span>
              <span className="font-medium">
                {isEgyCommPrimaryOwner ? "EgyComm" : ownedBy || t("notSet")}
              </span>
            </li>
            <li className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {t("egyCommPercentage")}:
              </span>
              <span className="font-medium">{ourPercentage || 0}%</span>
            </li>
            <li className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {t("totalPartnersPercentage")}:
              </span>
              <span className="font-medium">{totalPartnerPercentage}%</span>
            </li>
            <li className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {t("combinedTotal")}:
              </span>
              <span
                className={`font-medium ${
                  combinedTotal !== 100 ? "text-red-500" : "text-green-500"
                }`}
              >
                {combinedTotal}% {combinedTotal === 100 ? "✓" : ""}
              </span>
            </li>
            <li className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {t("partners")}:
              </span>
              <span className="font-medium">{ownershipShares.length}</span>
            </li>
          </ul>
        </div>

        {/* Percentage Allocation Bar */}
        <div className="mt-4">
          <div className="mb-1 flex justify-between text-sm">
            <span className="font-medium">{t("percentageAllocation")}</span>
            <span
              className={`font-medium ${
                combinedTotal !== 100 ? "text-red-500" : "text-green-500"
              }`}
            >
              {combinedTotal}% {combinedTotal === 100 ? "✓" : ""}
            </span>
          </div>
          <div className="flex h-6 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
            {/* EgyComm's percentage */}
            <div
              className="flex h-full items-center justify-center bg-blue-600 text-xs font-bold text-white"
              style={{ width: `${ourPercentage || 0}%` }}
            >
              {(ourPercentage || 0) > 10 ? `${ourPercentage || 0}%` : ""}
            </div>

            {/* Partners' percentages */}
            {ownershipShares.map((share, idx) => {
              const colors = [
                "bg-green-500 dark:bg-green-600",
                "bg-purple-500 dark:bg-purple-600",
                "bg-yellow-500 dark:bg-yellow-600",
                "bg-pink-500 dark:bg-pink-600",
                "bg-red-500 dark:bg-red-600",
              ];
              const color = colors[idx % colors.length];

              return (
                <div
                  key={idx}
                  className={`h-full ${color} flex items-center justify-center text-xs font-bold text-white`}
                  style={{ width: `${share.percentage}%` }}
                >
                  {share.percentage > 10 ? `${share.percentage}%` : ""}
                </div>
              );
            })}

            {/* Remaining space */}
            {combinedTotal < 100 && (
              <div
                className="flex h-full items-center justify-center bg-gray-300 text-xs font-bold text-white dark:bg-gray-600"
                style={{ width: `${100 - combinedTotal}%` }}
              >
                {100 - combinedTotal > 10 ? `${100 - combinedTotal}%` : ""}
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <button
            onClick={onClose}
            className="rounded bg-gray-200 px-4 py-2 text-sm text-gray-700 hover:bg-gray-300"
          >
            {t("cancel")}
          </button>
          <button
            onClick={handleSubmit}
            className="rounded bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700"
          >
            {t("submit")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OwnershipPopup;
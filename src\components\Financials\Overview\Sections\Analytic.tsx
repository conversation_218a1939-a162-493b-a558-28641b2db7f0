import React, { useEffect, useState, useCallback } from "react";
import { useSpring, animated } from "@react-spring/web";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";

interface AnalyticsProps {
  events: EventDetails[];
}

const Analytics: React.FC<AnalyticsProps> = ({ events }) => {
  const [analytics, setAnalytics] = useState({
    totalIncome: 0,
    totalExpense: 0,
    currentBalance: 0,
  });
  const [expectedAnalytics, setExpectedAnalytics] = useState({
    totalIncome: 0,
    totalExpense: 0,
    currentBalance: 0,
  });
  const { t, language } = useLanguage();

  const calculateAnalytics = useCallback(() => {
    let totalIncome = 0;
    let totalExpense = 0;
    let currentBalance = 0;

    const updatedExpectedAnalytics = {
      totalIncome: 0,
      totalExpense: 0,
      currentBalance: 0,
    };

    events.forEach((event) => {
      const amount =
        typeof event.amount === "string"
          ? parseInt(event.amount, 10) || 0
          : event.amount || 0;
      const isIncome = event.category === "income";
      const isCompleted = event.status === "completed";
      const isPendingOrOverdue = [
        "pending",
        "overdue",
        "upcoming",
        "cancelled",
      ].includes(event.status);

      if (isIncome) {
        if (isCompleted) {
          totalIncome += amount;
          updatedExpectedAnalytics.totalIncome += amount;
          currentBalance += amount;
          updatedExpectedAnalytics.currentBalance += amount;
        }
      } else {
        if (isCompleted) {
          totalExpense += amount;
          updatedExpectedAnalytics.totalExpense += amount;
          currentBalance -= amount;
          updatedExpectedAnalytics.currentBalance -= amount;
        }
      }

      if (isPendingOrOverdue) {
        if (isIncome) {
          updatedExpectedAnalytics.totalIncome += amount;
          updatedExpectedAnalytics.currentBalance += amount;
        } else {
          updatedExpectedAnalytics.totalExpense += amount;
          updatedExpectedAnalytics.currentBalance -= amount;
        }
      }
    });
    console.log("Updated Expected Analytics:", updatedExpectedAnalytics);

    setExpectedAnalytics(updatedExpectedAnalytics);

    return {
      totalIncome: Math.round(totalIncome),
      totalExpense: Math.round(totalExpense),
      currentBalance: Math.round(currentBalance),
    };
  }, [events]);

  useEffect(() => {
    const analyticsData = calculateAnalytics();
    console.log("Analytics Data:", analyticsData);

    setAnalytics(analyticsData);
  }, [calculateAnalytics]);

  // Currency formatting
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Compact currency formatting for large numbers
  const formatCurrencyCompact = (value: number): string => {
    const absValue = Math.abs(value);
    const isNegative = value < 0;

    let formattedValue: string;
    let suffix = "";

    if (absValue >= 1_000_000_000) {
      formattedValue = (absValue / 1_000_000_000).toFixed(1);
      suffix = language === "ar" ? "مليار" : "B";
    } else if (absValue >= 1_000_000) {
      formattedValue = (absValue / 1_000_000).toFixed(1);
      suffix = language === "ar" ? "مليون" : "M";
    } else if (absValue >= 1_000) {
      formattedValue = (absValue / 1_000).toFixed(1);
      suffix = language === "ar" ? "ألف" : "K";
    } else {
      formattedValue = absValue.toFixed(0);
    }

    // Remove unnecessary .0
    if (formattedValue.endsWith(".0")) {
      formattedValue = formattedValue.slice(0, -2);
    }

    if (language === "ar") {
      const arabicDigits = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"];
      const arabicFormatted = formattedValue.replace(
        /[0-9]/g,
        (digit) => arabicDigits[parseInt(digit)],
      );
      return `${isNegative ? "-" : ""}${arabicFormatted}${suffix ? " " + suffix : ""} ج.م`;
    } else {
      return `${isNegative ? "-" : ""}EGP ${formattedValue}${suffix}`;
    }
  };

  // Helper function to render a card with emphasis on actual values
  const renderCard = (
    title: string,
    actualValue: number,
    expectedValue: number,
  ) => {
    const { number: animatedActualValue } = useSpring({
      from: { number: 0 },
      number: actualValue,
      delay: 200,
      config: { mass: 1, tension: 170, friction: 26 },
    });

    const { number: animatedExpectedValue } = useSpring({
      from: { number: 0 },
      number: expectedValue,
      delay: 200,
      config: { mass: 1, tension: 170, friction: 26 },
    });

    return (
      <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-sm transition-all duration-300 hover:scale-[1.02] hover:shadow-md dark:border-gray-700 dark:bg-gray-800 sm:p-4 lg:p-6">
        <h3
          className="mb-3 truncate text-sm font-semibold text-gray-800 dark:text-gray-200 sm:mb-4 sm:text-base lg:text-lg"
          title={t(title)}
        >
          {t(title)}
        </h3>
        <div className="space-y-3 sm:space-y-4">
          {/* Actual Value (Emphasized) */}
          <div className="space-y-1">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400 sm:text-sm">
              {t("actual")}
            </span>
            <div className="space-y-1">
              <p
                className="break-words text-lg font-bold leading-tight text-gray-900 dark:text-gray-100 sm:text-xl lg:text-2xl"
                title={formatCurrency(actualValue)}
              >
                <animated.span>
                  {animatedActualValue.to((n) => formatCurrencyCompact(n))}
                </animated.span>
              </p>
              <p className="truncate text-xs text-gray-500 dark:text-gray-400">
                <animated.span>
                  {animatedActualValue.to((n) => formatCurrency(n))}
                </animated.span>
              </p>
            </div>
          </div>
          {/* Expected Value (Subtle) */}
          <div className="space-y-1">
            <span className="text-xs font-medium text-gray-500 dark:text-gray-500 sm:text-sm">
              {t("expected")}
            </span>
            <div className="space-y-1">
              <p
                className="break-words text-sm font-semibold leading-tight text-gray-600 dark:text-gray-400 sm:text-base lg:text-lg"
                title={formatCurrency(expectedValue)}
              >
                <animated.span>
                  {animatedExpectedValue.to((n) => formatCurrencyCompact(n))}
                </animated.span>
              </p>
              <p className="truncate text-xs text-gray-500 dark:text-gray-500">
                <animated.span>
                  {animatedExpectedValue.to((n) => formatCurrency(n))}
                </animated.span>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4 rounded-lg p-3 sm:space-y-6 sm:p-4 lg:space-y-8 lg:p-6">
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4 lg:grid-cols-3 lg:gap-6">
        {/* Current Balance */}
        {renderCard(
          "current balance",
          analytics.currentBalance,
          expectedAnalytics.currentBalance,
        )}

        {/* Total Income */}
        {renderCard(
          "total income",
          analytics.totalIncome,
          expectedAnalytics.totalIncome,
        )}

        {/* Total Expense */}
        {renderCard(
          "total expense",
          analytics.totalExpense,
          expectedAnalytics.totalExpense,
        )}
      </div>
    </div>
  );
};

export default Analytics;

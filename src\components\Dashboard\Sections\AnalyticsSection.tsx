import React, { useState, useMemo } from "react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, Area, AreaChart } from "recharts";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";
import {
    processEventsToMonthlyData,
    formatNumber,
    formatCurrency,
    formatCurrencyCompact,
    getChartConfig,
    filterValidEvents,
    calculateGrowthRate,
    CHART_COLORS,
    AnalyticsDataPoint
} from "@/utils/analyticsUtils";
import { TrendingUp, TrendingDown, Minus, BarChart3, Line<PERSON><PERSON> as LineChartIcon } from "lucide-react";

interface AnalyticsSectionProps {
    events: EventDetails[];
}

const AnalyticsSection: React.FC<AnalyticsSectionProps> = ({ events }) => {
    const { t, language } = useLanguage();
    const [viewMode, setViewMode] = useState<"previous" | "upcoming">("previous");
    const [chartType, setChartType] = useState<"bar" | "line" | "area">("bar");
    const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
    const [selectedMonth, setSelectedMonth] = useState<number | null>(null);

    // Process data using the utility functions
    const processedData = useMemo(() => {
        const validEvents = filterValidEvents(events);
        return processEventsToMonthlyData(validEvents, language);
    }, [events, language]);

    // Filter data based on view mode and selections
    const filteredData = useMemo(() => {
        let data = processedData.filter((item) =>
            viewMode === "previous" ? !item.isUpcoming : item.isUpcoming
        );

        // Apply year and month filters
        if (selectedYear) {
            data = data.filter(item => item.year === selectedYear);
        }
        if (selectedMonth !== null) {
            data = data.filter(item => item.month === selectedMonth);
        }

        return data;
    }, [processedData, viewMode, selectedYear, selectedMonth]);

    // Calculate summary statistics
    const summaryStats = useMemo(() => {
        const totalIncome = filteredData.reduce((sum, item) => sum + item.income, 0);
        const totalExpense = filteredData.reduce((sum, item) => sum + item.expense, 0);
        const netProfit = totalIncome - totalExpense;

        // Calculate growth rates (compare with previous period)
        const currentPeriodData = filteredData.slice(-6); // Last 6 months
        const previousPeriodData = filteredData.slice(-12, -6); // Previous 6 months

        const currentIncome = currentPeriodData.reduce((sum, item) => sum + item.income, 0);
        const previousIncome = previousPeriodData.reduce((sum, item) => sum + item.income, 0);
        const incomeGrowth = calculateGrowthRate(currentIncome, previousIncome);

        const currentExpense = currentPeriodData.reduce((sum, item) => sum + item.expense, 0);
        const previousExpense = previousPeriodData.reduce((sum, item) => sum + item.expense, 0);
        const expenseGrowth = calculateGrowthRate(currentExpense, previousExpense);

        return {
            totalIncome,
            totalExpense,
            netProfit,
            incomeGrowth,
            expenseGrowth,
            profitMargin: totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0
        };
    }, [filteredData]);

    // Years for filtering
    const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i);

    // Months for filtering
    const months = Array.from({ length: 12 }, (_, i) => ({
        value: i,
        label: new Date(0, i).toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', { month: "long" }),
    }));

    // Chart configuration
    const chartConfig = getChartConfig(language);

    // Custom tooltip component
    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            const data = payload[0]?.payload;
            return (
                <div className="bg-white p-4 border rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700 min-w-[200px]">
                    <p className="font-semibold text-gray-900 dark:text-white mb-3">{label}</p>

                    {/* Income breakdown */}
                    <div className="mb-2">
                        <p className="text-sm font-medium text-green-600 dark:text-green-400 mb-1">
                            {t("Income")}: {formatCurrency(data?.income || 0, language)}
                        </p>
                        {data?.actualIncome > 0 && (
                            <p className="text-xs text-green-500 ml-2">
                                {t("Actual")}: {formatCurrency(data.actualIncome, language)}
                            </p>
                        )}
                        {data?.expectedIncome > 0 && (
                            <p className="text-xs text-green-400 ml-2">
                                {t("Expected")}: {formatCurrency(data.expectedIncome, language)}
                            </p>
                        )}
                    </div>

                    {/* Expense breakdown */}
                    <div className="mb-2">
                        <p className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">
                            {t("Expense")}: {formatCurrency(data?.expense || 0, language)}
                        </p>
                        {data?.actualExpense > 0 && (
                            <p className="text-xs text-red-500 ml-2">
                                {t("Actual")}: {formatCurrency(data.actualExpense, language)}
                            </p>
                        )}
                        {data?.expectedExpense > 0 && (
                            <p className="text-xs text-red-400 ml-2">
                                {t("Expected")}: {formatCurrency(data.expectedExpense, language)}
                            </p>
                        )}
                    </div>

                    {/* Net calculation */}
                    <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                        <p className={`text-sm font-medium ${
                            (data?.income - data?.expense) >= 0 ? 'text-blue-600' : 'text-orange-600'
                        }`}>
                            {t("Net")}: {formatCurrency((data?.income || 0) - (data?.expense || 0), language)}
                        </p>
                    </div>
                </div>
            );
        }
        return null;
    };

    // Handle bar click
    const handleBarClick = (data: any) => {
        const { month, year } = data;
        window.location.href = `/finances?month=${month + 1}&year=${year}`;
    };

    // Render trend indicator
    const TrendIndicator = ({ value, label }: { value: number; label: string }) => {
        const isPositive = value > 0;
        const isNeutral = Math.abs(value) < 1;

        return (
            <div className="flex flex-col items-center text-center min-w-0">
                <div className="flex items-center justify-center mb-1">
                    {isNeutral ? (
                        <Minus className="w-4 h-4 text-gray-500" />
                    ) : isPositive ? (
                        <TrendingUp className="w-4 h-4 text-green-500" />
                    ) : (
                        <TrendingDown className="w-4 h-4 text-red-500" />
                    )}
                </div>
                <span className={`text-xs font-medium whitespace-nowrap ${
                    isNeutral ? 'text-gray-500' : isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                    {Math.abs(value).toFixed(1)}%
                </span>
                {label && (
                    <span className="text-xs text-gray-400 whitespace-nowrap">
                        {label}
                    </span>
                )}
            </div>
        );
    };

    if (!events || events.length === 0) {
        return (
            <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                <div className="text-center py-12">
                    <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        {t("No Data Available")}
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                        {t("Add some financial events to see analytics")}
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            {/* Header with Summary Stats */}
            <div className="p-6 border-b dark:border-gray-700">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                    <h2 className="text-xl font-semibold dark:text-white mb-4 lg:mb-0">
                        {t("Financial Analytics")}
                    </h2>

                    {/* Summary Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6">
                        <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-lg">
                            <div className="flex flex-col space-y-2">
                                <div className="flex items-center justify-between">
                                    <p className="text-sm text-green-600 dark:text-green-400">{t("Total Income")}</p>
                                    <TrendIndicator value={summaryStats.incomeGrowth} label="" />
                                </div>
                                <div className="flex flex-col">
                                    <p className="text-xl font-bold text-green-700 dark:text-green-300 break-words" title={formatCurrency(summaryStats.totalIncome, language)}>
                                        {formatCurrencyCompact(summaryStats.totalIncome, language)}
                                    </p>
                                    <p className="text-xs text-green-600/70 dark:text-green-400/70 mt-1">
                                        {formatCurrency(summaryStats.totalIncome, language)}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-4 rounded-lg">
                            <div className="flex flex-col space-y-2">
                                <div className="flex items-center justify-between">
                                    <p className="text-sm text-red-600 dark:text-red-400">{t("Total Expense")}</p>
                                    <TrendIndicator value={summaryStats.expenseGrowth} label="" />
                                </div>
                                <div className="flex flex-col">
                                    <p className="text-xl font-bold text-red-700 dark:text-red-300 break-words" title={formatCurrency(summaryStats.totalExpense, language)}>
                                        {formatCurrencyCompact(summaryStats.totalExpense, language)}
                                    </p>
                                    <p className="text-xs text-red-600/70 dark:text-red-400/70 mt-1">
                                        {formatCurrency(summaryStats.totalExpense, language)}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className={`bg-gradient-to-r p-4 rounded-lg ${
                            summaryStats.netProfit >= 0
                                ? 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20'
                                : 'from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20'
                        }`}>
                            <div className="flex flex-col space-y-2">
                                <div className="flex items-center justify-between">
                                    <p className={`text-sm ${
                                        summaryStats.netProfit >= 0
                                            ? 'text-blue-600 dark:text-blue-400'
                                            : 'text-orange-600 dark:text-orange-400'
                                    }`}>
                                        {t("Net Profit")}
                                    </p>
                                    <div className="text-right">
                                        <p className="text-xs text-gray-500 dark:text-gray-400">{t("Margin")}</p>
                                        <p className="text-sm font-medium">
                                            {summaryStats.profitMargin.toFixed(1)}%
                                        </p>
                                    </div>
                                </div>
                                <div className="flex flex-col">
                                    <p className={`text-xl font-bold break-words ${
                                        summaryStats.netProfit >= 0
                                            ? 'text-blue-700 dark:text-blue-300'
                                            : 'text-orange-700 dark:text-orange-300'
                                    }`} title={formatCurrency(summaryStats.netProfit, language)}>
                                        {formatCurrencyCompact(summaryStats.netProfit, language)}
                                    </p>
                                    <p className={`text-xs mt-1 ${
                                        summaryStats.netProfit >= 0
                                            ? 'text-blue-600/70 dark:text-blue-400/70'
                                            : 'text-orange-600/70 dark:text-orange-400/70'
                                    }`}>
                                        {formatCurrency(summaryStats.netProfit, language)}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Controls */}
                <div className="flex flex-wrap gap-4">
                    {/* View Mode Toggle */}
                    <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                        <button
                            onClick={() => setViewMode("previous")}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                                viewMode === "previous"
                                    ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                            }`}
                        >
                            {t("Previous Months")}
                        </button>
                        <button
                            onClick={() => setViewMode("upcoming")}
                            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                                viewMode === "upcoming"
                                    ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                            }`}
                        >
                            {t("Upcoming Months")}
                        </button>
                    </div>

                    {/* Chart Type Toggle */}
                    <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                        <button
                            onClick={() => setChartType("bar")}
                            className={`px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1 ${
                                chartType === "bar"
                                    ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                            }`}
                        >
                            <BarChart3 className="w-4 h-4" />
                            <span>{t("Bar")}</span>
                        </button>
                        <button
                            onClick={() => setChartType("line")}
                            className={`px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1 ${
                                chartType === "line"
                                    ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                            }`}
                        >
                            <LineChartIcon className="w-4 h-4" />
                            <span>{t("Line")}</span>
                        </button>
                        <button
                            onClick={() => setChartType("area")}
                            className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                                chartType === "area"
                                    ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                            }`}
                        >
                            {t("Area")}
                        </button>
                    </div>

                    {/* Filters */}
                    <select
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        value={selectedYear || ""}
                        onChange={e => setSelectedYear(e.target.value ? Number(e.target.value) : new Date().getFullYear())}
                    >
                        <option value="">{t("All Years")}</option>
                        {years.map(year => (
                            <option key={year} value={year}>{year}</option>
                        ))}
                    </select>

                    <select
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        value={selectedMonth ?? ""}
                        onChange={e => setSelectedMonth(e.target.value ? Number(e.target.value) : null)}
                    >
                        <option value="">{t("All Months")}</option>
                        {months.map(month => (
                            <option key={month.value} value={month.value}>{month.label}</option>
                        ))}
                    </select>
                </div>
            </div>

            {/* Chart */}
            <div className="p-6">
                <div className="h-96 w-full overflow-hidden">
                    <ResponsiveContainer width="100%" height="100%">
                        {chartType === "bar" ? (
                            <BarChart
                                data={filteredData}
                                margin={{
                                    top: 20,
                                    right: 30,
                                    left: language === 'ar' ? 50 : 40,
                                    bottom: language === 'ar' ? 80 : 60
                                }}
                                onClick={(e) => e.activePayload && handleBarClick(e.activePayload[0].payload)}
                            >
                                <defs>
                                    <linearGradient id="incomeGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={CHART_COLORS.income} stopOpacity={0.8}/>
                                        <stop offset="95%" stopColor={CHART_COLORS.income} stopOpacity={0.3}/>
                                    </linearGradient>
                                    <linearGradient id="expenseGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={CHART_COLORS.expense} stopOpacity={0.8}/>
                                        <stop offset="95%" stopColor={CHART_COLORS.expense} stopOpacity={0.3}/>
                                    </linearGradient>
                                </defs>
                                <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                <XAxis
                                    dataKey="name"
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{
                                        fontSize: 11,
                                        fill: '#6b7280',
                                        textAnchor: language === 'ar' ? 'start' : 'end',
                                        dy: language === 'ar' ? 10 : 0
                                    }}
                                    angle={-45}
                                    textAnchor={language === 'ar' ? 'start' : 'end'}
                                    height={60}
                                    interval={0}
                                />
                                <YAxis
                                    tickFormatter={(value) => formatNumber(value, language)}
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{ fontSize: 11, fill: '#6b7280' }}
                                    width={60}
                                />
                                <Tooltip content={<CustomTooltip />} />
                                <Legend
                                    wrapperStyle={{ paddingTop: '20px' }}
                                    iconType="rect"
                                />
                                <Bar
                                    dataKey="income"
                                    fill="url(#incomeGradient)"
                                    name={t("Income")}
                                    radius={[4, 4, 0, 0]}
                                    maxBarSize={60}
                                />
                                <Bar
                                    dataKey="expense"
                                    fill="url(#expenseGradient)"
                                    name={t("Expense")}
                                    radius={[4, 4, 0, 0]}
                                    maxBarSize={60}
                                />
                            </BarChart>
                        ) : chartType === "line" ? (
                            <LineChart
                                data={filteredData}
                                margin={{
                                    top: 20,
                                    right: 30,
                                    left: language === 'ar' ? 50 : 40,
                                    bottom: language === 'ar' ? 80 : 60
                                }}
                            >
                                <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                <XAxis
                                    dataKey="name"
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{
                                        fontSize: 11,
                                        fill: '#6b7280',
                                        textAnchor: language === 'ar' ? 'start' : 'end',
                                        dy: language === 'ar' ? 10 : 0
                                    }}
                                    angle={-45}
                                    textAnchor={language === 'ar' ? 'start' : 'end'}
                                    height={60}
                                    interval={0}
                                />
                                <YAxis
                                    tickFormatter={(value) => formatNumber(value, language)}
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{ fontSize: 11, fill: '#6b7280' }}
                                    width={60}
                                />
                                <Tooltip content={<CustomTooltip />} />
                                <Legend
                                    wrapperStyle={{ paddingTop: '20px' }}
                                    iconType="line"
                                />
                                <Line
                                    type="monotone"
                                    dataKey="income"
                                    stroke={CHART_COLORS.income}
                                    strokeWidth={3}
                                    dot={{ fill: CHART_COLORS.income, strokeWidth: 2, r: 4 }}
                                    activeDot={{ r: 6, stroke: CHART_COLORS.income, strokeWidth: 2 }}
                                    name={t("Income")}
                                />
                                <Line
                                    type="monotone"
                                    dataKey="expense"
                                    stroke={CHART_COLORS.expense}
                                    strokeWidth={3}
                                    dot={{ fill: CHART_COLORS.expense, strokeWidth: 2, r: 4 }}
                                    activeDot={{ r: 6, stroke: CHART_COLORS.expense, strokeWidth: 2 }}
                                    name={t("Expense")}
                                />
                            </LineChart>
                        ) : (
                            <AreaChart
                                data={filteredData}
                                margin={{
                                    top: 20,
                                    right: 30,
                                    left: language === 'ar' ? 50 : 40,
                                    bottom: language === 'ar' ? 80 : 60
                                }}
                            >
                                <defs>
                                    <linearGradient id="incomeAreaGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={CHART_COLORS.income} stopOpacity={0.6}/>
                                        <stop offset="95%" stopColor={CHART_COLORS.income} stopOpacity={0.1}/>
                                    </linearGradient>
                                    <linearGradient id="expenseAreaGradient" x1="0" y1="0" x2="0" y2="1">
                                        <stop offset="5%" stopColor={CHART_COLORS.expense} stopOpacity={0.6}/>
                                        <stop offset="95%" stopColor={CHART_COLORS.expense} stopOpacity={0.1}/>
                                    </linearGradient>
                                </defs>
                                <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                <XAxis
                                    dataKey="name"
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{
                                        fontSize: 11,
                                        fill: '#6b7280',
                                        textAnchor: language === 'ar' ? 'start' : 'end',
                                        dy: language === 'ar' ? 10 : 0
                                    }}
                                    angle={-45}
                                    textAnchor={language === 'ar' ? 'start' : 'end'}
                                    height={60}
                                    interval={0}
                                />
                                <YAxis
                                    tickFormatter={(value) => formatNumber(value, language)}
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{ fontSize: 11, fill: '#6b7280' }}
                                    width={60}
                                />
                                <Tooltip content={<CustomTooltip />} />
                                <Legend
                                    wrapperStyle={{ paddingTop: '20px' }}
                                    iconType="rect"
                                />
                                <Area
                                    type="monotone"
                                    dataKey="income"
                                    stackId="1"
                                    stroke={CHART_COLORS.income}
                                    fill="url(#incomeAreaGradient)"
                                    strokeWidth={2}
                                    name={t("Income")}
                                />
                                <Area
                                    type="monotone"
                                    dataKey="expense"
                                    stackId="2"
                                    stroke={CHART_COLORS.expense}
                                    fill="url(#expenseAreaGradient)"
                                    strokeWidth={2}
                                    name={t("Expense")}
                                />
                            </AreaChart>
                        )}
                    </ResponsiveContainer>
                </div>

                {/* Additional Insights */}
                {filteredData.length > 0 && (
                    <div className="mt-6 pt-6 border-t dark:border-gray-700">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Average Income")}</p>
                                <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                                    {formatCurrency(summaryStats.totalIncome / filteredData.length, language)}
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Average Expense")}</p>
                                <p className="text-lg font-semibold text-red-600 dark:text-red-400">
                                    {formatCurrency(summaryStats.totalExpense / filteredData.length, language)}
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Best Month")}</p>
                                <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                                    {filteredData.reduce((best, current) =>
                                        current.difference > best.difference ? current : best,
                                        filteredData[0]
                                    )?.name || '-'}
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Data Points")}</p>
                                <p className="text-lg font-semibold text-gray-600 dark:text-gray-400">
                                    {filteredData.length} {t("months")}
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AnalyticsSection;
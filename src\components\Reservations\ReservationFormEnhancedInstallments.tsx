import React, { useState, useEffect } from "react";
import { ReservationFormData } from "@/lib/interfaces/reservation";
import { EventDetails } from "@/lib/interfaces/finaces";
import {
  FaPlus,
  FaTrash,
  FaInfoCircle,
  FaChevronDown,
  FaEdit,
  FaSave,
  FaTimes,
} from "react-icons/fa";
import { useReservationEvents } from "@/hooks/useReservations";
import { useIncomeTypes } from "@/hooks/useIncomeTypes";
import { useExpenseTypes } from "@/hooks/useExpensesTypes";
import useLanguage from "@/hooks/useLanguage";

interface ReservationFormEnhancedInstallmentsProps {
  formData: ReservationFormData;
  setFormData: React.Dispatch<React.SetStateAction<ReservationFormData>>;
  t: (key: string) => string;
  reservationId?: string;
  // Add props for managing financial events locally
  incomeEvents: EventDetails[];
  expenseEvents: EventDetails[];
  onIncomeEventsChange: (events: EventDetails[]) => void;
  onExpenseEventsChange: (events: EventDetails[]) => void;
  validateTotalAmount?: () => { isValid: boolean; message?: string };
}

interface EditingEvent {
  id?: string;
  title: string;
  description: string;
  amount: number;
  dueDate: string;
  status: "pending" | "completed" | "overdue" | "upcoming" | "cancelled";
  type: string;
  category: "income" | "expense";
  isNew?: boolean;
  priority?: string;
}

const ReservationFormEnhancedInstallments: React.FC<
  ReservationFormEnhancedInstallmentsProps
> = ({
  formData,
  t,
  reservationId,
  incomeEvents,
  expenseEvents,
  onIncomeEventsChange,
  onExpenseEventsChange,
  validateTotalAmount,
}) => {
  const { language } = useLanguage();

  const [dropdownOpenId, setDropdownOpenId] = useState<string | null>(null);

  // Add editing states
  const [editingEvent, setEditingEvent] = useState<EditingEvent | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  // Date picker states for completed status
  const [selectedCompletedEvent, setSelectedCompletedEvent] = useState<
    string | null
  >(null);
  const [dateValue, setDateValue] = useState("");

  // Use the hook to fetch reservation events for initial data
  const { data: reservationEventsData, isLoading: isLoadingFinancialData } =
    useReservationEvents(reservationId);

  // Fetch income and expense types for proper UUIDs
  const { data: incomeTypesData } = useIncomeTypes();
  const { data: expenseTypesData } = useExpenseTypes();

  // Initialize financial events when data is fetched
  useEffect(() => {
    if (
      reservationEventsData &&
      incomeEvents.length === 0 &&
      expenseEvents.length === 0
    ) {
      console.log("Initializing financial events data:", reservationEventsData);

      if (
        reservationEventsData.incomes &&
        Array.isArray(reservationEventsData.incomes)
      ) {
        const typedIncomes = reservationEventsData.incomes.map(
          (income: any) => ({
            id: income.id || `income-${Date.now()}-${Math.random()}`,
            title: income.title || "",
            amount: Number(income.amount) || 0,
            dueDate: income.dueDate || income.due_date || "",
            status: income.status || "upcoming",
            type: income.type || "Payment",
            description: income.description || "",
            priority: income.priority || "medium",
            category: "income",
            lastEdited: income.lastEdited || new Date().toISOString(),
            lastEditedBy: income.lastEditedBy || "system",
            reservation_id: reservationId,
          }),
        );
        onIncomeEventsChange(typedIncomes as EventDetails[]);
      }

      if (
        reservationEventsData.expenses &&
        Array.isArray(reservationEventsData.expenses)
      ) {
        const typedExpenses = reservationEventsData.expenses.map(
          (expense: any) => ({
            id: expense.id || `expense-${Date.now()}-${Math.random()}`,
            title: expense.title || "",
            amount: Number(expense.amount) || 0,
            dueDate: expense.dueDate || expense.due_date || "",
            status: expense.status || "upcoming",
            type: expense.type || "Expense",
            description: expense.description || "",
            priority: expense.priority || "medium",
            category: "expense",
            lastEdited: expense.lastEdited || new Date().toISOString(),
            lastEditedBy: expense.lastEditedBy || "system",
            reservation_id: reservationId,
            income_id: expense.income_id || null, // Include income_id for dependent expenses
          }),
        );

        onExpenseEventsChange(typedExpenses as EventDetails[]);
      }
    }
  }, [
    reservationEventsData,
    incomeEvents.length,
    expenseEvents.length,
    onIncomeEventsChange,
    onExpenseEventsChange,
    reservationId,
  ]);

  // Status options for dropdown
  const statusOptions = [
    "completed",
    "pending",
    "upcoming",
    "overdue",
    "cancelled",
  ];

  // Helper functions to get type UUIDs
  const getIncomeTypeId = (typeName: string): string => {
    if (!incomeTypesData?.income_types) return "default-income-type-uuid";
    const type = incomeTypesData.income_types.find((t) => t.name === typeName);
    return (
      type?.id ||
      incomeTypesData.income_types[0]?.id ||
      "default-income-type-uuid"
    );
  };

  const getExpenseTypeId = (typeName: string): string => {
    if (!expenseTypesData?.event_types) return "default-expense-type-uuid";
    const type = expenseTypesData.event_types.find((t) => t.name === typeName);
    return (
      type?.id ||
      expenseTypesData.event_types[0]?.id ||
      "default-expense-type-uuid"
    );
  };

  // Type options for events (display names)
  const incomeTypeOptions = incomeTypesData?.income_types || [];
  const expenseTypeOptions = expenseTypesData?.event_types || [];

  // Helper functions for formatting
  const formatCurrency = (amount: number | undefined) => {
    return amount !== undefined && amount !== null
      ? new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
          style: "currency",
          currency: "EGP",
          minimumFractionDigits: 0,
        }).format(amount)
      : "N/A";
  };

  const formatDate = (dateString: string | undefined) => {
    return dateString
      ? new Date(dateString).toLocaleDateString(
          language === "ar" ? "ar-EG" : "en-US",
        )
      : "N/A";
  };

  // Handle event status change (local only - no API call)
  const handleEventStatusChange = (
    eventId: string,
    newStatus: string,
    type: "income" | "expense",
  ) => {
    if (!eventId) return;

    // If status is changing to "completed", show date picker
    if (newStatus === "completed") {
      setSelectedCompletedEvent(eventId);
      setDateValue(""); // Reset date value
      return; // Don't change status yet, wait for date selection
    } else {
      // For other status changes, just update status
      if (type === "income") {
        const updatedIncomes = incomeEvents.map((ev) =>
          ev.id === eventId
            ? { ...ev, status: newStatus as EventDetails["status"] }
            : ev,
        );
        onIncomeEventsChange(updatedIncomes);
      } else {
        const updatedExpenses = expenseEvents.map((ev) =>
          ev.id === eventId
            ? { ...ev, status: newStatus as EventDetails["status"] }
            : ev,
        );
        onExpenseEventsChange(updatedExpenses);
      }
      setDropdownOpenId(null); // Close dropdown for non-completed status changes
    }
  };

  // Handle status update with date for completed events
  const handleCompletedStatusUpdate = (
    eventId: string,
    type: "income" | "expense",
  ) => {
    if (!dateValue) {
      alert(
        type === "income"
          ? t("Please select received date")
          : t("Please select paid date"),
      );
      return;
    }

    // Update with status and date
    if (type === "income") {
      const updatedIncomes = incomeEvents.map((ev) =>
        ev.id === eventId
          ? {
              ...ev,
              status: "completed" as EventDetails["status"],
              received_date: dateValue,
            }
          : ev,
      );
      onIncomeEventsChange(updatedIncomes);
    } else {
      const updatedExpenses = expenseEvents.map((ev) =>
        ev.id === eventId
          ? {
              ...ev,
              status: "completed" as EventDetails["status"],
              paid_date: dateValue,
            }
          : ev,
      );
      onExpenseEventsChange(updatedExpenses);
    }

    // Reset states and close dropdown
    setSelectedCompletedEvent(null);
    setDateValue("");
    setDropdownOpenId(null);
  };

  // CRUD functions for editing events (local state only)
  const handleAddNew = (category: "income" | "expense") => {
    // Get the first available type for the category
    const defaultType =
      category === "income"
        ? incomeTypeOptions[0]?.name || "Payment"
        : expenseTypeOptions[0]?.name || "Other";

    const newEvent: EditingEvent = {
      title: `${formData.title || "Reservation"} - ${category === "income" ? t("Payment") : t("Expense")}`,
      description: "",
      amount: 0,
      dueDate: new Date().toISOString().split("T")[0],
      status: "pending",
      type: defaultType,
      category: category,
      isNew: true,
      priority: "medium",
    };
    setEditingEvent(newEvent);
    setIsAddingNew(true);
  };

  const handleEdit = (event: EventDetails, category: "income" | "expense") => {
    const editEvent: EditingEvent = {
      id: event.id,
      title: event.title,
      description: event.description || "",
      amount: Number(event.amount),
      dueDate: event.dueDate.split("T")[0],
      status: event.status || "pending",
      type: event.type || (category === "income" ? "Payment" : "Other"),
      category: category,
      isNew: false,
      priority: event.priority || "medium",
    };
    setEditingEvent(editEvent);
    setIsAddingNew(false);
  };

  const handleSave = () => {
    if (!editingEvent) return;

    if (editingEvent.category === "income") {
      if (editingEvent.isNew) {
        const newIncomeEvent: EventDetails = {
          id: `temp_${Date.now()}`,
          title: editingEvent.title,
          description: editingEvent.description,
          amount: editingEvent.amount,
          dueDate: editingEvent.dueDate,
          status: editingEvent.status,
          type: editingEvent.type,
          category: "income",
          priority: (editingEvent.priority || "medium") as
            | "low"
            | "medium"
            | "high",
          lastEdited: new Date().toISOString(),
          lastEditedBy: "Admin",
          reservation_id: reservationId,
          // Add fields needed for API payload
          received_date:
            editingEvent.status === "completed"
              ? new Date().toISOString()
              : undefined,
        };
        onIncomeEventsChange([...incomeEvents, newIncomeEvent]);
      } else {
        const updatedIncomes = incomeEvents.map((e) =>
          e.id === editingEvent.id
            ? {
                ...e,
                title: editingEvent.title,
                description: editingEvent.description,
                amount: editingEvent.amount,
                dueDate: editingEvent.dueDate,
                status: editingEvent.status,
                type: editingEvent.type,
                priority: (editingEvent.priority || "medium") as
                  | "low"
                  | "medium"
                  | "high",
                received_date:
                  editingEvent.status === "completed"
                    ? e.received_date || new Date().toISOString()
                    : e.received_date,
              }
            : e,
        );
        onIncomeEventsChange(updatedIncomes);
      }
    } else {
      if (editingEvent.isNew) {
        const newExpenseEvent: EventDetails = {
          id: `temp_${Date.now()}`,
          title: editingEvent.title,
          description: editingEvent.description,
          amount: editingEvent.amount,
          dueDate: editingEvent.dueDate,
          status: editingEvent.status,
          type: editingEvent.type,
          category: "expense",
          priority: (editingEvent.priority || "medium") as
            | "low"
            | "medium"
            | "high",
          lastEdited: new Date().toISOString(),
          lastEditedBy: "Admin",
          reservation_id: reservationId,
          // Add fields needed for API payload
          paid_date:
            editingEvent.status === "completed"
              ? new Date().toISOString()
              : undefined,
        };
        onExpenseEventsChange([...expenseEvents, newExpenseEvent]);
      } else {
        const updatedExpenses = expenseEvents.map((e) =>
          e.id === editingEvent.id
            ? {
                ...e,
                title: editingEvent.title,
                description: editingEvent.description,
                amount: editingEvent.amount,
                dueDate: editingEvent.dueDate,
                status: editingEvent.status,
                type: editingEvent.type,
                priority: (editingEvent.priority || "medium") as
                  | "low"
                  | "medium"
                  | "high",
                paid_date:
                  editingEvent.status === "completed"
                    ? e.paid_date || new Date().toISOString()
                    : e.paid_date,
              }
            : e,
        );
        onExpenseEventsChange(updatedExpenses);
      }
    }

    setEditingEvent(null);
    setIsAddingNew(false);
  };

  const handleDelete = (eventId: string, category: "income" | "expense") => {
    if (!window.confirm(t("Are you sure you want to delete this event?"))) {
      return;
    }

    if (category === "income") {
      const updatedIncomes = incomeEvents.map((e) =>
        e.id === eventId ? { ...e, is_deleted: true } : e,
      );
      onIncomeEventsChange(updatedIncomes);
    } else {
      const updatedExpenses = expenseEvents.map((e) =>
        e.id === eventId ? { ...e, is_deleted: true } : e,
      );
      onExpenseEventsChange(updatedExpenses);
    }
  };

  const handleCancel = () => {
    setEditingEvent(null);
    setIsAddingNew(false);
  };

  // Use the props data instead of local state
  const visibleIncomes = incomeEvents.filter((item: any) => !item.is_deleted);
  const visibleExpenses = expenseEvents.filter((item: any) => !item.is_deleted);

  // Helper function to categorize expenses based on income_id
  const getExpensesByCategory = () => {
    const relatedExpenses: EventDetails[] = [];
    const independentExpenses: EventDetails[] = [];

    visibleExpenses.forEach((expense) => {
      // Check if this expense has an income_id that matches any income in this reservation
      const hasMatchingIncome = visibleIncomes.some(
        (income) => income.id === expense.income_id,
      );

      if (expense.income_id && hasMatchingIncome) {
        relatedExpenses.push(expense);
      } else {
        independentExpenses.push(expense);
      }
    });

    return { relatedExpenses, independentExpenses };
  };

  const { independentExpenses } = getExpensesByCategory();

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "active":
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
      case "upcoming":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-100";
      case "overdue":
        return "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  // Show loading state while fetching initial data
  if (isLoadingFinancialData) {
    return (
      <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900 sm:p-6">
        <div className="flex items-center justify-center p-8">
          <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">
            {t("Loading financial events...")}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900 sm:p-6">
      <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
          {t("Reservation Financial Events")}
        </h3>
      </div>

      {/* Total Amount Validation Status */}
      {validateTotalAmount &&
        (() => {
          const validation = validateTotalAmount();
          const installmentTotal = formData.installments.reduce(
            (sum, installment) => sum + installment.amount,
            0,
          );
          const additionalIncomeTotal = incomeEvents
            .filter((event) => !event.is_deleted)
            .reduce((sum, event) => sum + event.amount, 0);
          const totalIncomeAmount = installmentTotal + additionalIncomeTotal;

          return (
            <div
              className={`mb-4 rounded-lg border p-3 ${
                validation.isValid
                  ? "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20"
                  : "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20"
              }`}
            >
              <div className="flex items-center gap-2">
                {validation.isValid ? (
                  <FaInfoCircle className="text-green-600 dark:text-green-400" />
                ) : (
                  <FaInfoCircle className="text-red-600 dark:text-red-400" />
                )}
                <div className="flex-1">
                  <div
                    className={`text-sm font-medium ${
                      validation.isValid
                        ? "text-green-800 dark:text-green-200"
                        : "text-red-800 dark:text-red-200"
                    }`}
                  >
                    {validation.isValid
                      ? t("Total Amount Validation: ✓ Valid")
                      : t("Total Amount Validation: ✗ Invalid")}
                  </div>
                  <div className="mt-1 text-xs text-gray-600 dark:text-gray-400">
                    {t("Reservation Total")}:{" "}
                    {Number(formData.totalAmount || 0).toFixed(2)} |{" "}
                    {t("Income Total")}: {totalIncomeAmount.toFixed(2)}
                    {!validation.isValid && validation.message && (
                      <div className="mt-1 text-red-600 dark:text-red-400">
                        {validation.message}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })()}

      <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        {/* Add New Buttons */}
        <div className="flex gap-2">
          <button
            onClick={() => handleAddNew("income")}
            disabled={!!editingEvent}
            className="flex items-center space-x-2 rounded-lg bg-green-600 px-3 py-2 text-sm text-white hover:bg-green-700 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <FaPlus className="h-3 w-3" />
            <span>{t("Add Income")}</span>
          </button>
          <button
            onClick={() => handleAddNew("expense")}
            disabled={!!editingEvent}
            className="flex items-center space-x-2 rounded-lg bg-red-600 px-3 py-2 text-sm text-white hover:bg-red-700 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <FaPlus className="h-3 w-3" />
            <span>{t("Add Expense")}</span>
          </button>
        </div>
      </div>

      {/* Legend */}
      <div className="mb-4 rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
        <h4 className="mb-2 text-sm font-semibold text-gray-800 dark:text-gray-200">
          {t("Legend")}:
        </h4>
        <div className="flex flex-wrap gap-4 text-xs">
          <div className="flex items-center gap-1">
            <div className="h-3 w-3 rounded bg-green-100"></div>
            <span>{t("Income")}</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="h-3 w-3 rounded bg-red-100"></div>
            <span>{t("Expense")}</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-blue-600">└─</span>
            <span>{t("Expense linked to income")}</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="text-orange-600">⚪</span>
            <span>{t("Independent expense")}</span>
          </div>
        </div>
      </div>

      {/* Show data counts */}
      <div className="mb-4 rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-green-600">
              {t("Total Incomes")}:{" "}
            </span>
            <span className="text-gray-900 dark:text-white">
              {visibleIncomes.length}
            </span>
          </div>
          <div>
            <span className="font-medium text-red-600">
              {t("Total Expenses")}:{" "}
            </span>
            <span className="text-gray-900 dark:text-white">
              {visibleExpenses.length}
            </span>
          </div>
        </div>
      </div>

      {/* Editing Form */}
      {editingEvent && (
        <div className="mb-6 rounded-lg border border-gray-300 bg-white p-4 shadow-sm dark:border-gray-600 dark:bg-gray-800">
          <h4 className="text-md mb-4 font-medium text-gray-900 dark:text-white">
            {isAddingNew
              ? t(
                  `Add New ${editingEvent.category === "income" ? "Income" : "Expense"}`,
                )
              : t("Edit Event")}
          </h4>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Title")}
              </label>
              <input
                type="text"
                value={editingEvent.title}
                onChange={(e) =>
                  setEditingEvent({ ...editingEvent, title: e.target.value })
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Type")}
              </label>
              <select
                value={editingEvent.type}
                onChange={(e) =>
                  setEditingEvent({ ...editingEvent, type: e.target.value })
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                {(editingEvent.category === "income"
                  ? incomeTypeOptions
                  : expenseTypeOptions
                ).map((type) => (
                  <option key={type.id} value={type.name}>
                    {t(type.name)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Amount")}
              </label>
              <input
                type="number"
                value={editingEvent.amount}
                onChange={(e) =>
                  setEditingEvent({
                    ...editingEvent,
                    amount: Number(e.target.value),
                  })
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Due Date")}
              </label>
              <input
                type="date"
                value={editingEvent.dueDate}
                onChange={(e) =>
                  setEditingEvent({ ...editingEvent, dueDate: e.target.value })
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Status")}
              </label>
              <select
                value={editingEvent.status}
                onChange={(e) =>
                  setEditingEvent({
                    ...editingEvent,
                    status: e.target.value as any,
                  })
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                {statusOptions.map((status) => (
                  <option key={status} value={status}>
                    {t(status)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Priority")}
              </label>
              <select
                value={editingEvent.priority}
                onChange={(e) =>
                  setEditingEvent({ ...editingEvent, priority: e.target.value })
                }
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="low">{t("Low")}</option>
                <option value="medium">{t("Medium")}</option>
                <option value="high">{t("High")}</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("Description")}
              </label>
              <textarea
                value={editingEvent.description}
                onChange={(e) =>
                  setEditingEvent({
                    ...editingEvent,
                    description: e.target.value,
                  })
                }
                rows={2}
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div className="mt-4 flex space-x-2">
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 rounded-lg bg-green-600 px-4 py-2 text-white hover:bg-green-700"
            >
              <FaSave className="h-4 w-4" />
              <span>{t("Save")}</span>
            </button>
            <button
              onClick={handleCancel}
              className="flex items-center space-x-2 rounded-lg bg-gray-600 px-4 py-2 text-white hover:bg-gray-700"
            >
              <FaTimes className="h-4 w-4" />
              <span>{t("Cancel")}</span>
            </button>
          </div>
        </div>
      )}

      {visibleIncomes.length > 0 || visibleExpenses.length > 0 ? (
        <div className="space-y-4">
          {/* Render incomes with their related expenses */}
          {visibleIncomes.map((income: any, incomeIndex) => {
            return (
              <div
                key={income.id || `income-${incomeIndex}`}
                className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
              >
                {/* Income Header */}
                <div className="bg-green-50 p-3 dark:bg-green-900/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-semibold text-green-800 dark:bg-green-800 dark:text-green-100">
                        {t("Income")}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {income.title || t("Untitled Income")}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* Edit and Delete buttons */}
                      <button
                        onClick={() => handleEdit(income, "income")}
                        disabled={!!editingEvent}
                        className="rounded p-1 text-blue-600 hover:bg-blue-100 disabled:cursor-not-allowed disabled:opacity-50"
                        title={t("Edit")}
                      >
                        <FaEdit className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => handleDelete(income.id, "income")}
                        disabled={!!editingEvent}
                        className="rounded p-1 text-red-600 hover:bg-red-100 disabled:cursor-not-allowed disabled:opacity-50"
                        title={t("Delete")}
                      >
                        <FaTrash className="h-3 w-3" />
                      </button>

                      {/* Status dropdown for income */}
                      <div className="relative">
                        <button
                          className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(
                            income.status || "completed",
                          )}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            setDropdownOpenId(
                              income.id === dropdownOpenId ? null : income.id,
                            );
                          }}
                          type="button"
                        >
                          {t(
                            (income.status || "completed")
                              .charAt(0)
                              .toUpperCase() +
                              (income.status || "completed").slice(1),
                          )}
                          <FaChevronDown className="ml-1 h-3 w-3" />
                        </button>
                        {dropdownOpenId === income.id && (
                          <div className="absolute right-0 z-10 mt-1 min-w-[120px] rounded border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-900">
                            {statusOptions.map((status) => (
                              <button
                                key={status}
                                className={`block w-full px-3 py-2 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-800 ${
                                  income.status === status
                                    ? "font-bold text-blue-600 dark:text-blue-400"
                                    : "text-gray-700 dark:text-gray-200"
                                }`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEventStatusChange(
                                    income.id,
                                    status,
                                    "income",
                                  );
                                  if (status !== "completed") {
                                    setDropdownOpenId(null);
                                  }
                                }}
                              >
                                {t(status)}
                              </button>
                            ))}
                            {/* Date input for completed status */}
                            {selectedCompletedEvent === income.id && (
                              <div className="border-t border-gray-200 px-3 py-3 dark:border-gray-600">
                                <label className="mb-2 block text-xs font-medium text-gray-700 dark:text-gray-300">
                                  {t("Received Date")} *
                                </label>
                                <input
                                  type="date"
                                  value={dateValue}
                                  onChange={(e) => setDateValue(e.target.value)}
                                  className="mb-3 w-full rounded border border-gray-300 px-2 py-1.5 text-xs focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                  onClick={(e) => e.stopPropagation()}
                                />
                                <div className="flex gap-2">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleCompletedStatusUpdate(
                                        income.id,
                                        "income",
                                      );
                                    }}
                                    className="flex-1 rounded bg-primary px-2 py-1 text-xs text-white hover:bg-primary/90"
                                  >
                                    {t("Update Status")}
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedCompletedEvent(null);
                                      setDateValue("");
                                      setDropdownOpenId(null);
                                    }}
                                    className="flex-1 rounded bg-gray-500 px-2 py-1 text-xs text-white hover:bg-gray-600"
                                  >
                                    {t("Cancel")}
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Income Details */}
                <div className="grid grid-cols-1 gap-4 p-4 md:grid-cols-2 lg:grid-cols-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                      {t("Amount")}
                    </label>
                    <div className="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(income.amount)}
                    </div>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                      {t("Due Date")}
                    </label>
                    <div className="mt-1 text-sm text-gray-900 dark:text-white">
                      {formatDate(income.dueDate)}
                    </div>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                      {t("Priority")}
                    </label>
                    <div className="mt-1 text-sm capitalize text-gray-900 dark:text-white">
                      {income.priority || "medium"}
                    </div>
                  </div>
                  {income.received_date && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                        {t("Received Date")}
                      </label>
                      <div className="mt-1 text-sm text-gray-900 dark:text-white">
                        {formatDate(income.received_date)}
                      </div>
                    </div>
                  )}
                </div>

                {/* Related Expenses for this specific income */}
                {(() => {
                  const incomeRelatedExpenses = visibleExpenses.filter(
                    (expense) => expense.income_id === income.id,
                  );
                  return (
                    incomeRelatedExpenses.length > 0 && (
                      <div className="border-t border-gray-200 bg-red-50 dark:border-gray-700 dark:bg-red-900/20">
                        <div className="p-3">
                          <h5 className="mb-2 flex items-center gap-2 text-sm font-medium text-gray-800 dark:text-gray-200">
                            <span className="text-blue-600">└─</span>
                            {t("Related Expenses")} (
                            {incomeRelatedExpenses.length})
                          </h5>
                          <div className="space-y-3">
                            {incomeRelatedExpenses.map((expense: any) => (
                              <div
                                key={expense.id || `expense-${expense.id}`}
                                className="rounded border border-red-200 bg-white p-3 dark:border-red-700 dark:bg-gray-800"
                              >
                                <div className="mb-2 flex items-center justify-between">
                                  <span className="rounded-full bg-red-100 px-2 py-1 text-xs font-semibold text-red-800 dark:bg-red-800 dark:text-red-100">
                                    {t("Expense")}
                                  </span>
                                  <div className="flex items-center gap-2">
                                    {/* Edit and Delete buttons */}
                                    <button
                                      onClick={() =>
                                        handleEdit(expense, "expense")
                                      }
                                      disabled={!!editingEvent}
                                      className="rounded p-1 text-blue-600 hover:bg-blue-100 disabled:cursor-not-allowed disabled:opacity-50"
                                      title={t("Edit")}
                                    >
                                      <FaEdit className="h-3 w-3" />
                                    </button>
                                    <button
                                      onClick={() =>
                                        handleDelete(expense.id, "expense")
                                      }
                                      disabled={!!editingEvent}
                                      className="rounded p-1 text-red-600 hover:bg-red-100 disabled:cursor-not-allowed disabled:opacity-50"
                                      title={t("Delete")}
                                    >
                                      <FaTrash className="h-3 w-3" />
                                    </button>

                                    {/* Status dropdown for expense */}
                                    <div className="relative">
                                      <button
                                        className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(
                                          expense.status || "upcoming",
                                        )}`}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setDropdownOpenId(
                                            expense.id === dropdownOpenId
                                              ? null
                                              : expense.id,
                                          );
                                        }}
                                        type="button"
                                      >
                                        {t(
                                          (expense.status || "upcoming")
                                            .charAt(0)
                                            .toUpperCase() +
                                            (
                                              expense.status || "upcoming"
                                            ).slice(1),
                                        )}
                                        <FaChevronDown className="ml-1 h-3 w-3" />
                                      </button>
                                      {dropdownOpenId === expense.id && (
                                        <div className="absolute right-0 z-10 mt-1 min-w-[120px] rounded border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-900">
                                          {statusOptions.map((status) => (
                                            <button
                                              key={status}
                                              className={`block w-full px-3 py-2 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-800 ${
                                                expense.status === status
                                                  ? "font-bold text-blue-600 dark:text-blue-400"
                                                  : "text-gray-700 dark:text-gray-200"
                                              }`}
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleEventStatusChange(
                                                  expense.id,
                                                  status,
                                                  "expense",
                                                );
                                                if (status !== "completed") {
                                                  setDropdownOpenId(null);
                                                }
                                              }}
                                            >
                                              {t(status)}
                                            </button>
                                          ))}
                                          {/* Date input for completed status */}
                                          {selectedCompletedEvent ===
                                            expense.id && (
                                            <div className="border-t border-gray-200 px-3 py-3 dark:border-gray-600">
                                              <label className="mb-2 block text-xs font-medium text-gray-700 dark:text-gray-300">
                                                {t("Paid Date")} *
                                              </label>
                                              <input
                                                type="date"
                                                value={dateValue}
                                                onChange={(e) =>
                                                  setDateValue(e.target.value)
                                                }
                                                className="mb-3 w-full rounded border border-gray-300 px-2 py-1.5 text-xs focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                                onClick={(e) =>
                                                  e.stopPropagation()
                                                }
                                              />
                                              <div className="flex gap-2">
                                                <button
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleCompletedStatusUpdate(
                                                      expense.id,
                                                      "expense",
                                                    );
                                                  }}
                                                  className="flex-1 rounded bg-primary px-2 py-1 text-xs text-white hover:bg-primary/90"
                                                >
                                                  {t("Update Status")}
                                                </button>
                                                <button
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    setSelectedCompletedEvent(
                                                      null,
                                                    );
                                                    setDateValue("");
                                                    setDropdownOpenId(null);
                                                  }}
                                                  className="flex-1 rounded bg-gray-500 px-2 py-1 text-xs text-white hover:bg-gray-600"
                                                >
                                                  {t("Cancel")}
                                                </button>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                      {t("Title")}
                                    </label>
                                    <div className="mt-1 text-sm text-gray-900 dark:text-white">
                                      {expense.title || t("Untitled Expense")}
                                    </div>
                                  </div>
                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                      {t("Amount")}
                                    </label>
                                    <div className="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                                      {formatCurrency(expense.amount)}
                                    </div>
                                  </div>
                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                      {t("Due Date")}
                                    </label>
                                    <div className="mt-1 text-sm text-gray-900 dark:text-white">
                                      {formatDate(expense.dueDate)}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )
                  );
                })()}
              </div>
            );
          })}

          {/* Independent Expenses Section */}
          {independentExpenses.length > 0 && (
            <div className="rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-700 dark:bg-orange-900/20">
              <h4 className="mb-3 flex items-center gap-2 text-lg font-semibold text-gray-800 dark:text-gray-200">
                <span className="text-orange-600">⚪</span>
                {t("Independent Expenses")} ({independentExpenses.length})
              </h4>
              <div className="space-y-3">
                {independentExpenses.map((expense: any) => (
                  <div
                    key={expense.id || `independent-expense-${expense.id}`}
                    className="rounded border border-red-200 bg-white p-3 dark:border-red-700 dark:bg-gray-800"
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="rounded-full bg-red-100 px-2 py-1 text-xs font-semibold text-red-800 dark:bg-red-800 dark:text-red-100">
                        {t("Independent Expense")}
                      </span>
                      <div className="flex items-center gap-2">
                        {/* Edit and Delete buttons */}
                        <button
                          onClick={() => handleEdit(expense, "expense")}
                          disabled={!!editingEvent}
                          className="rounded p-1 text-blue-600 hover:bg-blue-100 disabled:cursor-not-allowed disabled:opacity-50"
                          title={t("Edit")}
                        >
                          <FaEdit className="h-3 w-3" />
                        </button>
                        <button
                          onClick={() => handleDelete(expense.id, "expense")}
                          disabled={!!editingEvent}
                          className="rounded p-1 text-red-600 hover:bg-red-100 disabled:cursor-not-allowed disabled:opacity-50"
                          title={t("Delete")}
                        >
                          <FaTrash className="h-3 w-3" />
                        </button>

                        {/* Status dropdown for independent expense */}
                        <div className="relative">
                          <button
                            className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(
                              expense.status || "upcoming",
                            )}`}
                            onClick={(e) => {
                              e.stopPropagation();
                              setDropdownOpenId(
                                expense.id === dropdownOpenId
                                  ? null
                                  : expense.id,
                              );
                            }}
                            type="button"
                          >
                            {t(
                              (expense.status || "upcoming")
                                .charAt(0)
                                .toUpperCase() +
                                (expense.status || "upcoming").slice(1),
                            )}
                            <FaChevronDown className="ml-1 h-3 w-3" />
                          </button>
                          {dropdownOpenId === expense.id && (
                            <div className="absolute right-0 z-10 mt-1 min-w-[120px] rounded border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-900">
                              {statusOptions.map((status) => (
                                <button
                                  key={status}
                                  className={`block w-full px-3 py-2 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-800 ${
                                    expense.status === status
                                      ? "font-bold text-blue-600 dark:text-blue-400"
                                      : "text-gray-700 dark:text-gray-200"
                                  }`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEventStatusChange(
                                      expense.id,
                                      status,
                                      "expense",
                                    );
                                    if (status !== "completed") {
                                      setDropdownOpenId(null);
                                    }
                                  }}
                                >
                                  {t(status)}
                                </button>
                              ))}
                              {/* Date input for completed status */}
                              {selectedCompletedEvent === expense.id && (
                                <div className="border-t border-gray-200 px-3 py-3 dark:border-gray-600">
                                  <label className="mb-2 block text-xs font-medium text-gray-700 dark:text-gray-300">
                                    {t("Paid Date")} *
                                  </label>
                                  <input
                                    type="date"
                                    value={dateValue}
                                    onChange={(e) =>
                                      setDateValue(e.target.value)
                                    }
                                    className="mb-3 w-full rounded border border-gray-300 px-2 py-1.5 text-xs focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                  <div className="flex gap-2">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCompletedStatusUpdate(
                                          expense.id,
                                          "expense",
                                        );
                                      }}
                                      className="flex-1 rounded bg-primary px-2 py-1 text-xs text-white hover:bg-primary/90"
                                    >
                                      {t("Update Status")}
                                    </button>
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedCompletedEvent(null);
                                        setDateValue("");
                                        setDropdownOpenId(null);
                                      }}
                                      className="flex-1 rounded bg-gray-500 px-2 py-1 text-xs text-white hover:bg-gray-600"
                                    >
                                      {t("Cancel")}
                                    </button>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                          {t("Title")}
                        </label>
                        <div className="mt-1 text-sm text-gray-900 dark:text-white">
                          {expense.title || t("Untitled Expense")}
                        </div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                          {t("Amount")}
                        </label>
                        <div className="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                          {formatCurrency(expense.amount)}
                        </div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                          {t("Due Date")}
                        </label>
                        <div className="mt-1 text-sm text-gray-900 dark:text-white">
                          {formatDate(expense.dueDate)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="rounded-lg border-2 border-dashed border-gray-300 p-8 text-center dark:border-gray-600">
          <FaInfoCircle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
            {t("No financial events yet")}
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {t("Financial events will appear here when available")}
          </p>
        </div>
      )}
    </div>
  );
};

export default ReservationFormEnhancedInstallments;

"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import useLanguage from "@/hooks/useLanguage";
import { useRouter } from 'next/navigation';
import { Location as LocationType } from '@/lib/types/location';
import { Skeleton } from "@/components/ui/skeleton";
import Breadcrumb from '../Breadcrumbs/Breadcrumb';
import LocationStatistics from './LocationStatistics';
// Import the simplified FinancialHistory component
import { FinancialHistory } from "./FinancialHistory";

interface LocationHistoryProps {
  locationId: string;
  // Add an optional location prop to use when passed from the History button
  location?: LocationType;
}

export const LocationHistory: React.FC<LocationHistoryProps> = ({ 
  locationId,
  location: initialLocation
}) => {
  const { t } = useLanguage();
  const router = useRouter();

  // State variables with the correct types
  const [location, setLocation] = useState<LocationType | null>(initialLocation || null);
  const [isLoading, setIsLoading] = useState(!initialLocation);

  // Only fetch location data if initialLocation is not provided
  useEffect(() => {
    // If initialLocation is provided, we don't need to fetch location data
    if (initialLocation) {
      setLocation(initialLocation);
      setIsLoading(false);
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Here you would fetch location data from your API
        console.log(`Fetching location data for ID: ${locationId}`);
        setLocation({
          id: locationId,
          name: `Location ${locationId}`,
          // Add other required fields with placeholder values
          address: "Loading address...",
          type: "office",
          status: "active", 
          capacity: 0,
          ownedBy: "Loading...",
          ourPercentage: 0,
          sharedWith: [], 
          reservedBy: "",
          reservedFrom: new Date(),
          reservedUntil: new Date(),
          lastlyReservedIn: new Date(),
          contactPerson: "Loading...",
          contactEmail: "<EMAIL>",
          contactPhone: "Loading...",
          createdAt: new Date(),
          createdBy: "Loading...",
          reservations: [],
          description: "Loading description..." // Add the missing description property
        });
      } catch (error) {
        console.error('Error fetching location history:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [locationId, initialLocation]);

  return (
    <div className="space-y-6">
      <Breadcrumb pageName={t("locationHistory")} />
      
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          {t("back")}
        </Button>
      </div>

      {/* LocationStatistics component */}
      <LocationStatistics 
        locationId={locationId} 
        locationName={location?.name}
      />
      
      {/* Financial History tabs component - now simplified */}
      <FinancialHistory 
        locationId={locationId}
        isLoading={isLoading}
      />
    </div>
  );
};

export default LocationHistory;

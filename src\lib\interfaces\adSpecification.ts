export interface AdSpecification {
  id: string;
  title: string;
  description: string;
  type: 'static' | 'digital' | 'illuminated' | 'non-illuminated' | 'video';
  dimensions: {
    width: number;
    height: number;
    unit: 'meters' | 'feet';
  };
  format: string;
  designRequirements: {
    fileType: string[];
    resolution: string;
    colorSpace: string;
    bleedArea?: number;
    safeArea?: number;
  };
  materials?: string[];
  installationDetails?: {
    estimatedTime: number;
    specialRequirements: string[];
  };
  regulatoryRequirements?: string[];
  previewImage?: string;
}

export interface AdPerformanceMetrics {
  id: string;
  adId: string;
  locationId: string;
  reservationId: string;
  startDate: Date;
  endDate: Date;
  estimatedViews: number;
  actualViews?: number;
  impressions: number;
  engagementRate?: number;
  conversionRate?: number;
  roi?: number;
  feedback?: {
    clientSatisfaction: number;
    publicReaction: string[];
    notes: string;
  };
}

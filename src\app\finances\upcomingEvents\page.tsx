'use client';

import React, { useState, useEffect } from "react";
import Layout from "@/components/Layouts/Layout";
import UpcomingEventsPage from "@/components/Dashboard/UpcommingEventsPage/UpcommingEvents";
import { EventDetails } from "@/lib/interfaces/finaces";
import getCombinedEvents from "@/lib/events";
import { LoadingComp } from "@/components/common/Loading";

export default function UpcomingEvents() {
  const [events, setEvents] = useState<EventDetails[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      try {
        const fetchedEvents = await getCombinedEvents();
        setEvents(fetchedEvents);
      } catch (error) {
        console.error("Error fetching events:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  if (loading) {
    return (
      <Layout>
        <div className="flex min-h-[400px] items-center justify-center rounded-lg bg-white dark:bg-gray-800">
          <LoadingComp />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <UpcomingEventsPage events={events} setEvents={setEvents} />
    </Layout>
  );
}
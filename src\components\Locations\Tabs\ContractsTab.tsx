import React, { useState, useEffect } from "react";
import { Contract } from "@/lib/interfaces/contract";
import ContractServices from "@/lib/contracts";
import { Loader2 } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";
import {
  FaEye,
  FaTimes,
  FaSearch,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaUser,
  FaMoneyBillAlt,
  FaPercentage,
  FaFileUpload,
} from "react-icons/fa";

interface ContractsTabProps {
  locationId: string;
}

const ContractsTab: React.FC<ContractsTabProps> = ({ locationId }) => {
  const { t, language } = useLanguage();
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState<{ start: string; end: string }>({
    start: "",
    end: "",
  });

  // Function to determine if we should use card view (mobile) or table view
  const [isCardView, setIsCardView] = useState(window.innerWidth < 900);

  useEffect(() => {
    const fetchContracts = async () => {
      setIsLoading(true);
      try {
        // Get a fresh instance inside the effect
        const { getContractsByLocation } = ContractServices();
        const contractsData = await getContractsByLocation(locationId);
        setContracts(contractsData);
        setError(null);
      } catch (err) {
        console.error("Error fetching contracts:", err);
        setError("Failed to load contract data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchContracts();
  }, [locationId]); // Removed getContractsByLocation from dependencies

  // Listen for window resizes to toggle between card and table view
  useEffect(() => {
    const handleResize = () => {
      setIsCardView(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Filter contracts based on search term and filters
  const filteredContracts = contracts.filter((contract) => {
    const matchesSearch =
      contract.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.contractNumber
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      contract.client?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      true ||
      contract.location?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      true;

    const matchesStatus =
      statusFilter === "all" || contract.status === statusFilter;

    const startDate = dateFilter.start ? new Date(dateFilter.start) : null;
    const endDate = dateFilter.end ? new Date(dateFilter.end) : null;
    const contractStart = new Date(contract.start_date);
    const contractEnd = new Date(contract.end_date);

    const matchesDate =
      (!startDate || contractStart >= startDate || contractEnd >= startDate) &&
      (!endDate || contractStart <= endDate);

    return matchesSearch && matchesStatus && matchesDate;
  });

  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setDateFilter({ start: "", end: "" });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === "ar" ? "ar-EG" : "en-US",
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
      case "expired":
        return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
      case "terminated":
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      case "draft":
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  const getPaymentStatusBadgeClass = (status: string) => {
    switch (status) {
      case "up-to-date":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "overdue":
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      case "partially-paid":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  // Calculate contract duration in months
  const getContractDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const months =
      (end.getFullYear() - start.getFullYear()) * 12 +
      (end.getMonth() - start.getMonth());
    return Math.max(0, months);
  };

  // Handle contract click - updated to accept string ID
  const handleContractClick = (id: string) => {
    // For now just log - later implement view details functionality
    console.log(`View contract details for: ${id}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return <div className="py-8 text-center text-red-500">{error}</div>;
  }

  return (
    <div className="w-full max-w-full overflow-hidden rounded-lg bg-white p-3 dark:bg-gray-800 sm:p-6">
      {/* Filters - Improved for mobile */}
      <div className="mb-4 w-full max-w-full overflow-hidden sm:mb-6">
        <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:hidden">
          {t("searchAndFilters")}
        </h3>
        <div className="flex flex-col space-y-3 sm:flex-row sm:flex-wrap sm:gap-4 sm:space-y-0">
          {/* Search - Full width on mobile */}
          <div className="w-full">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t("searchContracts")}
                className="w-full rounded-lg border py-2 pl-10 pr-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
              />
              <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
                >
                  <FaTimes />
                </button>
              )}
            </div>
          </div>

          {/* Filter Controls */}
          <div className="grid w-full grid-cols-1 gap-3 sm:grid-cols-3">
            {/* Status Filter */}
            <div className="w-full">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full rounded-lg border px-4 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
              >
                <option value="all">{t("allStatuses")}</option>
                <option value="active">{t("active")}</option>
                <option value="pending">{t("pending")}</option>
                <option value="expired">{t("expired")}</option>
                <option value="terminated">{t("terminated")}</option>
                <option value="draft">{t("draft")}</option>
              </select>
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-2">
              <div className="relative">
                <FaCalendarAlt className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
                <input
                  type="date"
                  value={dateFilter.start}
                  onChange={(e) =>
                    setDateFilter({ ...dateFilter, start: e.target.value })
                  }
                  className="w-full rounded-lg border py-2 pl-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
                  placeholder={t("startDate")}
                />
              </div>
              <div className="relative">
                <input
                  type="date"
                  value={dateFilter.end}
                  onChange={(e) =>
                    setDateFilter({ ...dateFilter, end: e.target.value })
                  }
                  className="w-full rounded-lg border py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
                  placeholder={t("endDate")}
                />
              </div>
            </div>

            {/* Reset Button */}
            <button
              onClick={resetFilters}
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-red-500 px-4 py-2 text-white transition-colors hover:bg-red-600 sm:py-3"
            >
              <FaTimes />
              <span>{t("resetFilters")}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Card View for Mobile */}
      {isCardView ? (
        <div className="w-full max-w-full space-y-4 overflow-hidden">
          {filteredContracts.length > 0 ? (
            filteredContracts.map((contract) => (
              <div
                key={contract.id}
                className="cursor-pointer rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
                onClick={() => handleContractClick(contract.id)}
              >
                <div className="mb-2 flex items-start justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      {contract.title}
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {contract.contractNumber}
                    </p>
                  </div>
                  <span
                    className={`rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(contract.status)}`}
                  >
                    {t(
                      contract.status.charAt(0).toUpperCase() +
                        contract.status.slice(1),
                    )}
                  </span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center text-gray-500 dark:text-gray-400">
                    <FaUser className="mr-2" size={14} />{" "}
                    {contract.client?.name || "N/A"}
                  </div>
                  <div className="flex items-center text-gray-500 dark:text-gray-400">
                    <FaMapMarkerAlt className="mr-2" size={14} />{" "}
                    {contract.location?.name || "N/A"}
                  </div>
                  <div className="flex flex-wrap items-center text-gray-500 dark:text-gray-400">
                    <FaCalendarAlt className="mr-2" size={14} />
                    <span className="mr-1">
                      {formatDate(contract.start_date)} -{" "}
                      {formatDate(contract.end_date)}
                    </span>
                    <span className="text-xs">
                      (
                      {Number(
                        getContractDuration(
                          contract.start_date,
                          contract.end_date,
                        ),
                      ).toLocaleString(
                        language === "ar" ? "ar-EG" : "en-US",
                      )}{" "}
                      {t("months")})
                    </span>
                  </div>
                  <div className="flex items-center font-medium text-gray-900 dark:text-white">
                    <FaMoneyBillAlt className="mr-2" size={14} />
                    {formatCurrency(contract.monthlyAmount)} / {t("month")}
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className={`rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getPaymentStatusBadgeClass(contract.paymentStatus)}`}
                    >
                      {t(contract.paymentStatus.replace(/-/g, " "))}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {t("due")}:{" "}
                      {Number(contract.paymentDay).toLocaleString(
                        language === "ar" ? "ar-EG" : "en-US",
                      )}
                      {t("th")}
                    </span>
                  </div>
                  {contract.renewal_terms?.auto_renew && (
                    <div className="flex flex-wrap items-center text-gray-500 dark:text-gray-400">
                      <FaPercentage className="mr-2" size={14} />
                      <span className="text-xs">
                        {t("autoRenews")}{" "}
                        {Number(
                          contract.renewal_terms.increase_percentage,
                        ).toLocaleString(language === "ar" ? "ar-EG" : "en-US")}
                        % {t("increase")}
                      </span>
                    </div>
                  )}
                  {contract.document_uploaded?.length > 0 && (
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                      <FaFileUpload className="mr-2" size={14} />
                      {Number(contract.document_uploaded.length).toLocaleString(
                        language === "ar" ? "ar-EG" : "en-US",
                      )}{" "}
                      {t("documents")}
                    </div>
                  )}
                </div>

                <div className="mt-3 flex justify-end space-x-2 border-t border-gray-200 pt-3 dark:border-gray-700">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleContractClick(contract.id);
                    }}
                    className="p-2 text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                    aria-label={t("viewDetails")}
                  >
                    <FaEye size={18} />
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="py-8 text-center text-gray-500 dark:text-gray-400">
              {t("noContractsFound")}
            </div>
          )}
        </div>
      ) : (
        /* Table View for Tablets and Larger */
        <div className="w-full max-w-full overflow-hidden">
          <div className="w-full overflow-x-auto">
            <div className="min-w-full">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("contract")}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("client")}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("contractPeriod")}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("totalAmount")}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("status")}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                  {filteredContracts.length > 0 ? (
                    filteredContracts.map((contract) => (
                      <tr
                        key={contract.id}
                        className="cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700"
                        onClick={() => handleContractClick(contract.id)}
                      >
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {contract.title}
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {contract.client?.name || "N/A"}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {contract.location?.name || "N/A"}
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {formatDate(contract.start_date)} -{" "}
                            {formatDate(contract.end_date)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {Number(
                              getContractDuration(
                                contract.start_date,
                                contract.end_date,
                              ),
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}{" "}
                            {t("months")}
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(contract.total_amount)}
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <span
                            className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(contract.status)}`}
                          >
                            {t(
                              contract.status.charAt(0).toUpperCase() +
                                contract.status.slice(1),
                            )}
                          </span>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={6}
                        className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        {t("noContractsFound")}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContractsTab;

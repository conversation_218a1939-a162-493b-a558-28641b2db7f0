import React, { useState } from 'react';
import Link from 'next/link';
import { Bell, Check, Trash2, X } from 'lucide-react';
import NotificationList from './NotificationList';
import NotificationFilter from './NotificationFilter';
import { Notification, NotificationFilterOptions } from '@/lib/types/notification';
import useLanguage from '@/hooks/useLanguage';

interface NotificationPanelProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onMarkAllAsRead: () => void;
  onDeleteAll: () => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({
  isOpen,
  onClose,
  notifications,
  onMarkAsRead,
  onDelete,
  onMarkAllAsRead,
  onDeleteAll
}) => {
  const { t, language } = useLanguage();
  const isRTL = language === 'ar';
  const [filterOptions, setFilterOptions] = useState<NotificationFilterOptions>({
    type: 'all',
    read: null,
    timeframe: 'all'
  });

  if (!isOpen) return null;

  // Update to use is_read instead of read
  const unreadCount = notifications.filter(n => !n.is_read).length;

  return (
    <div 
      className={`fixed top-0 ${isRTL ? 'left-0' : 'right-0'} w-full sm:w-96 h-screen bg-white dark:bg-boxdark shadow-lg z-[9999] flex flex-col transition-transform duration-300 ease-in-out ${
        isOpen 
          ? 'translate-x-0' 
          : isRTL 
            ? '-translate-x-full' 
            : 'translate-x-full'
      }`}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <div className="flex items-center">
          <Bell className="h-5 w-5 text-gray-600 dark:text-gray-300 mr-2" />
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{t('notifications')}</h2>
          {unreadCount > 0 && (
            <span className="ml-2 bg-meta-1 text-white text-xs font-bold px-2 py-0.5 rounded-full">
              {unreadCount}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label="Close"
          >
            <X className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          </button>
        </div>
      </div>

      {/* Action Bar */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-2">
          <button
            onClick={onMarkAllAsRead}
            disabled={unreadCount === 0}
            className={`flex items-center text-xs font-medium py-1 px-2 rounded ${
              unreadCount === 0
                ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                : 'text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
            }`}
          >
            <Check className="h-3 w-3 mr-1" />
            {t('markAllRead')}
          </button>
          <button
            onClick={onDeleteAll}
            disabled={notifications.length === 0}
            className={`flex items-center text-xs font-medium py-1 px-2 rounded ${
              notifications.length === 0
                ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                : 'text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20'
            }`}
          >
            <Trash2 className="h-3 w-3 mr-1" />
            {t('clearAll')}
          </button>
        </div>
        
        <NotificationFilter 
          filterOptions={filterOptions}
          onFilterChange={setFilterOptions}
        />
      </div>

      {/* Notification List */}
      <div className="flex-1 overflow-y-auto">
        <NotificationList
          notifications={notifications}
          filterOptions={filterOptions}
          onMarkAsRead={onMarkAsRead}
          onDelete={onDelete}
        />
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 dark:border-gray-700 text-center">
        <Link
          href="/notifications"
          className="text-sm text-blue-600 dark:text-blue-400 font-medium hover:underline"
          onClick={onClose}
        >
          {t('seeAllNotifications')}
        </Link>
      </div>
    </div>
  );
};

export default NotificationPanel;

"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import ExpenseSummary from "./ExpenseSummary";
import ExpensePopup from "./ExpensePopup";
import UpcomingEventsPage from "@/components/Dashboard/UpcommingEventsPage/UpcommingEvents";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import {
  getDateRange,
  DateRangeSelector,
} from "@/components/Financials/common/dateRanges";
import { LoadingComp } from "@/components/common/Loading";
import { usePermissions } from "@/hooks/usePermissions";
import ExpenseServices from "@/lib/expenses";
import { Card, CardContent } from "../../cards/card";
import { CreditCard } from "lucide-react";

const ExpenseOverview: React.FC = () => {
  const { t, language } = useLanguage();
  const searchParams = useSearchParams();
  const [expenses, setExpenses] = useState<EventDetails[]>([]);
  const [selectedExpense, setSelectedExpense] = useState<EventDetails | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const { hasPermission, permissionsLoaded } = usePermissions();
  const { getExpenses } = ExpenseServices();

  // Default Date Range
  const currentDate = new Date();
  const [startDate, setStartDate] = useState<Date>(
    new Date(currentDate.setDate(currentDate.getDate() - 15)),
  );
  const [endDate, setEndDate] = useState<Date>(
    new Date(new Date().setDate(new Date().getDate() + 15)),
  );

  useEffect(() => {
    const fetchExpenses = async () => {
      setLoading(true);
      try {
        const result = await getExpenses();
        console.log("Fetched expenses:", result);
        setExpenses(result || []);
      } catch (err) {
        console.error("Failed to fetch expenses:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchExpenses();
  }, []);

  const filterExpensesByDate = (expenses: EventDetails[]) => {
    return expenses.filter((expense) => {
      // Normalize dates to remove time component for accurate comparison
      const expenseDate = new Date(expense.dueDate);
      expenseDate.setHours(0, 0, 0, 0);

      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);

      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // Set to end of day for inclusive comparison

      return expenseDate >= start && expenseDate <= end;
    });
  };

  // Change the parameter type from number to string
  const handleEditClick = (id: string) => {
    const expense = expenses.find((e) => e.id === id);
    if (expense) setSelectedExpense(expense);
  };

  const handlePopupClose = () => setSelectedExpense(null);

  const handleSavePopUp = (updatedExpense: EventDetails) => {
    setExpenses((prev) =>
      prev.map((expense) =>
        expense.id === updatedExpense.id ? updatedExpense : expense,
      ),
    );
    handlePopupClose();
  };

  const handleDateRangeChange = (range: string) => {
    const { start, end } = getDateRange(range);
    setStartDate(new Date(start));
    setEndDate(new Date(end));
  };

  const locale = language === "ar" ? arSA : enUS;
  const eventId = searchParams ? searchParams.get("eventId") : null;

  // Check permissions loading state
  if (!permissionsLoaded) {
    return (
      <div className="fixed left-0 top-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }

  // Check if user has view permission for expenses
  if (!hasPermission("expenses", "view")) {
    return (
      <>
        <Breadcrumb pageName={t("expenses")} />
        <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
          <div className="flex items-center justify-center h-full">
            <Card className="w-full max-w-md">
              <CardContent className="text-center py-12">
                <CreditCard size={48} className="mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  {t("accessDenied")}
                </h2>
                <p className="text-gray-500 dark:text-gray-400">
                  {t("noPermissionToViewExpenses")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 lg:space-y-8">
      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex flex-col space-y-3 sm:space-y-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-400 sm:text-2xl lg:text-3xl">
            {t("Expense Overview")}
          </h2>

          {/* Date Range Controls */}
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400 sm:text-base">
              {t("as of")}
            </span>
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0">
              <DatePicker
                selected={startDate}
                onChange={(date) => date && setStartDate(date)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 sm:w-auto"
                dateFormat="yyyy-MM-dd"
                locale={locale}
              />
              <span className="text-sm text-gray-500 dark:text-gray-400 sm:text-base">
                {t("to")}
              </span>
              <DatePicker
                selected={endDate}
                onChange={(date) => date && setEndDate(date)}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 sm:w-auto"
                dateFormat="yyyy-MM-dd"
                locale={locale}
              />
            </div>
          </div>
        </div>

        {/* Date Range Selector */}
        <DateRangeSelector onChange={handleDateRangeChange} />
      </div>

      {/* Content Sections */}
      <div className="space-y-4 sm:space-y-6 lg:space-y-8">
        {/* Analytics Section */}
        {hasPermission("expenses", "analytics") && (
          <div className="w-full">
            <ExpenseSummary
              expenses={filterExpensesByDate(expenses)}
              loading={loading}
            />
          </div>
        )}

        {/* Events Section */}
        <div className="w-full">
          {loading ? (
            <div className="flex min-h-[400px] items-center justify-center rounded-lg bg-white dark:bg-gray-800">
              <LoadingComp />
            </div>
          ) : (
            <UpcomingEventsPage
              events={expenses}
              setEvents={setExpenses}
              mode="expenses"
              selectedEventId={eventId || undefined} // Pass the string ID directly, no need to parse to number
            />
          )}
        </div>
      </div>

      {/* Popup */}
      {selectedExpense && (
        <ExpensePopup
          expense={selectedExpense}
          onClose={handlePopupClose}
          onSave={handleSavePopUp}
        />
      )}
    </div>
  );
};

export default ExpenseOverview;

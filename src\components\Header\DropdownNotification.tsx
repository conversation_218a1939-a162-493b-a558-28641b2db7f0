import { useState, useEffect } from "react";
import Link from "next/link";
import ClickOutside from "../ClickOutside";
import useLanguage from "@/hooks/useLanguage";
import NotificationServices, { Notification } from "@/lib/notifications";
import { formatDistanceToNow } from "date-fns";
import { ar } from "date-fns/locale";

const DropdownNotification = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [notifying, setNotifying] = useState(true);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const { t, language } = useLanguage();
  const isRTL = language === "ar";
  const notificationServices = NotificationServices();

  // Fetch unread count
  useEffect(() => {
    const abortController = new AbortController();

    const fetchUnreadCount = async () => {
      try {
        const count = await notificationServices.getUnreadCount(
          abortController.signal,
        );
        setUnreadCount(count);

        // If there are unread notifications, show the notification indicator
        if (count > 0) {
          setNotifying(true);
        }
      } catch (error) {
        console.error("Failed to fetch notification count:", error);
      }
    };

    fetchUnreadCount();

    // Set up a polling mechanism to update the count periodically
    const interval = setInterval(fetchUnreadCount, 60000); // Update every minute

    return () => {
      abortController.abort();
      clearInterval(interval);
    };
  }, []);

  // Fetch notifications when dropdown is opened
  useEffect(() => {
    const abortController = new AbortController();

    const fetchNotifications = async () => {
      if (dropdownOpen) {
        setLoading(true);
        try {
          // Fetch unread notifications
          const fetchedNotifications =
            await notificationServices.getNotifications(
              { read: false },
              abortController.signal,
            );
          setNotifications(fetchedNotifications);
        } catch (error) {
          console.error("Failed to fetch notifications:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchNotifications();

    return () => {
      abortController.abort();
    };
  }, [dropdownOpen]);

  // Refresh unread count when dropdown is closed
  useEffect(() => {
    if (!dropdownOpen) {
      const fetchUnreadCount = async () => {
        try {
          const count = await notificationServices.getUnreadCount();
          setUnreadCount(count);

          if (count > 0) {
            setNotifying(true);
          }
        } catch (error) {
          console.error("Failed to fetch notification count:", error);
        }
      };

      fetchUnreadCount();
    }
  }, [dropdownOpen]);

  // Get the priority color
  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-amber-500";
      case "low":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Format the date to a relative time (e.g., "2 hours ago")
  const formatNotificationDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: language === "ar" ? ar : undefined,
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  // Handle marking notification as read
  const handleNotificationClick = async (notification: Notification) => {
    try {
      if (!notification.is_read) {
        await notificationServices.markAsRead(notification.id);
        // Update the notifications list
        setNotifications((prevNotifications) =>
          prevNotifications.map((n) =>
            n.id === notification.id ? { ...n, is_read: true } : n,
          ),
        );
        // Update unread count
        setUnreadCount((prev) => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  // Handle marking notification as unread
  const handleMarkAsUnread = async (id: string) => {
    try {
      const notification = notifications.find((n) => n.id === id);
      if (notification && notification.is_read) {
        const success = await notificationServices.markAsUnread(id);
        if (success) {
          // Update the notifications list
          setNotifications((prevNotifications) =>
            prevNotifications.map((n) =>
              n.id === id ? { ...n, is_read: false } : n,
            ),
          );
          // Update unread count
          setUnreadCount((prev) => prev + 1);
          // Re-enable the notification indicator
          setNotifying(true);
        }
      }
    } catch (error) {
      console.error("Failed to mark notification as unread:", error);
    }
  };

  return (
    <ClickOutside
      onClick={() => setDropdownOpen(false)}
      className="relative z-[999999]"
    >
      <li className="relative">
        <button
          onClick={() => {
            setNotifying(false);
            setDropdownOpen(!dropdownOpen);
          }}
          className="relative flex h-10 w-10 items-center justify-center rounded-full border border-stroke bg-gray-100 transition-colors hover:bg-gray-200 dark:border-strokedark dark:bg-meta-4 dark:hover:bg-meta-3"
        >
          {notifying && unreadCount > 0 && (
            <span className="absolute -right-1 -top-1 h-4 w-4">
              <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-meta-1 opacity-75"></span>
              <span className="relative inline-flex h-4 w-4 rounded-full bg-meta-1"></span>
            </span>
          )}

          <svg
            className="fill-current duration-300 ease-in-out"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.1999 14.9343L15.6374 14.0624C15.5249 13.8937 15.4687 13.7249 15.4687 13.528V7.67803C15.4687 6.01865 14.7655 4.47178 13.4718 3.31865C12.4312 2.39053 11.0812 1.7999 9.64678 1.6874V1.1249C9.64678 0.787402 9.36553 0.478027 8.9999 0.478027C8.6624 0.478027 8.35303 0.759277 8.35303 1.1249V1.65928C8.29678 1.65928 8.24053 1.65928 8.18428 1.6874C4.92178 2.05303 2.4749 4.66865 2.4749 7.79053V13.528C2.44678 13.8093 2.39053 13.9499 2.33428 14.0343L1.7999 14.9343C1.63115 15.2155 1.63115 15.553 1.7999 15.8343C1.96865 16.0874 2.2499 16.2562 2.55928 16.2562H8.38115V16.8749C8.38115 17.2124 8.6624 17.5218 9.02803 17.5218C9.36553 17.5218 9.6749 17.2405 9.6749 16.8749V16.2562H15.4687C15.778 16.2562 16.0593 16.0874 16.228 15.8343C16.3968 15.553 16.3968 15.2155 16.1999 14.9343ZM3.23428 14.9905L3.43115 14.653C3.5999 14.3718 3.68428 14.0343 3.74053 13.6405V7.79053C3.74053 5.31553 5.70928 3.23428 8.3249 2.95303C9.92803 2.78428 11.503 3.2624 12.6562 4.2749C13.6687 5.1749 14.2312 6.38428 14.2312 7.67803V13.528C14.2312 13.9499 14.3437 14.3437 14.5968 14.7374L14.7655 14.9905H3.23428Z"
              fill=""
            />
          </svg>
        </button>

        {dropdownOpen && (
          <div
            className={`fixed top-[4.5rem] sm:absolute sm:top-[calc(100%+1rem)] 
          ${
            isRTL
              ? "rtl left-4 right-4 pl-5 sm:-right-64 sm:left-auto"
              : "ltr left-4 right-4 pr-5 sm:-right-5 sm:left-auto"
          }
          z-[99999] flex 
          max-h-[480px] w-[calc(100%-2rem)] 
          flex-col rounded-sm border 
          border-stroke bg-white shadow-xl 
          dark:border-strokedark 
          dark:bg-boxdark sm:max-h-[480px]
          sm:w-80 sm:rounded-lg`}
          >
            {/* Header */}
            <div className="flex-none border-b border-stroke bg-white dark:border-strokedark dark:bg-boxdark">
              <div
                className={`flex items-center justify-between px-4 py-3 ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <h5 className="text-sm font-medium text-black dark:text-white">
                  {t("notifications")}
                </h5>
                <span className="flex h-5 w-5 items-center justify-center rounded-full bg-meta-1/10 text-xs font-medium text-meta-1">
                  {unreadCount}
                </span>
              </div>
            </div>

            {/* Notification List */}
            <div
              className={`flex-1 overflow-y-auto ${isRTL ? "pl-2" : "pr-2"}`}
            >
              {loading ? (
                <div className="flex h-20 items-center justify-center">
                  <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-primary"></div>
                </div>
              ) : notifications.length === 0 ? (
                <div className="flex h-20 items-center justify-center text-sm text-gray-500 dark:text-gray-400">
                  {t("noNotifications")}
                </div>
              ) : (
                <ul
                  className={`flex flex-col divide-y divide-stroke dark:divide-strokedark 
                  ${isRTL ? "text-right" : "text-left"}`}
                >
                  {notifications.map((notification) => (
                    <li key={notification.id} className="group relative">
                      <Link
                        href="#"
                        className={`flex gap-4 px-4 py-3 transition-colors hover:bg-gray-2 dark:hover:bg-meta-4
                          ${isRTL ? "flex-row-reverse" : ""}`}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div
                          className={`flex flex-1 items-start gap-3 ${isRTL ? "flex-row-reverse" : ""}`}
                        >
                          {/* Priority dot indicator */}
                          <span
                            className={`mt-2 flex h-2 w-2 rounded-full ${getPriorityColor(notification.priority)}`}
                          ></span>

                          <div
                            className={`flex-1 ${isRTL ? "text-right" : "text-left"}`}
                          >
                            <p className="text-sm font-medium text-black dark:text-white">
                              {language === "ar"
                                ? notification.title_ar
                                : notification.title}
                            </p>
                            <p className="line-clamp-2 text-sm text-gray-500 dark:text-gray-400">
                              {language === "ar"
                                ? notification.message_ar
                                : notification.message}
                            </p>
                            <p className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                              {formatNotificationDate(notification.created_at)}
                            </p>
                          </div>
                        </div>
                      </Link>
                      {/* Add a context menu for mark as unread */}
                      {notification.is_read && (
                        <div className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMarkAsUnread(notification.id);
                            }}
                            className="rounded-full bg-white p-1 text-xs shadow hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600"
                            title={t("markAsUnread")}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-3 w-3"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                          </button>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* Footer */}
            <div className="flex-none border-t border-stroke bg-gray-50 dark:border-strokedark dark:bg-meta-4">
              <div className="flex items-center justify-center p-4">
                <Link
                  href="/notification"
                  className="text-sm font-medium text-primary hover:underline"
                >
                  {t("viewAllNotifications")}
                </Link>
              </div>
            </div>
          </div>
        )}
      </li>
    </ClickOutside>
  );
};

export default DropdownNotification;

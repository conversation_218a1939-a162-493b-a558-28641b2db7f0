"use client";

import { ContactHistory } from '@/components/contacts/ContactHistory';
import { Metadata } from 'next';
import React from "react";
import Layout from "@/components/Layouts/Layout";

interface PageProps {
  params: {
    id: string;
  };
}

export default function ContactHistoryPage({ params }: PageProps) {
  return (
    <div className="container mx-auto py-6">
      <Layout>
        <ContactHistory contactId={params.id} />
      </Layout>
    </div>
  );
}
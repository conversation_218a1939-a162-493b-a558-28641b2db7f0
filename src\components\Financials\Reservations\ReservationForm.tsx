// import React, { useState, useEffect } from "react";
// import { Reservation, Installment, ReservationFormData } from "@/lib/interfaces/reservation";
// import { EventDetails, Contact, Location } from "@/lib/interfaces/finaces";
// import { FaPlus, FaTrash, FaInfoCircle, Fa<PERSON>heck } from "react-icons/fa";
// import Select from "react-select";
// import useLanguage from "@/hooks/useLanguage";
// import { useContacts } from "@/hooks/useContact";
// import { useLocations } from "@/hooks/useLocations";
// import { is } from "date-fns/locale";
// import ReservationServices from "@/lib/reservations";
// import IncomeServices from "@/lib/income";
// import ExpenseServices from "@/lib/expenses";
// interface ReservationFormProps {
//   reservation?: Reservation; // Optional for editing existing reservation
//   onSave: (reservation: Reservation, events: EventDetails[]) => void;
//   onCancel: () => void;
// }

// const ReservationForm: React.FC<ReservationFormProps> = ({ reservation, onSave, onCancel }) => {
//   const { t } = useLanguage();
//   const isEditing = !!reservation;
//   const reservationServices = ReservationServices();
//   const { data, isLoading, isError } = useContacts();
//   const { data: locationsData, isLoading: isLocationsLoading, isError: isLocationsError } = useLocations();
//   const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
//   const IncomeService = IncomeServices();
//   const ExpenseService = ExpenseServices();
//   const [formData, setFormData] = useState<ReservationFormData>({
//     title: reservation?.title || "",
//     description: reservation?.description || "",
//     totalAmount: reservation?.totalAmount || 0,
//     startDate: reservation?.startDate.split('T')[0] || "",
//     endDate: reservation?.endDate.split('T')[0] || "",
//     contactId: reservation?.contactId || 0,
//     locationId: reservation?.locationId || 0,
//     adType: reservation?.adType || "",
//     adPlacement: reservation?.adPlacement || "",
//     installmentCount: reservation?.installments.length || 1,
//     installmentType: "equal",
//     installments: reservation?.installments.map(inst => ({
//       amount: inst.amount,
//       dueDate: inst.dueDate.split('T')[0],
//       status: inst.status
//     })) || [],
//     notes: reservation?.notes || "",
//     requiredCapacity: reservation?.requiredCapacity || 0, // Added required capacity field
//   });

//   // State for selected location details
//   const [selectedLocation, setSelectedLocation] = useState<{
//     id: string;
//     totalCapacity: number;
//     takenCapacity: number;
//     availableCapacity: number;
//   } | null>(null);

//   // Initialize installments if editing
//   useEffect(() => {
//     if (!isEditing && formData.installments.length === 0 && formData.startDate && formData.totalAmount > 0) {
//       generateEqualInstallments();
//     }
    

//   }, []);

//       const formattedLocations = locationsData?.locations.map((location: any) => ({
//           id: location.id || "default-id",
//           name: location.name,
//           address: location.address,
//           city: location.city ? location.city : "",
//           country: location.country ? location.country : "",
//           postalCode: location.postalCode ? location.postalCode : "",
//           percentage: location.ourPercentage ? location.ourPercentage : 0,
//           amount: location.amount ? location.amount : 0,
//       }));
//       const [locationList, setLocationList] = useState<Location[]>(formattedLocations || []);
//       const [selectedLocationId, setSelectedLocationId] = useState<string | null>(null);

//   // Update capacity information when location changes
//   const updateLocationCapacityInfo = ( value: string , totalCapacity: number , takenCapacity: number) => {

//       const availableCapacity = totalCapacity - takenCapacity;
//       setSelectedLocation({
//         id : value,
//         totalCapacity: totalCapacity,
//         takenCapacity: takenCapacity,
//         availableCapacity
//       });

//       // Reset required capacity when changing locations
//       if (!isEditing) {
//         setFormData(prev => ({
//           ...prev,
//           requiredCapacity: 0
//         }));
//       }
//     } 

//   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
//     const { name, value } = e.target;
    
//     // Special handling for capacity to ensure it doesn't exceed available capacity
//     if (name === "requiredCapacity" && selectedLocation) {
//       const numValue = Number(value);
//       if (numValue > selectedLocation.availableCapacity) {
//         alert(t("Required capacity cannot exceed available capacity"));
//         return;
//       }
//     }
    
//     setFormData({
//       ...formData,
//       [name]: ["totalAmount", "installmentCount", "requiredCapacity"].includes(name) ? Number(value) : value
//     });
//   };

//   const handleSelectChange = async (name: string, value: any) => {
//     if (name === "locationId") {
//       try {
//         const locationDetails = await reservationServices.getReservationsByLocation(
//           value,
//           formData.startDate,
//           formData.endDate
//         );
  
//         if (locationDetails?.location) {
//           updateLocationCapacityInfo(
//             value,
//             locationDetails.location.totalCapacity,
//             locationDetails.location.takenCapacity
//           );
//         }
//       } catch (error) {
//         console.error("Error fetching location details:", error);
//         alert("Failed to fetch location details. Please try again.");
//       }
//     }
  
//     setFormData({
//       ...formData,
//       [name]: value,
//     });
//   };

//   const generateEqualInstallments = () => {
//     // Silent validation - simply return if values are invalid without showing alert
//     if (formData.totalAmount <= 0 || formData.installmentCount <= 0 || !formData.startDate) {
//       return;
//     }

//     const amount = Math.floor(formData.totalAmount / formData.installmentCount);
//     const remainder = formData.totalAmount % formData.installmentCount;
    
//     const startDate = new Date(formData.startDate);
    
//     const newInstallments = Array(formData.installmentCount).fill(null).map((_, index) => {
//       const dueDate = new Date(startDate);
//       dueDate.setMonth(dueDate.getMonth() + index);
      
//       return {
//         amount: index === 0 ? amount + remainder : amount,
//         dueDate: dueDate.toISOString().split('T')[0],
//         status: 'upcoming' as const
//       };
//     });
    
//     setFormData({
//       ...formData,
//       installments: newInstallments,
//       installmentType: "equal"
//     });
//   };

//   const handleInstallmentChange = (index: number, field: string, value: any) => {
//     const updatedInstallments = [...formData.installments];
//     updatedInstallments[index] = {
//       ...updatedInstallments[index],
//       [field]: field === "amount" ? Number(value) : value
//     };
    
//     setFormData({
//       ...formData,
//       installments: updatedInstallments
//     });
//   };

//   const handleAddInstallment = () => {
//     const lastInstallment = formData.installments[formData.installments.length - 1];
//     const newDueDate = lastInstallment ? 
//       (() => {
//         const date = new Date(lastInstallment.dueDate);
//         date.setMonth(date.getMonth() + 1);
//         return date.toISOString().split('T')[0];
//       })() : 
//       formData.startDate;

//     setFormData({
//       ...formData,
//       installments: [
//         ...formData.installments,
//         {
//           amount: 0,
//           dueDate: newDueDate,
//           status: 'upcoming'
//         }
//       ],
//       installmentType: "custom"
//     });
//   };

//   const handleRemoveInstallment = (index: number) => {
//     const updatedInstallments = formData.installments.filter((_, i) => i !== index);
//     setFormData({
//       ...formData,
//       installments: updatedInstallments
//     });
//   };

//       const handleSaveNormal = async() => {
//           const events: EventDetails[] = [];

  
//           for (let i = 0; i < formData.installments.length; i++) {
//               const eventDate = new Date(formData.installments[i].dueDate);
              
  
  
//               const newEvent = {
//                   // title,
//                   // amount,
//                   title: `${formData.title} - Installment ${i + 1}`,
//                   amount: formData.installments[i].amount,
//                   dueDate: eventDate.toISOString(),
//                   category: "income",
//                   type: "Advertisement",
//                   status,
//                   lastEdited: new Date().toISOString(),
//                   lastEditedBy: "Admin",
//                   priority: "medium",
//                   editingHistory: [],
//                   contact: selectedContact || { id: "default-id", name: "", email: "", phone: "" },
//                   location: selectedLocation || { id: "default-id", name: "", address: "", city: "", country: "", postalCode: "" }
//               }
  
//                   await IncomeService.createIncome({
//                   title: newEvent.title,
//                   amount: newEvent.amount,
//                   paid_date: null,
//                   due_date: eventDate.toISOString(),
//                   status,
//                   priority: "medium",
//                   contact_ids: selectedContact ? [selectedContact.id] : [],
//                   location_ids: selectedLocation ? [selectedLocation.id] : [],
//                   description: ""
//               });
             
//               }
   
  
//           }
      
  

//   const handleSubmit = () => {
//     // Validation
//     if (!formData.title || !formData.startDate || !formData.endDate ) {
//       alert(t("Please fill all required fields"));
//       return;
//     }

//     if (formData.installments.length === 0) {
//       alert(t("Please add at least one installment"));
//       return;
//     }

//     if (formData.installments.some(i => i.amount <= 0)) {
//       alert(t("All installments must have an amount greater than zero"));
//       return;
//     }

//     // Validate capacity selection
//     if (formData.requiredCapacity <= 0) {
//       alert(t("Please specify the required capacity"));
//       return;
//     }

//     if (selectedLocation && formData.requiredCapacity > selectedLocation.availableCapacity) {
//       alert(t("Required capacity cannot exceed available capacity"));
//       return;
//     }

//     const totalInstallmentAmount = formData.installments.reduce((sum, i) => sum + i.amount, 0);
//     if (totalInstallmentAmount !== formData.totalAmount) {
//       alert(t(`Total installment amount (${totalInstallmentAmount}) doesn't match reservation amount (${formData.totalAmount})`));
//       return;
//     }

//     // Create financial events for installments
//     const installmentEvents: EventDetails[] = formData.installments.map((installment, index) => {
//       const existingEventId = reservation?.installments[index]?.financialEventId;
//       return {
//         id: existingEventId || Date.now() + index,
//         title: `${formData.title} - Installment ${index + 1}`,
//         amount: installment.amount,
//         dueDate: installment.dueDate,
//         status: installment.status,
//         category: "income",
//         type: "Advertisement",
//         priority: "medium",
//         lastEdited: new Date().toISOString(),
//         lastEditedBy: "Admin",
//         editingHistory: [],
//         contact: data?.contacts?.find(c => Number(c.id) === formData.contactId) || { id: "0", name: "", email: "", phone: "" },
//         location: locationsData?.locations?.find(l => Number(l.id) === formData.locationId) || { id: "0", name: "", address: "", city: "", country: "", postalCode: "" }
//       };
//     });

//     // Create the reservation object
//     const selectedContact = data?.contacts?.find(c => Number(c.id) === formData.contactId);
//     const selectedLocationInfo = locationsData?.locations?.find(l => Number(l.id) === formData.locationId);

//     const newReservation: Reservation = {
//       id: reservation?.id || Date.now(),
//       title: formData.title,
//       description: formData.description,
//       totalAmount: formData.totalAmount,
//       reservationDate: reservation?.reservationDate || new Date().toISOString(),
//       startDate: new Date(formData.startDate).toISOString(),
//       endDate: new Date(formData.endDate).toISOString(),
//       status: reservation?.status || "pending",
//       contactId: formData.contactId,
//       contact: selectedContact || { id: "0", name: "", email: "", phone: "" },
//       locationId: formData.locationId,
//       location: selectedLocationInfo || { id: "0", name: "", address: "", city: "", country: "", postalCode: "" },
//       adType: formData.adType,
//       adPlacement: formData.adPlacement,
//       requiredCapacity: formData.requiredCapacity, // Add required capacity to reservation
//       installments: formData.installments.map((installment, index) => ({
//         id: reservation?.installments[index]?.id || Date.now() + index,
//         amount: installment.amount,
//         dueDate: new Date(installment.dueDate).toISOString(),
//         status: installment.status,
//         financialEventId: installmentEvents[index].id
//       })),
//       createdAt: reservation?.createdAt || new Date().toISOString(),
//       updatedAt: new Date().toISOString(),
//       createdBy: reservation?.createdBy || "Current User",
//       notes: formData.notes
//     };

//     console.log("Submitting reservation:", newReservation);
//     console.log("Installment events:", installmentEvents);

//     handleSaveNormal();
    
    
//     // onSave(newReservation, installmentEvents);
//   };

//   return (
//     isLoading || isLocationsLoading ? (
//       <div className="flex items-center justify-center h-screen">
//         <div className="loader"></div>
//       </div>
//     ) : isError || isLocationsError ? (
//       <div className="text-red-500 text-center">{t("Error loading data")}</div>
//     ) : (
//     <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 sm:p-6">
//       <h2 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-gray-900 dark:text-white">
//         {isEditing ? t("Edit Reservation") : t("Create New Reservation")}
//       </h2>

//       <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-6">
//         {/* Basic Information */}
//         <div>
//           <h3 className="text-lg font-semibold mb-3 sm:mb-4 text-gray-800 dark:text-gray-200">
//             {t("Basic Information")}
//           </h3>
          
//           <div className="space-y-3 sm:space-y-4">
//             {/* Title field */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 {t("Title")} *
//               </label>
//               <input
//                 type="text"
//                 name="title"
//                 value={formData.title}
//                 onChange={handleInputChange}
//                 className="w-full px-4 py-2 sm:py-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                 required
//               />
//             </div>

//             {/* Description field */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 {t("Description")}
//               </label>
//               <textarea
//                 name="description"
//                 value={formData.description}
//                 onChange={handleInputChange}
//                 rows={3}
//                 className="w-full px-4 py-2 sm:py-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//               />
//             </div>

//             {/* Amount field */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 {t("Total Amount")} *
//               </label>
//               <input
//                 type="number"
//                 name="totalAmount"
//                 value={formData.totalAmount}
//                 onChange={handleInputChange}
//                 className="w-full px-4 py-2 sm:py-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                 required
//               />
//             </div>

//             {/* Date Range fields - Improved for mobile */}
//             <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                   {t("Start Date")} *
//                 </label>
//                 <input
//                   type="date"
//                   name="startDate"
//                   value={formData.startDate}
//                   onChange={handleInputChange}
//                   className="w-full px-4 py-2 sm:py-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                   required
//                 />
//               </div>
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                   {t("End Date")} *
//                 </label>
//                 <input
//                   type="date"
//                   name="endDate"
//                   value={formData.endDate}
//                   onChange={handleInputChange}
//                   className="w-full px-4 py-2 sm:py-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                   required
//                 />
//               </div>
//             </div>
//           </div>
//         </div>

//         {/* Advertisement Details */}
//         <div>
//           <h3 className="text-lg font-semibold mb-3 sm:mb-4 mt-4 lg:mt-0 text-gray-800 dark:text-gray-200">
//             {t("Advertisement Details")}
//           </h3>
          
//           <div className="space-y-3 sm:space-y-4">
//             {/* Client selection */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//               {t("Client")} *
//               </label>
//               <div className="flex-1">
//                   <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Contact")}</label>
//                   <select
//                       value={selectedContact?.email || ""}
//                       onChange={(e) => setSelectedContact(data?.contacts.find(contact => contact.email === e.target.value) || null)}
//                       className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 dark:text-gray-200"
//                   >
//                       <option value="">{t("Select Contact")}</option>
//                       {data?.contacts.map(contact => (
//                           <option key={contact.id || contact.email} value={contact.email}>
//                               {contact.name}
//                           </option>
//                       ))}
//                   </select>
//             </div>

//             {/* Location selection */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 {t("Location")} *
//               </label>
//               <select
//                         value={formData.locationId}
//                         onChange={(e) => {
//                           console.log("Location ID:", e.target.value);
                          
//                             const selectedLocation = locationList.find(location => location.id === e.target.value);
//                             console.log("Selected Location:", selectedLocation);
                            
//                             setSelectedLocationId(e.target.value);
//                             handleSelectChange("locationId", e.target.value);
//                         }}

//                         className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 dark:text-gray-200"
//                         >
//                         <option value="">{t("Select Location")}</option>
//                         {locationList.map(location => (
//                             <option key={location.id} value={location.id}>{location.name}</option>
//                         ))}
//                         </select>
//             </div>

//             {/* Location Capacity Information */}
//             {selectedLocation && (
//               <div className="mt-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
//                 <h4 className="font-medium text-sm mb-2 text-gray-700 dark:text-gray-300">
//                   {t("Location Capacity")}
//                 </h4>
//                 <div className="grid grid-cols-3 gap-2 text-center">
//                   <div className="p-2 bg-blue-50 dark:bg-blue-900 rounded">
//                     <div className="text-sm font-medium text-blue-800 dark:text-blue-200">
//                       {t("Total")}
//                     </div>
//                     <div className="text-xl font-bold text-blue-900 dark:text-blue-100">
//                       {selectedLocation.totalCapacity}
//                     </div>
//                   </div>
//                   <div className="p-2 bg-yellow-50 dark:bg-yellow-900 rounded">
//                     <div className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
//                       {t("Taken")}
//                     </div>
//                     <div className="text-xl font-bold text-yellow-900 dark:text-yellow-100">
//                       {selectedLocation.takenCapacity}
//                     </div>
//                   </div>
//                   <div className={`p-2 ${selectedLocation.availableCapacity > 0 ? 'bg-green-50 dark:bg-green-900' : 'bg-red-50 dark:bg-red-900'} rounded`}>
//                     <div className={`text-sm font-medium ${selectedLocation.availableCapacity > 0 ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'}`}>
//                       {t("Available")}
//                     </div>
//                     <div className={`text-xl font-bold ${selectedLocation.availableCapacity > 0 ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'}`}>
//                       {selectedLocation.availableCapacity}
//                     </div>
//                   </div>
//                 </div>

//                 {/* Capacity progress bar */}
//                 <div className="mt-3">
//                   <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
//                     <div 
//                       className={`h-2.5 rounded-full ${selectedLocation.availableCapacity > 0 ? 'bg-blue-600' : 'bg-red-600'}`}
//                       style={{ width: `${(selectedLocation.takenCapacity / selectedLocation.totalCapacity) * 100}%` }}
//                     ></div>
//                   </div>
//                   <div className="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
//                     <span>0</span>
//                     <span>{selectedLocation.totalCapacity}</span>
//                   </div>
//                 </div>

//                 {/* Required Capacity Selection */}
//                 <div className="mt-4">
//                   <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     {t("Required Capacity")} *
//                   </label>
//                   <div className="flex items-center">
//                     <input
//                       type="number"
//                       name="requiredCapacity"
//                       value={formData.requiredCapacity}
//                       onChange={handleInputChange}
//                       min="1"
//                       max={selectedLocation.availableCapacity}
//                       className="w-full px-4 py-2 sm:py-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                       required
//                     />
//                     <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
//                       / {selectedLocation.availableCapacity} {t("available")}
//                     </span>
//                   </div>
                  
//                   {formData.requiredCapacity > 0 && (
//                     <div className="mt-2">
//                       <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-600">
//                         <div 
//                           className="h-2 rounded-full bg-green-500 dark:bg-green-400"
//                           style={{ width: `${(formData.requiredCapacity / selectedLocation.availableCapacity) * 100}%` }}
//                         ></div>
//                       </div>
//                       <div className="text-xs text-right mt-1 text-gray-500 dark:text-gray-400">
//                         {((formData.requiredCapacity / selectedLocation.availableCapacity) * 100).toFixed(1)}% {t("of available capacity")}
//                       </div>
//                     </div>
//                   )}
//                 </div>
//               </div>
//             )}
//           </div>
//         </div>
//       </div>

//       {/* Installments - Mobile Responsive */}
//       <div className="mt-6 sm:mt-8">
//         <div className="flex flex-wrap justify-between items-center mb-4">
//           <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2 sm:mb-0">
//             {t("Payment Installments")}
//           </h3>
//           <button
//             type="button"
//             onClick={handleAddInstallment}
//             className="w-full sm:w-auto px-4 py-2 sm:py-3 mt-2 sm:mt-0 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center justify-center gap-2 transition-colors"
//           >
//             <FaPlus /> {t("Add Installment")}
//           </button>
//         </div>

//         <div className="mb-4">
//           <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//             {t("Number of Installments")}
//           </label>
//           <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
//             <input
//               type="number"
//               name="installmentCount"
//               value={formData.installmentCount}
//               onChange={handleInputChange}
//               className="w-full sm:w-32 px-4 py-2 sm:py-3 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//               min="1"
//             />
//             <button
//               type="button"
//               onClick={() => {
//                 if (formData.totalAmount <= 0 || formData.installmentCount <= 0) {
//                   alert(t("Total amount and installment count must be greater than zero"));
//                   return;
//                 }
//                 generateEqualInstallments();
//               }}
//               className="w-full sm:w-auto px-4 py-2 sm:py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center justify-center gap-2"
//             >
//               <FaCheck className="hidden sm:block" /> {t("Generate Equal Installments")}
//             </button>
//           </div>
//         </div>

//         {/* Responsive Installments Table/Cards */}
//         {formData.installments.length > 0 ? (
//           <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
//             <div className="hidden sm:block">
//               <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
//                 <thead className="bg-gray-50 dark:bg-gray-700">
//                   <tr>
//                     <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
//                       {t("Installment #")}
//                     </th>
//                     <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
//                       {t("Amount")}
//                     </th>
//                     <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
//                       {t("Due Date")}
//                     </th>
//                     <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
//                       {t("Status")}
//                     </th>
//                     <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
//                       {t("Actions")}
//                     </th>
//                   </tr>
//                 </thead>
//                 <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
//                   {formData.installments.map((installment, index) => (
//                     <tr key={index}>
//                       <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
//                         {index + 1}
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap">
//                         <input
//                           type="number"
//                           value={installment.amount}
//                           onChange={(e) => handleInstallmentChange(index, "amount", e.target.value)}
//                           className="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                         />
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap">
//                         <input
//                           type="date"
//                           value={installment.dueDate}
//                           onChange={(e) => handleInstallmentChange(index, "dueDate", e.target.value)}
//                           className="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                         />
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap">
//                         <select
//                           value={installment.status}
//                           onChange={(e) => handleInstallmentChange(index, "status", e.target.value)}
//                           className="w-full px-4 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                         >
//                           <option value="upcoming">{t("Upcoming")}</option>
//                           <option value="pending">{t("Pending")}</option>
//                           <option value="completed">{t("Completed")}</option>
//                           <option value="overdue">{t("Overdue")}</option>
//                           <option value="cancelled">{t("Cancelled")}</option>
//                         </select>
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap">
//                         <button
//                           type="button"
//                           onClick={() => handleRemoveInstallment(index)}
//                           className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
//                           aria-label={t("Remove installment")}
//                         >
//                           <FaTrash size={18} />
//                         </button>
//                       </td>
//                     </tr>
//                   ))}
//                 </tbody>
//               </table>
//             </div>

//             {/* Mobile Card View for Installments */}
//             <div className="sm:hidden">
//               {formData.installments.map((installment, index) => (
//                 <div key={index} className="p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
//                   <div className="flex justify-between items-center mb-3">
//                     <span className="font-semibold text-gray-700 dark:text-gray-300">
//                       {t("Installment")} #{index + 1}
//                     </span>
//                     <button
//                       type="button"
//                       onClick={() => handleRemoveInstallment(index)}
//                       className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-2"
//                       aria-label={t("Remove installment")}
//                     >
//                       <FaTrash size={16} />
//                     </button>
//                   </div>
//                   <div className="space-y-3">
//                     <div>
//                       <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
//                         {t("Amount")}
//                       </label>
//                       <input
//                         type="number"
//                         value={installment.amount}
//                         onChange={(e) => handleInstallmentChange(index, "amount", e.target.value)}
//                         className="w-full px-3 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                       />
//                     </div>
//                     <div>
//                       <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
//                         {t("Due Date")}
//                       </label>
//                       <input
//                         type="date"
//                         value={installment.dueDate}
//                         onChange={(e) => handleInstallmentChange(index, "dueDate", e.target.value)}
//                         className="w-full px-3 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                       />
//                     </div>
//                     <div>
//                       <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
//                         {t("Status")}
//                       </label>
//                       <select
//                         value={installment.status}
//                         onChange={(e) => handleInstallmentChange(index, "status", e.target.value)}
//                         className="w-full px-3 py-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
//                       >
//                         <option value="upcoming">{t("Upcoming")}</option>
//                         <option value="pending">{t("Pending")}</option>
//                         <option value="completed">{t("Completed")}</option>
//                         <option value="overdue">{t("Overdue")}</option>
//                         <option value="cancelled">{t("Cancelled")}</option>
//                       </select>
//                     </div>
//                   </div>
//                 </div>
//               ))}
//             </div>
//           </div>
//         ) : (
//           <div className="text-center py-6 bg-gray-100 dark:bg-gray-700 rounded-lg">
//             <p className="text-gray-500 dark:text-gray-400">{t("No installments added yet")}</p>
//           </div>
//         )}
//       </div>


//       {/* Form Actions - Mobile-friendly button layout */}
//       <div className="mt-6 sm:mt-8 flex flex-col sm:flex-row-reverse justify-end gap-3 sm:gap-4">
//         <button
//           type="button"
//           onClick={handleSubmit}
//           className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
//         >
//           {isEditing ? t("Update Reservation") : t("Create Reservation")}
//         </button>
//         <button
//           type="button"
//           onClick={onCancel}
//           className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors"
//         >
//           {t("Cancel")}
//         </button>
//       </div>
//     </div>
//     </div>
//     )
//   );
// };

// export default ReservationForm;

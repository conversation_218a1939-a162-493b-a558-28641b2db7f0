import React, { useState, useEffect } from "react";
import { Reservation } from "@/lib/interfaces/reservation";
import ReservationServices from "@/lib/reservations";
import { Loader2 } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";
import {
  FaEye,
  FaTimes,
  FaSearch,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaUser,
  FaMoneyBillAlt,
} from "react-icons/fa";

interface ReservationsTabProps {
  locationId: string;
}

const ReservationsTab: React.FC<ReservationsTabProps> = ({ locationId }) => {
  const { t, language } = useLanguage();
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState<{ start: string; end: string }>({
    start: "",
    end: "",
  });

  // Function to calculate status based on dates
  const calculateStatus = (startDate: string, endDate: string): string => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison

    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999); // End of day

    if (today < start) {
      return t("upcoming");
    } else if (today >= start && today <= end) {
      return t("active");
    } else {
      return t("completed");
    }
  };

  // Function to determine if we should use card view (mobile) or table view
  const [isCardView, setIsCardView] = useState(window.innerWidth < 1024); // Changed from 768 to 1024

  // Fixed fetch to avoid infinite loop - get service inside useEffect
  useEffect(() => {
    const fetchReservations = async () => {
      setIsLoading(true);
      try {
        const { getReservationsByLocation } = ReservationServices();

        // Create default date range for the API call
        const today = new Date();
        const startDate = new Date(today);
        startDate.setMonth(today.getMonth() - 3); // 3 months ago
        const endDate = new Date(today);
        endDate.setMonth(today.getMonth() + 3); // 3 months ahead

        // Format dates as ISO strings for the API
        const startDateString = startDate.toISOString().split("T")[0];
        const endDateString = endDate.toISOString().split("T")[0];

        const response = await getReservationsByLocation(
          locationId,
          startDateString,
          endDateString,
        );

        setReservations(response.reservations || []);
        setError(null);
      } catch (err) {
        console.error("Error fetching reservations:", err);
        setError("Failed to load reservation data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchReservations();
  }, [locationId]);

  // Listen for window resizes to toggle between card and table view
  useEffect(() => {
    const handleResize = () => {
      setIsCardView(window.innerWidth < 1024); // Changed from 768 to 1024
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Filter reservations based on search term and filters
  const filteredReservations = reservations.filter((reservation) => {
    const matchesSearch =
      reservation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.contact?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      false ||
      reservation.location?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      false;

    // Calculate dynamic status instead of using backend status
    const dynamicStatus = calculateStatus(
      reservation.start_date,
      reservation.end_date,
    );
    const matchesStatus =
      statusFilter === "all" || dynamicStatus === statusFilter;

    const startDate = dateFilter.start ? new Date(dateFilter.start) : null;
    const endDate = dateFilter.end ? new Date(dateFilter.end) : null;
    const reservationStart = new Date(reservation.start_date);
    const reservationEnd = new Date(reservation.end_date);

    const matchesDate =
      (!startDate ||
        reservationStart >= startDate ||
        reservationEnd >= startDate) &&
      (!endDate || reservationStart <= endDate);

    return matchesSearch && matchesStatus && matchesDate;
  });

  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setDateFilter({ start: "", end: "" });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === "ar" ? "ar-EG" : "en-US",
    );
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case t("active"):
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case t("upcoming"):
        return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
      case t("completed"):
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  // Handle reservation click - readonly view
  const handleReservationClick = (id: string) => {
    console.log(`View reservation details for: ${id}`);
    // You could implement a details modal here in the future
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return <div className="py-8 text-center text-red-500">{error}</div>;
  }

  return (
    <div className="w-full max-w-full overflow-hidden">
      {/* Filters - Improved for mobile */}
      <div className="mb-4 w-full max-w-full overflow-hidden px-3 sm:mb-6 sm:px-6">
        <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:hidden">
          {t("Search & Filters")}
        </h3>
        <div className="flex flex-col space-y-3">
          {/* Search - Full width on mobile */}
          <div className="w-full">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t(
                  "Search reservations... (Title, Client, Location)",
                )}
                className="w-full rounded-lg border py-2 pl-10 pr-10 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:py-3"
              />
              <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 transform text-sm text-gray-400" />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform text-gray-400 hover:text-gray-600"
                >
                  <FaTimes className="text-sm" />
                </button>
              )}
            </div>
          </div>

          {/* Filter Controls */}
          <div className="flex w-full flex-col gap-3">
            {/* Status Filter */}
            <div className="w-full">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full rounded-lg border px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
              >
                <option value="all">{t("All Statuses")}</option>
                <option value="active">{t("Active")}</option>
                <option value="upcoming">{t("Upcoming")}</option>
                <option value="completed">{t("Completed")}</option>
              </select>
            </div>

            {/* Date Range */}
            <div className="grid w-full grid-cols-1 gap-3 sm:grid-cols-2">
              <div className="relative w-full">
                <FaCalendarAlt className="absolute left-3 top-1/2 -translate-y-1/2 transform text-sm text-gray-400" />
                <input
                  type="date"
                  value={dateFilter.start}
                  onChange={(e) =>
                    setDateFilter({ ...dateFilter, start: e.target.value })
                  }
                  className="w-full rounded-lg border py-2 pl-10 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                  placeholder={t("Start date")}
                />
              </div>
              <div className="relative w-full">
                <input
                  type="date"
                  value={dateFilter.end}
                  onChange={(e) =>
                    setDateFilter({ ...dateFilter, end: e.target.value })
                  }
                  className="w-full rounded-lg border px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                  placeholder={t("End date")}
                />
              </div>
            </div>

            {/* Reset Button */}
            <button
              onClick={resetFilters}
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-red-500 px-4 py-2 text-sm text-white transition-colors hover:bg-red-600 sm:w-auto"
            >
              <FaTimes className="text-sm" />
              <span>{t("Reset Filters")}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="w-full max-w-full overflow-hidden px-3 sm:px-6">
        {/* Card View for Mobile/Tablet */}
        {isCardView ? (
          <div className="w-full max-w-full space-y-4 overflow-hidden">
            {filteredReservations.length > 0 ? (
              filteredReservations.map((reservation) => {
                const dynamicStatus = calculateStatus(
                  reservation.start_date,
                  reservation.end_date,
                );
                return (
                  <div
                    key={reservation.id}
                    className="w-full max-w-full cursor-pointer overflow-hidden rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
                    onClick={() => handleReservationClick(reservation.id)}
                  >
                    <div className="mb-3 flex w-full items-start justify-between">
                      <div className="mr-3 min-w-0 flex-1">
                        <h3 className="truncate font-medium text-gray-900 dark:text-white">
                          {reservation.title}
                        </h3>
                        <p className="truncate text-xs text-gray-500 dark:text-gray-400">
                          ID: {reservation.id}
                        </p>
                      </div>
                      <span
                        className={`whitespace-nowrap rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(dynamicStatus)}`}
                      >
                        {dynamicStatus.charAt(0).toUpperCase() +
                          dynamicStatus.slice(1)}
                      </span>
                    </div>

                    <div className="w-full space-y-2 text-sm">
                      <div className="flex w-full items-center text-gray-500 dark:text-gray-400">
                        <FaUser className="mr-2 flex-shrink-0" size={12} />
                        <span className="truncate">
                          {reservation.contact?.name || "N/A"}
                        </span>
                      </div>
                      <div className="flex w-full items-center text-gray-500 dark:text-gray-400">
                        <FaMapMarkerAlt
                          className="mr-2 flex-shrink-0"
                          size={12}
                        />
                        <span className="truncate">
                          {reservation.location?.name || "N/A"}
                        </span>
                      </div>
                      <div className="flex w-full items-start text-gray-500 dark:text-gray-400">
                        <FaCalendarAlt
                          className="mr-2 mt-0.5 flex-shrink-0"
                          size={12}
                        />
                        <div className="flex w-full min-w-0 flex-col sm:flex-row sm:items-center">
                          <span className="truncate text-xs">
                            {formatDate(reservation.start_date)}
                          </span>
                          <span className="mx-1 hidden text-xs sm:inline">
                            -
                          </span>
                          <span className="truncate text-xs">
                            {formatDate(reservation.end_date)}
                          </span>
                        </div>
                      </div>
                      <div className="flex w-full items-center font-medium text-gray-900 dark:text-white">
                        <FaMoneyBillAlt
                          className="mr-2 flex-shrink-0"
                          size={12}
                        />
                        <span className="truncate">
                          $
                          {Number(reservation.total_amount || 0).toLocaleString(
                            language === "ar" ? "ar-EG" : "en-US",
                          )}
                        </span>
                      </div>
                    </div>

                    <div className="mt-3 flex justify-end border-t border-gray-200 pt-3 dark:border-gray-700">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleReservationClick(reservation.id);
                        }}
                        className="p-2 text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                      >
                        <FaEye size={16} />
                      </button>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="py-8 text-center text-gray-500 dark:text-gray-400">
                {t("No reservations found")}
              </div>
            )}
          </div>
        ) : (
          /* Responsive Table View for Desktop */
          <div className="w-full max-w-full overflow-hidden">
            <div className="w-full overflow-x-auto">
              <table className="w-full min-w-0 divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="w-32 min-w-0 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("Title")}
                    </th>
                    <th className="w-24 min-w-0 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("Client")}
                    </th>
                    <th className="w-24 min-w-0 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("Location")}
                    </th>
                    <th className="w-32 min-w-0 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("Period")}
                    </th>
                    <th className="w-24 min-w-0 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("Amount")}
                    </th>
                    <th className="w-20 min-w-0 px-3 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      {t("Status")}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                  {filteredReservations.length > 0 ? (
                    filteredReservations.map((reservation) => {
                      const dynamicStatus = calculateStatus(
                        reservation.start_date,
                        reservation.end_date,
                      );
                      return (
                        <tr
                          key={reservation.id}
                          className="cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700"
                          onClick={() => handleReservationClick(reservation.id)}
                        >
                          <td
                            className="max-w-0 truncate px-3 py-4 text-xs font-medium text-gray-900 dark:text-white"
                            title={reservation.title}
                          >
                            {reservation.title}
                          </td>
                          <td
                            className="max-w-0 truncate px-3 py-4 text-xs text-gray-500 dark:text-gray-400"
                            title={reservation.contact?.name || "N/A"}
                          >
                            {reservation.contact?.name || "N/A"}
                          </td>
                          <td
                            className="max-w-0 truncate px-3 py-4 text-xs text-gray-500 dark:text-gray-400"
                            title={reservation.location?.name || "N/A"}
                          >
                            {reservation.location?.name || "N/A"}
                          </td>
                          <td className="max-w-0 truncate px-3 py-4 text-xs text-gray-500 dark:text-gray-400">
                            <div className="flex flex-col">
                              <span>{formatDate(reservation.start_date)}</span>
                              <span>{formatDate(reservation.end_date)}</span>
                            </div>
                          </td>
                          <td className="max-w-0 truncate px-3 py-4 text-xs font-medium text-gray-900 dark:text-white">
                            {Number(
                              reservation.total_amount || 0,
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}
                          </td>
                          <td className="px-3 py-4">
                            <span
                              className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(dynamicStatus)}`}
                            >
                              {dynamicStatus.charAt(0).toUpperCase() +
                                dynamicStatus.slice(1)}
                            </span>
                          </td>
                        </tr>
                      );
                    })
                  ) : (
                    <tr>
                      <td
                        colSpan={8}
                        className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        {t("No reservations found")}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReservationsTab;

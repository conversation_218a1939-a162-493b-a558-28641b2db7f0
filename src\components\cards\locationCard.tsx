import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/cards/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Building, Calendar, DollarSign, User, Mail, Phone } from "lucide-react"
import useLanguage from "@/hooks/useLanguage"
import type { Location } from "@/lib/types/location"

interface LocationCardProps {
  location: Location
  onSelect: (location: Location) => void
  isSelected: boolean
}

export const LocationCard: React.FC<LocationCardProps> = ({ location, onSelect, isSelected }) => {
  const { t } = useLanguage()

  const colors = {
    office: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    warehouse: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    retail: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    other: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
  }

  const getTypeColor = (type: keyof typeof colors) => {
    return colors[type] || colors.other
  }

  return (
    <Card
      onClick={() => onSelect(location)}
      className={`cursor-pointer transition-all hover:shadow-md ${isSelected ? "ring-2 ring-primary" : ""}`}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-2">
            <MapPin className="h-5 w-5 text-muted-foreground mt-1" />
            <div>
              <CardTitle className="text-lg">{location.name}</CardTitle>
              <p className="text-sm text-muted-foreground">{location.address}</p>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <Badge variant="secondary" className={getTypeColor(location.type as keyof typeof colors)}>
              {t(location.type)}
            </Badge>
            <Badge variant={location.status === "active" ? "default" : "secondary"}>{t(location.status)}</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <InfoSection
            icon={Building}
            label={t("ownership")}
            value={`${location.ownedBy} (${location.ourPercentage}%)`}
          />
          <InfoSection
            icon={Calendar}
            label={t("reservation")}
            value={
              location.reservedBy
                ? `${location.reservedBy} (${new Date(location.reservedFrom).toLocaleDateString()} - ${new Date(location.reservedUntil).toLocaleDateString()})`
                : t("notReserved")
            }
          />

          <InfoSection icon={User} label={t("contact")} value={location.contactPerson} />
          <InfoSection icon={Mail} label={t("email")} value={location.contactEmail} />
          <InfoSection icon={Phone} label={t("phone")} value={location.contactPhone} />
        </div>
      </CardContent>
    </Card>
  )
}

const InfoSection: React.FC<{ icon: React.ElementType; label: string; value: string }> = ({
  icon: Icon,
  label,
  value,
}) => (
  <div className="flex items-center space-x-2">
    <Icon className="h-4 w-4 text-muted-foreground" />
    <span className="text-sm font-medium">{label}:</span>
    <span className="text-sm">{value}</span>
  </div>
)


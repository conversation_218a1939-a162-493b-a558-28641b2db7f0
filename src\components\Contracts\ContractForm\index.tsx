import React, { useState } from "react";
import { ContractFormData } from "@/lib/interfaces/contract";
import { useContacts } from "@/hooks/useContact";
import { useLocations } from "@/hooks/useLocations";
import useLanguage from "@/hooks/useLanguage";
import { useExpenseTypes } from "@/hooks/useExpensesTypes";
import { usePermissions } from "@/hooks/usePermissions";
import ContractServices from "@/lib/contracts";
import UploadFiles from "@/components/common/uploadFile";
import { SuccessPopup } from "@/components/common/successPopUp";
import { ErrorPopup } from "@/components/common/errorPopUp";
import { set } from "date-fns";
import InstallmentsComponent from "@/components/common/installments";
import { DataImporter } from "@/components/Importer/DataImporter";
import UploadContractPopUp from "./UploadContractPopUp";
import InstallmentsPopUp from "./InstallmentsPopUp";
import { Contract } from "@/lib/interfaces/contract";

interface DocumentUpload {
  url: string;
}

interface ContractFormProps {
  contract?: ContractFormData;
  onCancel: () => void;
  onUpdate?: (contract: Contract) => void;
  isUpdating?: boolean;
}

const ContractForm: React.FC<ContractFormProps> = ({
  contract,
  onCancel,
  onUpdate,
  isUpdating = false,
}) => {
  const { t, language } = useLanguage();
  const isEditing = !!contract;
  const { hasPermission } = usePermissions();
  const { data: contacts } = useContacts();
  const { data: locations } = useLocations();
  const { data: expenseTypes } = useExpenseTypes();
  const { createContract, updateContract } = ContractServices();
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [showErrorPopup, setShowErrorPopup] = useState(false);
  const [message, setMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const [formData, setFormData] = useState<ContractFormData>({
    priority: contract?.priority || "medium",
    contact_id: contract?.contact_id || "",
    location_id: contract?.location_id || "",
    type_id: contract?.type_id || "",
    installmentCount: contract?.installmentCount ?? 0,
    contract: {
      id: contract?.contract.id || "",
      title: contract?.contract.title || "",
      description: contract?.contract.description || "",
      start_date: contract?.contract.start_date
        ? (() => {
            try {
              const date = new Date(contract.contract.start_date);
              return isNaN(date.getTime())
                ? ""
                : date.toISOString().split("T")[0];
            } catch {
              return "";
            }
          })()
        : "",
      end_date: contract?.contract.end_date
        ? (() => {
            try {
              const date = new Date(contract.contract.end_date);
              return isNaN(date.getTime())
                ? ""
                : date.toISOString().split("T")[0];
            } catch {
              return "";
            }
          })()
        : "",
      total_amount: contract?.contract.total_amount || 0,
      status: contract?.contract.status || "active",
      notes: contract?.contract.notes || "",
    },
    installments: contract?.installments || [],
    documentUpload: contract?.documentUpload ?? [],
    renewal_terms: contract?.renewal_terms || {
      auto_renew: false,
      increase_percentage: 0,
      notice_period_days: 0,
    },
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
    path: string,
  ) => {
    const value = e.target.type === "number" ? +e.target.value : e.target.value;

    setFormData((prev) => {
      const updated = { ...prev };
      const keys = path.split(".");
      let current: any = updated;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      current[keys[keys.length - 1]] = value;
      return updated;
    });
  };

  const handleDateChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    path: string,
  ) => {
    const value = e.target.value;

    setFormData((prev) => {
      const updated = { ...prev };
      const keys = path.split(".");
      let current: any = updated;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }

      // Handle date validation
      if (path === "contract.start_date") {
        current[keys[keys.length - 1]] = value;

        // If start date is after end date, clear end date
        if (value && updated.contract.end_date && new Date(value) > new Date(updated.contract.end_date)) {
          updated.contract.end_date = "";
        }
      } else if (path === "contract.end_date") {
        // Only set end date if it's after start date
        if (!value || !updated.contract.start_date || new Date(value) >= new Date(updated.contract.start_date)) {
          current[keys[keys.length - 1]] = value;
        } else {
          // Don't update if end date is before start date
          return prev;
        }
      } else {
        current[keys[keys.length - 1]] = value;
      }

      return updated;
    });
  };

  const totalExpenseAmount = formData.installments.reduce((sum, event) => {
    // When editing contracts, only include expenses in the total
    if (isEditing && (event as any).type) {
      return (event as any).type === "expense"
        ? sum + Number(event.amount || 0)
        : sum;
    }
    // When creating contracts, include all installments (they're all expenses by default)
    return sum + Number(event.amount || 0);
  }, 0);
  const handleExpenseChange = (
    index: number,
    field: string,
    value: string | number,
  ) => {
    setFormData((prev) => {
      const updatedExpenses = [...prev.installments];
      updatedExpenses[index] = {
        ...updatedExpenses[index],
        [field]: value,
      };
      return {
        ...prev,
        installments: updatedExpenses,
      };
    });
  };
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
    }).format(amount);
  };
  const addExpenseEvent = () => {
    setFormData((prev) => ({
      ...prev,
      installments: [
        ...prev.installments,
        {
          amount: 0,
          due_date: "",
          notification_view_date: "",
          status: "pending",
        },
      ],
    }));
  };

  const removeExpenseEvent = (index: number) => {
    const updated = [...formData.installments];
    updated.splice(index, 1);
    setFormData((prev) => ({ ...prev, installments: updated }));
  };
  const handleSubmit = async () => {
    console.log("Form Data Before Submission:", formData);

    // Check permissions before proceeding
    const requiredPermission = isEditing ? "edit" : "create";
    if (!hasPermission("contracts", requiredPermission)) {
      console.error(`User lacks ${requiredPermission} permission for contracts`);
      return;
    }

    // Prevent multiple submissions
    if (loading || isUpdating) return;

    setLoading(true);
    console.log("Submitting Form Data:", formData);

    const errors: string[] = [];

    if (!formData.contract.title.trim())
      errors.push(t("validation.titleRequired"));
    if (!formData.contact_id) errors.push(t("validation.contactRequired"));
    if (!formData.location_id) errors.push(t("validation.locationRequired"));
    if (!formData.type_id) errors.push(t("validation.typeRequired"));
    if (!formData.contract.start_date)
      errors.push(t("validation.startDateRequired"));
    if (!formData.contract.end_date)
      errors.push(t("validation.endDateRequired"));
    if (
      !formData.contract.total_amount ||
      formData.contract.total_amount <= 0
    ) {
      errors.push(t("validation.amountPositive"));
    }

    if (errors.length > 0) {
      alert(errors.join("\n"));
      setLoading(false);
      return;
    }

    console.log("Validated Form Data:", formData); // Transform installments data for API submission when editing contracts
    let submissionData: any = { ...formData };

    if (isEditing && formData.installments.length > 0) {
      const income_events: any[] = [];
      const expense_events: any[] = [];

      formData.installments.forEach((installment: any) => {
        const eventData: any = {
          id: installment.id,
          title: installment.title || "",
          description: installment.description || "",
          amount: installment.amount || 0,
          due_date: installment.due_date
            ? `${installment.due_date}T00:00:00Z`
            : "",
          status: installment.status || "pending",
          priority: installment.priority || "medium",
          paid_date: installment.paid_date
            ? `${installment.paid_date}T00:00:00Z`
            : undefined,
          contact_id: installment.contact_id || formData.contact_id,
          location_id: installment.location_id || formData.location_id,
          is_deleted: installment.is_deleted || false,
        };

        if (installment.type === "income") {
          if (installment.expense_id) {
            eventData.expense_id = installment.expense_id;
          }
          income_events.push(eventData);
        } else if (installment.type === "expense") {
          eventData.type_id = installment.type_id || formData.type_id;
          expense_events.push(eventData);
        }
      });

      // Add the events to submission data for contract editing
      submissionData = {
        ...submissionData,
        income_events,
        expense_events,
      };
    }

    try {
      const response = isEditing
        ? await updateContract(formData.contract.id, submissionData)
        : await createContract(formData);

      console.log(
        `Contract ${isEditing ? "updated" : "created"} successfully:`,
        response,
      );

      if (isEditing && onUpdate && response) {
        // For editing: call the update handler and let parent handle UI
        onUpdate(response);
      } else {
        // For creation: show success message and close after delay
        setMessage(t("Contract created successfully"));
        setShowSuccessPopup(true);

        setTimeout(() => {
          onCancel();
        }, 1500);
      }
    } catch (error: unknown) {
      console.error(
        `Error ${isEditing ? "updating" : "creating"} contract:`,
        error,
      );

      const errorResponse = error as any;
      if (errorResponse?.response?.data?.error) {
        setErrorMessage(errorResponse.response.data.error);
      } else {
        setErrorMessage(
          t(
            isEditing
              ? "Error in updating contract"
              : "Error in creating contract",
          ),
        );
      }
      setShowErrorPopup(true);
    } finally {
      setLoading(false);
    }
  };

  const handleFilesUpload = (urls: string[]) => {
    setFormData((prev) => ({
      ...prev,
      documentUpload: [
        ...(prev.documentUpload || []),
        ...urls.map((url, index) => ({
          id: `doc-${Date.now()}-${index}`,
          name: `Document ${index + 1}`,
          uploadDate: new Date().toISOString(),
          type: "uploaded",
          fileType: "unknown",
          url,
        })),
      ],
    }));
  };

  const handleImport = async (importedEvents: any[]) => {
    // Check import permission before proceeding
    if (!hasPermission("contracts", "import_data")) {
      console.error("User lacks import_data permission for contracts");
      return;
    }

    console.log("Imported Events:", importedEvents);

    importedEvents.forEach(async (event) => {
      await createContract({
        contract: {
          id: event.id || "",
          title: event.title || "",
          description: event.description || "",
          start_date: event.startDate || "",
          end_date: event.endDate || "",
          total_amount: event.amount || 0,
          notes: event.notes || "",
        },
        // contact_id: // find contact id by name
        contact_id:
          contacts?.contacts.find((c) => c.name === event.contact)?.id || "",
        location_id:
          locations?.locations.find((l) => l.name === event.location)?.id || "",
        type_id:
          expenseTypes?.event_types.find((t) => t.name === event.type)?.id ||
          "",
        priority: event.priority || "medium",
        installmentCount: event.installments?.length || 0,
        installments: event.installments || [],
        documentUpload: event.documentUpload || [],
      });
    });
    setMessage(t("Contracts imported successfully"));
    setShowSuccessPopup(true);
  };

  const category = "expense"; // adjust based on your logic
  const types = { expense: ["rent", "utilities", "supplies"] }; // sample types
  const locationList = locations?.locations || [];

  const isSubmitting = loading || isUpdating;

  return (
    <>
      {showSuccessPopup && (
        <SuccessPopup
          message={message}
          onClose={() => setShowSuccessPopup(false)}
        />
      )}
      {showErrorPopup && (
        <ErrorPopup
          message={errorMessage}
          onClose={() => setShowErrorPopup(false)}
        />
      )}

      {/* Loading overlay for updates */}
      {isUpdating && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="rounded-lg bg-white p-6 shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
              <span className="text-gray-700">{t("Updating contract...")}</span>
            </div>
          </div>
        </div>
      )}

      <h2 className="text-3xl font-bold text-gray-800">
        {isEditing ? t("Edit Contract") : t("New Contract")}
      </h2>

      {!isEditing && hasPermission("contracts", "import_data") && (
        <div className="flex justify-end">
          <button
            type="button"
            onClick={() => setIsImporting(!isImporting)}
            className="rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            {isImporting ? t("Close Import") : t("Import Contracts")}
          </button>
        </div>
      )}
      {isImporting ? (
        <DataImporter<ContractFormData>
          columns={[
            {
              key: "title",
              label: t("title"),
              type: "text",
              required: true,
            },
            {
              key: "amount",
              label: t("amount"),
              type: "text",
              required: true,
            },
            {
              key: "category",
              label: t("category"),
              type: "dropdown",
              required: true,
              options: ["income", "expense"],
            },
            {
              key: "type",
              label: t("type"),
              type: "dropdown",
              required: false,
              options: [...new Set(types[category] || [])],
            },
            {
              key: "status",
              label: t("status"),
              type: "dropdown",
              required: true,
              options: ["completed", "pending", "cancelled", "upcoming", "overdue"],
            },
            {
              key: "priority",
              label: t("priority"),
              type: "dropdown",
              required: false,
              options: ["low", "medium", "high"],
            },
            {
              key: "contact",
              label: t("contact"),
              type: "dropdown",
              required: false,
              options: [...new Set(contacts?.contacts.map((c) => c.name) || [])],
            },
            {
              key: "location",
              label: t("location"),
              type: "dropdown",
              required: false,
              options: [...new Set(locationList.map((location) => location.name))],
            },
            {
              key: "startDate",
              label: t("startDate"),
              type: "date",
              required: false,
            },
            {
              key: "endDate",
              label: t("endDate"),
              type: "date",
              required: false,
            },
            {
              key: "documentUpload",
              label: t("documentUpload"),
              type: "modal",
              required: false,
              component: UploadContractPopUp,
            },
            {
              key: "installments",
              label: t("installments"),
              type: "modal",
              required: false,
              component: InstallmentsPopUp,
            },
          ]}
          onImport={handleImport}
        />
      ) : (
        <>
          <form
            className="max-w-8xl mx-auto w-full space-y-8 rounded-2xl bg-white p-8 shadow-lg"
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit();
            }}
          >
            {/* General Info */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Title")}
                </label>
                <input
                  type="text"
                  value={formData.contract.title}
                  onChange={(e) => handleInputChange(e, "contract.title")}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Contact")}
                </label>
                <select
                  value={formData.contact_id}
                  onChange={(e) => handleInputChange(e, "contact_id")}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">{t("Select Contact")}</option>
                  {contacts?.contacts.map((c) => (
                    <option key={c.id} value={c.id}>
                      {c.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Location")}
                </label>
                <select
                  value={formData.location_id}
                  onChange={(e) => handleInputChange(e, "location_id")}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">{t("Select Location")}</option>
                  {locations?.locations.map((l) => (
                    <option key={l.id} value={l.id}>
                      {l.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Type")}
                </label>
                <select
                  value={formData.type_id}
                  onChange={(e) => handleInputChange(e, "type_id")}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">{t("Select Type")}</option>
                  {expenseTypes?.event_types.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            {/* Contract Details */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Start Date")}
                </label>
                <input
                  type="date"
                  value={formData.contract.start_date}
                  onChange={(e) => handleDateChange(e, "contract.start_date")}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("End Date")}
                </label>
                <input
                  type="date"
                  value={formData.contract.end_date}
                  onChange={(e) => handleDateChange(e, "contract.end_date")}
                  min={formData.contract.start_date || undefined}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {formData.contract.start_date && !formData.contract.end_date && (
                  <p className="mt-1 text-sm text-blue-600 dark:text-blue-400">
                    {t("Please select an end date")}
                  </p>
                )}
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Priority")}
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => handleInputChange(e, "priority")}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3"
                >
                  <option value="high">{t("High")}</option>
                  <option value="medium">{t("Medium")}</option>
                  <option value="low">{t("Low")}</option>
                </select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Total Amount")}
                </label>
                <input
                  type="number"
                  value={formData.contract.total_amount}
                  onChange={(e) =>
                    handleInputChange(e, "contract.total_amount")
                  }
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="md:col-span-2">
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Description")}
                </label>
                <textarea
                  value={formData.contract.description}
                  onChange={(e) => handleInputChange(e, "contract.description")}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="md:col-span-2">
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Notes")}
                </label>
                <textarea
                  value={formData.contract.notes}
                  onChange={(e) => handleInputChange(e, "contract.notes")}
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Auto Renew")}
                </label>
                <input
                  type="checkbox"
                  checked={formData.renewal_terms?.auto_renew || false}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      renewal_terms: {
                        auto_renew: e.target.checked,
                        increase_percentage:
                          prev.renewal_terms?.increase_percentage || 0,
                        notice_period_days:
                          prev.renewal_terms?.notice_period_days || 0,
                      },
                    }))
                  }
                  className="h-6 w-6"
                />
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Increase Percentage")}
                </label>
                <input
                  type="number"
                  value={formData.renewal_terms?.increase_percentage || 0}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      renewal_terms: {
                        increase_percentage: Number(e.target.value) || 0,
                        auto_renew: prev.renewal_terms?.auto_renew || false,
                        notice_period_days:
                          prev.renewal_terms?.notice_period_days || 0,
                      },
                    }))
                  }
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  {t("Notice Period (Days)")}
                </label>
                <input
                  type="number"
                  value={formData.renewal_terms?.notice_period_days || 0}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      renewal_terms: {
                        notice_period_days: Number(e.target.value) || 0,
                        auto_renew: prev.renewal_terms?.auto_renew || false,
                        increase_percentage:
                          prev.renewal_terms?.increase_percentage || 0,
                      },
                    }))
                  }
                  className="w-full rounded-xl border border-gray-300 px-4 py-3 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            {/* Document Upload */}
            <div className="md:col-span-2">
              <UploadFiles onUploadComplete={handleFilesUpload} />
              {(formData.documentUpload?.length || 0) > 0 && (
                <ul className="mt-2 space-y-1 text-sm text-green-600">
                  {(formData.documentUpload || []).map((url, index) => (
                    <li key={index}>
                      <a
                        href={url.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="underline"
                      >
                        {t("Document")} {index + 1}
                      </a>
                    </li>
                  ))}
                </ul>
              )}
            </div>{" "}
            <InstallmentsComponent
              formData={formData}
              setFormData={setFormData}
              isEditingContract={isEditing}
            />
            {formData.installments.length > 0 && (
              <div className="mt-2 text-right font-semibold text-gray-800">
                {t("Total Amount")}: {formatCurrency(totalExpenseAmount)}
              </div>
            )}
            {/* Buttons */}
            <div className="flex justify-end space-x-4 pt-6">
              <button
                type="button"
                onClick={onCancel}
                disabled={isSubmitting}
                className="rounded-xl bg-gray-100 px-5 py-3 font-medium text-gray-700 hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {t("Cancel")}
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center space-x-2 rounded-xl bg-blue-600 px-6 py-3 font-semibold text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isSubmitting && (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                )}
                <span>
                  {isSubmitting
                    ? t(isEditing ? "Updating..." : "Saving...")
                    : t(isEditing ? "Update Contract" : "Save Contract")}
                </span>
              </button>
            </div>
          </form>
        </>
      )}
    </>
  );
};

export default ContractForm;

import { Contact, Location } from './finaces';

export interface Installment {
  id: number;
  amount: number;
  dueDate: string;
  status: 'upcoming' | 'pending' | 'completed' | 'overdue' | 'cancelled';
  financialEventId?: number; // Reference to the created financial event
  notificationDate?: string;
  priority?: 'low' | 'medium' | 'high';
  received_date?: string; // Date when the installment was received
}

export interface Reservation {
  id: string;
  title: string;
  description: string;
  total_amount: number;
  reservationDate: string; 
  start_date: string; 
  end_date: string; 
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  contactId: string;
  contact: Contact;
  locationId: string;
  location: Location;
  adType: string; 
  adPlacement: string; 
  installments: Installment[];
  created_at: string; 
  updated_at: string; 
  created_by: string;
  notes: string;
  actual_amount?: number;
  required_capacity: number;
}

export interface ReservationFormData {
  title: string;
  description: string;
  totalAmount: number;
  startDate: string; 
  endDate: string;
  contactId: string;
  locationId: string;
  adType: string;
  adPlacement: string;
  installmentCount: number;
  installmentType: 'equal' | 'custom';
  installments: Omit<Installment, 'id' | 'financialEventId'>[];
  notes: string;
  required_capacity: number;
}

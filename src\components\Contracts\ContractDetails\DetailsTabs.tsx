import React from "react";
import useLanguage from "@/hooks/useLanguage";

interface DetailsTabsProps {
  activeTab: string;
  onTabChange: (tab: "details" | "payments" | "renewal" | "documents") => void;
}

const DetailsTabs: React.FC<DetailsTabsProps> = ({ activeTab, onTabChange }) => {
  const { t } = useLanguage();

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 mb-6 overflow-x-auto">
      <nav className="flex min-w-full whitespace-nowrap" aria-label="Tabs">
        <button
          onClick={() => onTabChange("details")}
          className={`py-3 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm flex-1 sm:flex-none ${
            activeTab === "details"
              ? "border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"
          }`}
        >
          {t("Details")}
        </button>
        <button
          onClick={() => onTabChange("payments")}
          className={`py-3 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm flex-1 sm:flex-none ${
            activeTab === "payments"
              ? "border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"
          }`}
        >
          {t("payments")}
        </button>
        <button
          onClick={() => onTabChange("renewal")}
          className={`py-3 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm flex-1 sm:flex-none ${
            activeTab === "renewal"
              ? "border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"
          }`}
        >
          {t("renewal")}
        </button>
        <button
          onClick={() => onTabChange("documents")}
          className={`py-3 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm flex-1 sm:flex-none ${
            activeTab === "documents"
              ? "border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"
          }`}
        >
          {t("documents")}
        </button>
      </nav>
    </div>
  );
};

export default DetailsTabs;

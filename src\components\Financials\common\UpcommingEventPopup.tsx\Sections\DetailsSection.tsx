import React from "react";

interface DetailsSectionProps {
  event: any;
  isEditing: boolean;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const DetailsSection: React.FC<DetailsSectionProps> = ({ event, isEditing, onInputChange }) => {
  return (
    <div className="border-r border-gray-300 dark:border-gray-700 pr-6">
      <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4 text-center">Details</h3>
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">Amount:</label>
            {isEditing ? (
              <input
                type="text"
                name="amount"
                value={event.amount}
                onChange={onInputChange}
                className="w-full p-2 border rounded-lg dark:bg-gray-700 dark:text-white"
              />
            ) : (
              <p className="text-gray-600 dark:text-gray-400 font-semibold">${event.amount}</p>
            )}
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">Due Date:</label>
            {isEditing ? (
              <input
                type="date"
                name="dueDate"
                value={event.dueDate}
                onChange={onInputChange}
                className="w-full p-2 border rounded-lg dark:bg-gray-700 dark:text-white"
              />
            ) : (
              <p className="text-gray-600 dark:text-gray-400 font-semibold">
                {new Date(event.dueDate).toLocaleDateString()}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsSection;
import React, { useState, useMemo, useEffect, useCallback } from "react";
import {
  Search,
  Filter,
  X,
  MapPin,
  Sliders,
  Building,
  Percent,
  Calendar,
  Check,
  AlertCircle,
  PercentCircleIcon,
  Edit,
  Trash2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import DeleteConfirmationModal from "../Modals/DeleteConfirmationModal";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/cards/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Location } from "@/lib/types/location";
import useLanguage from "@/hooks/useLanguage";
// Import the JSON data
import { useLocations, useLocationServices } from "@/hooks/useLocations";

interface LocationListProps {
  onSelect: (location: Location) => void;
  onEdit?: (location: Location) => void; // Add onEdit prop
}

const LocationList: React.FC<LocationListProps> = ({ onSelect, onEdit }) => {
  const { t, language } = useLanguage();
  const isRTL = language === "ar";
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    type: [] as string[],
    status: [] as string[], // Will now be "reserved" or "not-reserved"
    ownership: [] as string[],
    capacity: { min: 0, max: 0 },
    revenue: { min: 0, max: 0 },
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilterTab, setActiveFilterTab] = useState("type");

  // Remove hardcoded locationTypes and add state for real location types
  const [locationTypes, setLocationTypes] = useState<
    Array<{ id: string; name: string }>
  >([]);
  const statusTypes = ["reserved", "not-reserved"]; // Updated status types
  const { data: locationsData, isLoading, refetch } = useLocations();
  const locationServices = useLocationServices();
  const [locations, setLocations] = useState<Location[]>([]);

  // New state for reservation management
  const [reservationStates, setReservationStates] = useState<
    Record<
      string,
      {
        showDatePicker: boolean;
        startDate: string;
        endDate: string;
        isSubmitting: boolean;
      }
    >
  >({});

  // New state for deletion
  const [deleteModalState, setDeleteModalState] = useState<{
    isOpen: boolean;
    locationId: string | null;
    locationName: string;
    isDeleting: boolean;
    error: string | null;
  }>({
    isOpen: false,
    locationId: null,
    locationName: "",
    isDeleting: false,
    error: null,
  });

  // Memoize the getAllLocationTypes function to prevent recreation on every render
  const getAllLocationTypes = useCallback(() => {
    return locationServices.getAllLocationTypes();
  }, [locationServices]);

  // Fetch location types when component mounts - with empty dependency array
  useEffect(() => {
    const fetchLocationTypes = async () => {
      try {
        const types = await getAllLocationTypes();
        setLocationTypes(types);
      } catch (error) {
        console.error("Error fetching location types:", error);
        // Fallback to empty array if fetch fails
        setLocationTypes([]);
      }
    };

    fetchLocationTypes();
  }, []); // Empty dependency array - only run once on mount

  // Convert the JSON data dates to Date objects

  // Enhanced filter logic
  const filteredLocations = useMemo(() => {
    return locations.filter((location) => {
      const matchesSearch =
        location.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        location.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        location.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());

      // Add type filtering logic using real location types
      const matchesType =
        filters.type.length === 0 ||
        filters.type.some(
          (typeId) =>
            location.type === typeId ||
            location.type ===
              locationTypes.find((lt) => lt.id === typeId)?.name,
        );

      // Updated status filter to check for active reservations
      const hasActiveReservations = location.reservations.some(
        (reservation) => {
          const today = new Date();
          const startDate = new Date(reservation.startDate);
          const endDate = new Date(reservation.endDate);
          return today >= startDate && today <= endDate;
        },
      );

      const matchesStatusFilter =
        filters.status.length === 0 ||
        (filters.status.includes("reserved") && hasActiveReservations) ||
        (filters.status.includes("not-reserved") && !hasActiveReservations);

      const matchesOwnershipFilter =
        filters.ownership.length === 0 ||
        (filters.ownership.includes("owned-by-us") &&
          location.areWeOwner === true) ||
        (filters.ownership.includes("fully-owned-by-us") &&
          location.ourPercentage === 100) ||
        (filters.ownership.includes("no-shares-for-us") &&
          location.areWeOwner === false &&
          location.ourPercentage === 0) ||
        (filters.ownership.includes("partially-shares-for-us") &&
          location.areWeOwner === false &&
          location.ourPercentage > 0);

      return (
        matchesSearch &&
        matchesType &&
        matchesStatusFilter &&
        matchesOwnershipFilter
      );
    });
  }, [locations, searchTerm, filters, locationTypes]);

  useEffect(() => {
    if (locationsData) {
      console.log("Locations Data", locationsData);
      setLocations(
        locationsData.locations.map((location) => {
          // Helper function to safely convert date strings
          const safeDate = (dateValue: any): Date | undefined => {
            if (!dateValue) return undefined;
            if (typeof dateValue === "string" && dateValue.trim() === "")
              return undefined;
            const date = new Date(dateValue);
            return isNaN(date.getTime()) ? undefined : date;
          };

          return {
            ...location,
            createdAt: new Date(location.createdAt),
            res_start_date: safeDate(location.res_start_date),
            res_end_date: safeDate(location.res_end_date),
            reservations: location.reservations.map((reservation) => ({
              ...reservation,
              startDate: new Date(reservation.startDate),
              endDate: new Date(reservation.endDate),
            })),
          };
        }),
      );
    }
  }, [locationsData]);

  // Calculate filter counts
  const filterCounts = useMemo(() => {
    const counts = {
      type: {} as Record<string, number>,
      status: {} as Record<string, number>,
      ownership: {
        "owned-by-us": 0,
        "no-shares-for-us": 0,
        "fully-owned-by-us": 0,
        "partially-shares-for-us": 0,
      },
    };

    locations.forEach((location) => {
      // Count by type - updated to handle both ID and name matching
      locationTypes.forEach((locationType) => {
        if (
          location.type === locationType.id ||
          location.type === locationType.name
        ) {
          counts.type[locationType.id] =
            (counts.type[locationType.id] || 0) + 1;
        }
      });

      // Count by reservation status
      const hasActiveReservations = location.reservations.some(
        (reservation) => {
          const today = new Date();
          const startDate = new Date(reservation.startDate);
          const endDate = new Date(reservation.endDate);
          return today >= startDate && today <= endDate;
        },
      );

      if (hasActiveReservations) {
        counts.status["reserved"] = (counts.status["reserved"] || 0) + 1;
      } else {
        counts.status["not-reserved"] =
          (counts.status["not-reserved"] || 0) + 1;
      }

      // Count by ownership - updated logic
      if (location.areWeOwner === true) {
        counts.ownership["owned-by-us"]++;
      }

      if (location.ourPercentage === 100) {
        counts.ownership["fully-owned-by-us"]++;
      }

      if (location.areWeOwner === false && location.ourPercentage === 0) {
        counts.ownership["no-shares-for-us"]++;
      }

      if (location.areWeOwner === false && location.ourPercentage > 0) {
        counts.ownership["partially-shares-for-us"]++;
      }
    });

    return counts;
  }, [locations, locationTypes]); // Added locationTypes to dependencies

  const toggleFilter = (filterType: keyof typeof filters, value: string) => {
    if (filterType === "capacity" || filterType === "revenue") return;

    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      type: [],
      status: [],
      ownership: [],
      capacity: { min: 0, max: 0 },
      revenue: { min: 0, max: 0 },
    });
  };

  const getAppliedFiltersCount = () => {
    return (
      filters.type.length +
      filters.status.length +
      filters.ownership.length +
      (filters.revenue.min > 0 || filters.revenue.max > 0 ? 1 : 0)
    );
  };

  if (isLoading) {
    return <div className="py-4 text-center">{t("loading")}</div>;
  }
  function getTypeColor(type: string) {
    switch (type) {
      case "office":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "retail":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "other":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  }

  // Helper function to check if location is currently reserved
  const isLocationReserved = (location: Location) => {
    if (!location.res_start_date || !location.res_end_date) {
      return false;
    }

    const today = new Date();
    const startDate = location.res_start_date;
    const endDate = location.res_end_date;

    const isReserved = today >= startDate && today <= endDate;

    return isReserved;
  };

  // Toggle date picker for specific location
  const toggleDatePicker = (locationId: string) => {
    setReservationStates((prev) => ({
      ...prev,
      [locationId]: {
        ...prev[locationId],
        showDatePicker: !prev[locationId]?.showDatePicker,
        startDate: prev[locationId]?.startDate || "",
        endDate: prev[locationId]?.endDate || "",
        isSubmitting: false,
      },
    }));
  };

  // Update date for specific location
  const updateDate = (
    locationId: string,
    field: "startDate" | "endDate",
    value: string,
  ) => {
    setReservationStates((prev) => ({
      ...prev,
      [locationId]: {
        ...prev[locationId],
        [field]: value,
        showDatePicker: true,
        isSubmitting: false,
      },
    }));
  };

  // Submit reservation
  const submitReservation = async (locationId: string) => {
    const state = reservationStates[locationId];
    if (!state?.startDate || !state?.endDate) return;

    setReservationStates((prev) => ({
      ...prev,
      [locationId]: {
        ...prev[locationId],
        isSubmitting: true,
      },
    }));

    try {
      // Convert dates to the format expected by Django with timezone (ISO format)
      const formatDate = (dateStr: string) => {
        const date = new Date(dateStr);
        // Set time to 08:00 and return ISO string (includes timezone)
        date.setHours(8, 0, 0, 0);
        return date.toISOString();
      };

      console.log("Submitting reservation with dates:", {
        startDate: formatDate(state.startDate),
        endDate: formatDate(state.endDate),
      });

      await locationServices.updateLocationReservationNotOurs(
        locationId,
        formatDate(state.startDate),
        formatDate(state.endDate),
      );

      // Reset state and hide date picker
      setReservationStates((prev) => ({
        ...prev,
        [locationId]: {
          showDatePicker: false,
          startDate: "",
          endDate: "",
          isSubmitting: false,
        },
      }));

      // Refresh the locations data to update the UI immediately
      await refetch();
    } catch (error) {
      console.error("Error updating reservation:", error);
      setReservationStates((prev) => ({
        ...prev,
        [locationId]: {
          ...prev[locationId],
          isSubmitting: false,
        },
      }));
    }
  };

  // Handle edit location
  const handleEditLocation = (locationId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const location = locations.find((loc) => loc.id === locationId);
    if (location && onEdit) {
      onEdit(location);
    }
  };

  // Handle delete location
  const handleDeleteLocation = (
    locationId: string,
    locationName: string,
    event: React.MouseEvent,
  ) => {
    event.stopPropagation();
    setDeleteModalState({
      isOpen: true,
      locationId,
      locationName,
      isDeleting: false,
      error: null,
    });
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!deleteModalState.locationId) return;

    setDeleteModalState((prev) => ({ ...prev, isDeleting: true, error: null }));

    try {
      await locationServices.deleteLocation(deleteModalState.locationId);
      // Close modal and refresh data
      setDeleteModalState({
        isOpen: false,
        locationId: null,
        locationName: "",
        isDeleting: false,
        error: null,
      });
      await refetch();
    } catch (error: any) {
      console.error("Error deleting location:", error);
      const errorMessage =
        error.response?.data?.details ||
        error.response?.data?.error ||
        error.response?.data?.warning ||
        error.response?.data?.message ||
        "Failed to delete location. Please try again.";
      setDeleteModalState((prev) => ({
        ...prev,
        error: errorMessage,
        isDeleting: false,
      }));
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="space-y-4 p-4 sm:space-y-6 sm:p-6">
        {/* Header with Search and Filter Toggle - Enhanced */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
          <CardTitle className="text-lg font-bold text-gray-900 dark:text-white sm:text-xl lg:text-2xl">
            {t("locations")}{" "}
            {filteredLocations.length !== locations.length && (
              <span className="text-xs font-normal text-gray-500 sm:text-sm">
                ({filteredLocations.length} {t("of")} {locations.length})
              </span>
            )}
          </CardTitle>

          <div className="flex w-full gap-2 sm:w-auto">
            <div className="relative flex-1 sm:w-56 lg:w-64">
              <Search
                className={`absolute top-1/2 -translate-y-1/2 transform text-gray-400
                  ${isRTL ? "left-3" : "right-3"}`}
                size={16}
              />
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t("searchLocations")}
                className={`${isRTL ? "pl-9 pr-3" : "pl-3 pr-9"} h-9 border-gray-200 bg-gray-50 text-sm focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 sm:h-10 sm:text-base`}
              />
            </div>

            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className={`relative flex items-center gap-1.5 rounded-lg border px-3 py-2 text-sm transition-colors sm:gap-2 sm:px-4 sm:text-base
                ${
                  isFilterOpen
                    ? "border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                    : "border-gray-200 text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-800"
                }`}
            >
              <Sliders
                size={14}
                className={`sm:size-4 ${isFilterOpen ? "text-blue-500" : ""}`}
              />
              <span className="hidden sm:inline">{t("filters")}</span>
              <span className="sm:hidden">{t("Filter")}</span>
              {getAppliedFiltersCount() > 0 && (
                <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-blue-500 text-xs text-white sm:-right-2 sm:-top-2 sm:h-5 sm:w-5">
                  {getAppliedFiltersCount()}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Active Filters Display - Enhanced */}
        {getAppliedFiltersCount() > 0 && (
          <div className="flex flex-wrap gap-1.5 rounded-lg bg-gray-50 p-2.5 dark:bg-gray-800/50 sm:gap-2 sm:p-3">
            <span className="my-1 mr-1 text-xs text-gray-500 dark:text-gray-400 sm:mr-2 sm:text-sm">
              {t("activeFilters")}:
            </span>

            {filters.type.map((type) => {
              const typeName =
                locationTypes.find((lt) => lt.id === type)?.name || type;
              return (
                <Badge
                  key={type}
                  variant="secondary"
                  className={`${getTypeColor(type as Location["type"])} flex items-center gap-1 px-2 py-1 text-xs sm:px-3 sm:py-1.5 sm:text-sm`}
                >
                  <Building size={10} className="mr-0.5 sm:mr-1 sm:size-3" />
                  {t(typeName)}
                  <X
                    size={12}
                    className="ml-0.5 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 sm:ml-1 sm:size-3.5"
                    onClick={() => toggleFilter("type", type)}
                  />
                </Badge>
              );
            })}

            {filters.status.map((status) => (
              <Badge
                key={status}
                variant="secondary"
                className={`${
                  status === "active"
                    ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                } flex items-center gap-1 px-3 py-1.5`}
              >
                {t(status)}
                <X
                  size={14}
                  className="ml-1 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                  onClick={() => toggleFilter("status", status)}
                />
              </Badge>
            ))}

            {filters.ownership.map((owner) => (
              <Badge
                key={owner}
                variant="secondary"
                className="flex items-center gap-1 bg-blue-100 px-3 py-1.5 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
              >
                <Percent size={12} className="mr-1" />
                {t(owner)}
                <X
                  size={14}
                  className="ml-1 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                  onClick={() => toggleFilter("ownership", owner)}
                />
              </Badge>
            ))}

            {(filters.capacity.min > 0 || filters.capacity.max > 0) && (
              <Badge
                variant="secondary"
                className="flex items-center gap-1 bg-purple-100 px-3 py-1.5 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
              >
                {t("capacity")}: {filters.capacity.min}
                {filters.capacity.max > 0 ? ` - ${filters.capacity.max}` : "+"}
                <X
                  size={14}
                  className="ml-1 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                  onClick={() =>
                    setFilters((prev) => ({
                      ...prev,
                      capacity: { min: 0, max: 0 },
                    }))
                  }
                />
              </Badge>
            )}

            {(filters.revenue.min > 0 || filters.revenue.max > 0) && (
              <Badge
                variant="secondary"
                className="flex items-center gap-1 bg-emerald-100 px-3 py-1.5 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400"
              >
                {t("revenue")}: {filters.revenue.min.toLocaleString()}
                {filters.revenue.max > 0
                  ? ` - ${filters.revenue.max.toLocaleString()}`
                  : "+"}
                <X
                  size={14}
                  className="ml-1 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                  onClick={() =>
                    setFilters((prev) => ({
                      ...prev,
                      revenue: { min: 0, max: 0 },
                    }))
                  }
                />
              </Badge>
            )}
          </div>
        )}

        {/* Expandable Filter Panel - Enhanced & Tabbed */}
        {isFilterOpen && (
          <div className="mt-3 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:mt-4">
            {/* Filter Tabs */}
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setActiveFilterTab("type")}
                className={`flex-1 px-2 py-2.5 text-xs font-medium sm:flex-none sm:px-4 sm:py-3 sm:text-sm ${
                  activeFilterTab === "type"
                    ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
                }`}
              >
                <Building
                  size={14}
                  className="mr-1 inline-block sm:mr-2 sm:size-4"
                />
                <span className="hidden sm:inline">{t("locationType")}</span>
                <span className="sm:hidden">{t("Type")}</span>
                {filters.type.length > 0 && (
                  <span className="ml-1 rounded-full bg-blue-100 px-1.5 py-0.5 text-xs text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 sm:ml-2 sm:px-2">
                    {filters.type.length}
                  </span>
                )}
              </button>

              <button
                onClick={() => setActiveFilterTab("status")}
                className={`flex-1 px-2 py-2.5 text-xs font-medium sm:flex-none sm:px-4 sm:py-3 sm:text-sm ${
                  activeFilterTab === "status"
                    ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
                }`}
              >
                <Filter
                  size={14}
                  className="mr-1 inline-block sm:mr-2 sm:size-4"
                />
                <span className="hidden sm:inline">{t("status")}</span>
                <span className="sm:hidden">{t("Status")}</span>
                {filters.status.length > 0 && (
                  <span className="ml-1 rounded-full bg-blue-100 px-1.5 py-0.5 text-xs text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 sm:ml-2 sm:px-2">
                    {filters.status.length}
                  </span>
                )}
              </button>

              <button
                onClick={() => setActiveFilterTab("ownership")}
                className={`flex-1 px-2 py-2.5 text-xs font-medium sm:flex-none sm:px-4 sm:py-3 sm:text-sm ${
                  activeFilterTab === "ownership"
                    ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400"
                    : "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
                }`}
              >
                <Percent
                  size={14}
                  className="mr-1 inline-block sm:mr-2 sm:size-4"
                />
                <span className="hidden sm:inline">{t("ownership")}</span>
                <span className="sm:hidden">{t("Own")}</span>
                {filters.ownership.length > 0 && (
                  <span className="ml-1 rounded-full bg-blue-100 px-1.5 py-0.5 text-xs text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 sm:ml-2 sm:px-2">
                    {filters.ownership.length}
                  </span>
                )}
              </button>
            </div>

            {/* Filter Content */}
            <div className="p-3 sm:p-4">
              {activeFilterTab === "type" && (
                <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                  {locationTypes.map((type) => (
                    <label
                      key={type.id}
                      className={`
                      flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors
                      ${
                        filters.type.includes(type.id)
                          ? "border border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20"
                          : "border border-gray-200 bg-gray-50 hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                      }
                    `}
                    >
                      <input
                        type="checkbox"
                        checked={filters.type.includes(type.id)}
                        onChange={() => toggleFilter("type", type.id)}
                        className="form-checkbox h-4 w-4 rounded border-gray-300 text-blue-600 dark:border-gray-600"
                      />
                      <div>
                        <span
                          className={`text-sm font-medium ${
                            filters.type.includes(type.id)
                              ? "text-blue-700 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          }`}
                        >
                          {t(type.name)}
                        </span>
                        <span className="block text-xs text-gray-500 dark:text-gray-400">
                          {filterCounts.type[type.id] || 0} {t("locations")}
                        </span>
                      </div>
                    </label>
                  ))}
                </div>
              )}

              {activeFilterTab === "status" && (
                <div className="grid grid-cols-2 gap-2 sm:grid-cols-4">
                  {statusTypes.map((status) => (
                    <label
                      key={status}
                      className={`
                      flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors
                      ${
                        filters.status.includes(status)
                          ? status === "active"
                            ? "border border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20"
                            : "border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20"
                          : "border border-gray-200 bg-gray-50 hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                      }
                    `}
                    >
                      <input
                        type="checkbox"
                        checked={filters.status.includes(status)}
                        onChange={() => toggleFilter("status", status)}
                        className={`form-checkbox h-4 w-4 rounded border-gray-300 dark:border-gray-600 ${
                          status === "active"
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      />
                      <div>
                        <span
                          className={`text-sm font-medium ${
                            filters.status.includes(status)
                              ? status === "active"
                                ? "text-green-700 dark:text-green-400"
                                : "text-red-700 dark:text-red-400"
                              : "text-gray-700 dark:text-gray-300"
                          }`}
                        >
                          {t(status)}
                        </span>
                        <span className="block text-xs text-gray-500 dark:text-gray-400">
                          {filterCounts.status[status] || 0} {t("locations")}
                        </span>
                      </div>
                    </label>
                  ))}
                </div>
              )}

              {activeFilterTab === "ownership" && (
                <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                  <label
                    className={`
                    flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors
                    ${
                      filters.ownership.includes("owned-by-us")
                        ? "border border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20"
                        : "border border-gray-200 bg-gray-50 hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                    }
                  `}
                  >
                    <input
                      type="checkbox"
                      checked={filters.ownership.includes("owned-by-us")}
                      onChange={() => toggleFilter("ownership", "owned-by-us")}
                      className="form-checkbox h-4 w-4 rounded border-gray-300 text-blue-600 dark:border-gray-600"
                    />
                    <div>
                      <span
                        className={`text-sm font-medium ${
                          filters.ownership.includes("owned-by-us")
                            ? "text-blue-700 dark:text-blue-400"
                            : "text-gray-700 dark:text-gray-300"
                        }`}
                      >
                        {t("OwnedByUs")}
                      </span>
                      <span className="block text-xs text-gray-500 dark:text-gray-400">
                        {filterCounts.ownership["owned-by-us"]} {t("locations")}
                      </span>
                    </div>
                  </label>

                  <label
                    className={`
                    flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors
                    ${
                      filters.ownership.includes("no-shares-for-us")
                        ? "border border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20"
                        : "border border-gray-200 bg-gray-50 hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                    }
                  `}
                  >
                    <input
                      type="checkbox"
                      checked={filters.ownership.includes("no-shares-for-us")}
                      onChange={() =>
                        toggleFilter("ownership", "no-shares-for-us")
                      }
                      className="form-checkbox h-4 w-4 rounded border-gray-300 text-blue-600 dark:border-gray-600"
                    />
                    <div>
                      <span
                        className={`text-sm font-medium ${
                          filters.ownership.includes("no-shares-for-us")
                            ? "text-blue-700 dark:text-blue-400"
                            : "text-gray-700 dark:text-gray-300"
                        }`}
                      >
                        {t("WeDontHaveShares")}
                      </span>
                      <span className="block text-xs text-gray-500 dark:text-gray-400">
                        {filterCounts.ownership["no-shares-for-us"]}{" "}
                        {t("locations")}
                      </span>
                    </div>
                  </label>

                  <label
                    className={`
                    flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors
                    ${
                      filters.ownership.includes("fully-owned-by-us")
                        ? "border border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20"
                        : "border border-gray-200 bg-gray-50 hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                    }
                  `}
                  >
                    <input
                      type="checkbox"
                      checked={filters.ownership.includes("fully-owned-by-us")}
                      onChange={() =>
                        toggleFilter("ownership", "fully-owned-by-us")
                      }
                      className="form-checkbox h-4 w-4 rounded border-gray-300 text-blue-600 dark:border-gray-600"
                    />
                    <div>
                      <span
                        className={`text-sm font-medium ${
                          filters.ownership.includes("fully-owned-by-us")
                            ? "text-blue-700 dark:text-blue-400"
                            : "text-gray-700 dark:text-gray-300"
                        }`}
                      >
                        {t("fullyOwnedByUS")}
                      </span>
                      <span className="block text-xs text-gray-500 dark:text-gray-400">
                        {filterCounts.ownership["fully-owned-by-us"]}{" "}
                        {t("locations")}
                      </span>
                    </div>
                  </label>

                  <label
                    className={`
                    flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors
                    ${
                      filters.ownership.includes("partially-shares-for-us")
                        ? "border border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20"
                        : "border border-gray-200 bg-gray-50 hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                    }
                  `}
                  >
                    <input
                      type="checkbox"
                      checked={filters.ownership.includes(
                        "partially-shares-for-us",
                      )}
                      onChange={() =>
                        toggleFilter("ownership", "partially-shares-for-us")
                      }
                      className="form-checkbox h-4 w-4 rounded border-gray-300 text-blue-600 dark:border-gray-600"
                    />
                    <div>
                      <span
                        className={`text-sm font-medium ${
                          filters.ownership.includes("partially-shares-for-us")
                            ? "text-blue-700 dark:text-blue-400"
                            : "text-gray-700 dark:text-gray-300"
                        }`}
                      >
                        {t("partiallySharesForUs")}
                      </span>
                      <span className="block text-xs text-gray-500 dark:text-gray-400">
                        {filterCounts.ownership["partially-shares-for-us"]}{" "}
                        {t("locations")}
                      </span>
                    </div>
                  </label>
                </div>
              )}

              {/* Filter Actions */}
              <div className="mt-4 flex justify-between border-t border-gray-200 pt-4 dark:border-gray-700">
                <button
                  onClick={clearFilters}
                  className="rounded-lg px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:text-red-300"
                >
                  {t("clearAllFilters")}
                </button>
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-3 sm:p-4 lg:p-6">
        <div className="grid gap-3 sm:gap-4">
          {filteredLocations.length === 0 ? (
            <div className="py-8 text-center sm:py-12">
              <MapPin
                size={40}
                className="mx-auto mb-4 text-gray-400 sm:size-12"
              />
              <h3 className="mb-2 text-lg font-medium text-gray-700 dark:text-gray-300 sm:text-xl">
                {t("noLocationsFound")}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 sm:text-base">
                {t("Try adjusting your search or filters")}
              </p>
            </div>
          ) : (
            filteredLocations.map((location) => {
              // Calculate used capacity for this location - Filter out expired reservations
              const activeReservations = location.reservations.filter((r) => {
                // Check if reservation is active or pending
                const isActiveStatus =
                  r.status === "active" || r.status === "pending";

                // Check if the end date is in the future
                const endDate = new Date(r.endDate);
                const isNotExpired = endDate >= new Date();

                // Only include reservations that are both active/pending AND not expired
                return isActiveStatus && isNotExpired;
              });

              const usedCapacity = activeReservations.reduce(
                (sum, res) => sum + res.capacity,
                0,
              );
              const capacityPercentage =
                (usedCapacity / location.capacity) * 100;

              const isReserved = isLocationReserved(location);
              const locationReservationState = reservationStates[location.id];

              return (
                <div
                  key={location.id}
                  onClick={
                    location.weHaveShares ? () => onSelect(location) : undefined
                  }
                  className={`rounded-lg p-3 transition-all sm:p-4 ${
                    location.weHaveShares
                      ? "cursor-pointer border border-gray-200 bg-white hover:border-blue-200 hover:shadow-md dark:border-gray-700 dark:bg-gray-800 dark:hover:border-blue-800"
                      : "border border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-700/50"
                  }`}
                >
                  <div className="flex flex-col gap-3 sm:gap-4">
                    {/* Header Section */}
                    <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
                      <div className="min-w-0 flex-1">
                        <div className="flex items-start gap-2 sm:gap-3">
                          <MapPin className="mt-1 h-4 w-4 flex-shrink-0 text-gray-400 sm:h-5 sm:w-5" />
                          <div className="min-w-0 flex-1">
                            <h3 className="truncate text-sm font-medium text-gray-900 dark:text-gray-100 sm:text-base">
                              {location.name}
                            </h3>
                            {location.areWeOwner && (
                              <span className="mt-1 inline-block rounded bg-blue-100 px-2 py-1 text-xs font-semibold text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                ⭐ {t("ourLocation")}
                              </span>
                            )}
                            {!location.weHaveShares && (
                              <span className="mt-1 inline-flex items-center gap-1 rounded bg-red-100 px-2 py-1 text-xs font-semibold text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                <X size={12} className="inline-block" />
                                {t("weDontHaveShares")}
                              </span>
                            )}
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                              {location.address}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-wrap items-center gap-1.5 sm:gap-2">
                        <Badge
                          variant="secondary"
                          className={`${getTypeColor(location.type)} text-xs sm:text-sm`}
                        >
                          {t(location.type)}
                        </Badge>
                        <Badge
                          variant="secondary"
                          className={`text-xs sm:text-sm ${
                            location.status === "active"
                              ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                              : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                          }`}
                        >
                          {t(location.status)}
                        </Badge>

                        {/* Reservation Status Badge for non-owned locations */}
                        {!location.areWeOwner && (
                          <Badge
                            variant="destructive"
                            className={
                              isReserved
                                ? "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
                            }
                          >
                            {isReserved ? t("reserved") : t("available")}
                          </Badge>
                        )}

                        {/* Edit and Delete buttons for non-owned locations */}
                        {!location.weHaveShares && (
                          <div
                            className="ml-1 flex items-center gap-1 sm:ml-2"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) =>
                                handleEditLocation(location.id, e)
                              }
                              className="h-7 w-7 border-blue-200 bg-blue-50 p-0 text-blue-700 hover:bg-blue-100 hover:text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30 sm:h-8 sm:w-8"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={(e) =>
                                handleDeleteLocation(
                                  location.id,
                                  location.name,
                                  e,
                                )
                              }
                              className="h-7 w-7 bg-red-600 p-0 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 sm:h-8 sm:w-8"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Reservation Management for non-owned locations - Allow for all non-owned locations */}
                    {!location.weHaveShares && (
                      <div
                        className="border-t border-gray-200 pt-4 dark:border-gray-700"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-blue-500" />
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {t("reserveLocation")}
                            </span>
                          </div>
                          <button
                            onClick={() => toggleDatePicker(location.id)}
                            className="rounded bg-blue-500 px-3 py-1.5 text-xs text-white hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
                          >
                            {locationReservationState?.showDatePicker
                              ? t("cancel")
                              : t("reserve")}
                          </button>
                        </div>

                        {/* Date Picker */}
                        {locationReservationState?.showDatePicker && (
                          <div className="mt-3 rounded-lg bg-gray-50 p-3 dark:bg-gray-800/50">
                            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                              <div>
                                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                  {t("startDate")}
                                </label>

                                <input
                                  type="date"
                                  value={locationReservationState.startDate}
                                  onChange={(e) =>
                                    updateDate(
                                      location.id,
                                      "startDate",
                                      e.target.value,
                                    )
                                  }
                                  className="mt-1 w-full rounded border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                  min={new Date().toISOString().split("T")[0]}
                                />
                              </div>
                              <div>
                                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                                  {t("endDate")}
                                </label>
                                <input
                                  type="date"
                                  value={locationReservationState.endDate}
                                  onChange={(e) =>
                                    updateDate(
                                      location.id,
                                      "endDate",
                                      e.target.value,
                                    )
                                  }
                                  className="mt-1 w-full rounded border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                  min={
                                    locationReservationState.startDate ||
                                    new Date().toISOString().split("T")[0]
                                  }
                                />
                              </div>
                            </div>

                            {locationReservationState.startDate &&
                              locationReservationState.endDate && (
                                <div className="mt-3 flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <AlertCircle className="h-4 w-4 text-amber-500" />
                                    <span className="text-xs text-gray-600 dark:text-gray-400">
                                      {t("confirmReservationDates")}
                                    </span>
                                  </div>
                                  <button
                                    onClick={() =>
                                      submitReservation(location.id)
                                    }
                                    disabled={
                                      locationReservationState.isSubmitting
                                    }
                                    className="flex items-center gap-1 rounded bg-green-500 px-3 py-1.5 text-xs text-white hover:bg-green-600 disabled:opacity-50 dark:bg-green-600 dark:hover:bg-green-700"
                                  >
                                    {locationReservationState.isSubmitting ? (
                                      <>
                                        <div className="h-3 w-3 animate-spin rounded-full border border-white border-t-transparent"></div>
                                        {t("submitting")}
                                      </>
                                    ) : (
                                      <>
                                        <Check className="h-3 w-3" />
                                        {t("confirm")}
                                      </>
                                    )}
                                  </button>
                                </div>
                              )}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Details Grid - Conditional layout based on weHaveShares */}
                    <div
                      className={`mt-2 grid grid-cols-1 gap-4 ${
                        location.weHaveShares
                          ? "sm:grid-cols-2 lg:grid-cols-3"
                          : "sm:grid-cols-3"
                      }`}
                    >
                      {/* Ownership Info */}
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t("ownership")}
                        </p>
                        <div className="space-y-1">
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {t("ownedBy")}:{" "}
                            <span className="font-medium">
                              {/* Display the name from primaryOwner object if available, fallback to ownedBy */}
                              {location.primaryOwner?.name || location.ownedBy}
                              {location.primaryOwner?.percentage &&
                                ` (${language === "ar" ? "%" : ""}${Number(
                                  location.primaryOwner.percentage,
                                ).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                  {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0,
                                  },
                                )}${language === "ar" ? "" : "%"})`}
                            </span>
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {t("ourPercentage")}:{" "}
                            <span className="font-medium">
                              {language === "ar" ? "%" : ""}
                              {Number(location.ourPercentage).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                                {
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0,
                                },
                              )}
                              {language === "ar" ? "" : "%"}
                            </span>
                          </p>

                          {/* Shared With Information - Only show if we have shares */}
                          {location.weHaveShares &&
                            location.sharedWith.length > 0 && (
                              <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                  {t("sharedWith")}:
                                </p>
                                <div className="mt-1 space-y-1">
                                  {location.ownershipShares
                                    ? location.ownershipShares.map(
                                        (share, idx) => (
                                          <p
                                            key={idx}
                                            className="pl-2 text-sm text-gray-600 dark:text-gray-400"
                                          >
                                            • {share.name}{" "}
                                            <span className="font-medium">
                                              ({language === "ar" ? "%" : ""}
                                              {Number(
                                                share.percentage,
                                              ).toLocaleString(
                                                language === "ar"
                                                  ? "ar-EG"
                                                  : "en-US",
                                                {
                                                  minimumFractionDigits: 0,
                                                  maximumFractionDigits: 0,
                                                },
                                              )}
                                              {language === "ar" ? "" : "%"})
                                            </span>
                                          </p>
                                        ),
                                      )
                                    : location.sharedWith.map(
                                        (partner, idx) => (
                                          <p
                                            key={idx}
                                            className="pl-2 text-sm text-gray-600 dark:text-gray-400"
                                          >
                                            • {partner}
                                          </p>
                                        ),
                                      )}
                                </div>
                              </div>
                            )}
                        </div>
                      </div>

                      {/* Reservation Info - Only show if we have shares */}
                      {location.weHaveShares && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {t("reservation")}
                          </p>
                          {activeReservations.length > 0 ? (
                            <div className="space-y-2">
                              {activeReservations.map((res, idx) => (
                                <div
                                  key={res.id}
                                  className={`${idx > 0 ? "mt-2 border-t border-gray-200 pt-2 dark:border-gray-700" : ""}`}
                                >
                                  <p className="text-sm text-gray-600 dark:text-gray-400">
                                    <span className="font-medium">
                                      {res.clientName}
                                    </span>{" "}
                                    -{" "}
                                    <span>
                                      {language === "ar" ? "" : ""}
                                      {Number(res.capacity).toLocaleString(
                                        language === "ar" ? "ar-EG" : "en-US",
                                        {
                                          minimumFractionDigits: 0,
                                          maximumFractionDigits: 0,
                                        },
                                      )}{" "}
                                      {t("units")}
                                    </span>
                                  </p>
                                  <p className="text-xs text-gray-500 dark:text-gray-500">
                                    {new Date(res.startDate).toLocaleDateString(
                                      language === "ar" ? "ar-EG" : "en-US",
                                    )}{" "}
                                    -{" "}
                                    {new Date(res.endDate).toLocaleDateString(
                                      language === "ar" ? "ar-EG" : "en-US",
                                    )}
                                  </p>
                                </div>
                              ))}
                              {activeReservations.length > 1 && (
                                <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                                  {t("totalActiveReservations")}:{" "}
                                  {activeReservations.length}
                                </p>
                              )}
                            </div>
                          ) : (
                            <p className="text-sm italic text-gray-500 dark:text-gray-500">
                              {t("noActiveReservations")}
                            </p>
                          )}
                        </div>
                      )}

                      {/* Reservation Dates - Only show if we don't have shares and location is reserved */}
                      {!location.weHaveShares && isReserved && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {t("reservationPeriod")}
                          </p>
                          <div className="space-y-1">
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {t("startDate")}:{" "}
                              <span className="font-medium">
                                {location.res_start_date
                                  ? new Date(
                                      location.res_start_date,
                                    ).toLocaleDateString()
                                  : t("notSet")}
                              </span>
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {t("endDate")}:{" "}
                              <span className="font-medium">
                                {location.res_end_date
                                  ? new Date(
                                      location.res_end_date,
                                    ).toLocaleDateString()
                                  : t("notSet")}
                              </span>
                            </p>
                            {location.res_start_date &&
                              location.res_end_date && (
                                <p className="text-xs text-blue-600 dark:text-blue-400">
                                  {Math.ceil(
                                    (new Date(location.res_end_date).getTime() -
                                      new Date(
                                        location.res_start_date,
                                      ).getTime()) /
                                      (1000 * 60 * 60 * 24),
                                  )}{" "}
                                  {t("days")}
                                </p>
                              )}
                          </div>
                        </div>
                      )}

                      {/* Creation Info */}
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t("created")}
                        </p>
                        <div className="space-y-1">
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {t("by")}:{" "}
                            <span className="font-medium">
                              {location.createdBy}
                            </span>
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {new Date(location.createdAt).toLocaleDateString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}
                          </p>
                        </div>
                      </div>

                      {/* Capacity Info - Always show */}
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t("capacity")}
                        </p>
                        <div>
                          <div className="flex items-center justify-between">
                            <p className="text-xl font-medium text-gray-900 dark:text-gray-100">
                              {language === "ar"
                                ? `${Number(location.capacity).toLocaleString("ar-EG")}${location.weHaveShares ? `/${Number(usedCapacity).toLocaleString("ar-EG")}` : ""}`
                                : `${location.weHaveShares ? `${Number(usedCapacity).toLocaleString("en-US")}/` : ""}${Number(location.capacity).toLocaleString("en-US")}`}
                            </p>
                            {location.weHaveShares && (
                              <Badge
                                className={`${
                                  capacityPercentage > 80
                                    ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                                    : capacityPercentage > 50
                                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                                      : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                                }`}
                              >
                                {language === "ar" ? "%" : ""}
                                {capacityPercentage.toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                  {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0,
                                  },
                                )}
                                {language === "ar" ? "" : "%"}
                              </Badge>
                            )}
                          </div>
                          {location.weHaveShares && (
                            <div className="mt-2 h-2 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                              <div
                                className={`h-full rounded-full ${
                                  capacityPercentage > 80
                                    ? "bg-red-500"
                                    : capacityPercentage > 50
                                      ? "bg-yellow-500"
                                      : "bg-green-500"
                                }`}
                                style={{ width: `${capacityPercentage}%` }}
                              ></div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </CardContent>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalState.isOpen}
        onClose={() => {
          setDeleteModalState({
            isOpen: false,
            locationId: null,
            locationName: "",
            isDeleting: false,
            error: null,
          });
        }}
        onConfirm={confirmDelete}
        message={`${t("deleteLocationWarning")} ${t("deleteLocationConditions")}`}
        title={t("deleteLocation")}
        itemName={deleteModalState.locationName}
        isLoading={deleteModalState.isDeleting}
        error={deleteModalState.error}
      />
    </Card>
  );
};

export default LocationList;
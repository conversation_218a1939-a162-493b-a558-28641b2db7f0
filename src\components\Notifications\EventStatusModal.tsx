import React, { useState } from "react";
import useLanguage from "@/hooks/useLanguage";
import IncomeServices from "@/lib/income";
import ExpenseServices from "@/lib/expenses";
import { toast } from "react-hot-toast";

interface EventStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  category: string;
  category_id: string;
  currentStatus?: string;
  eventTitle?: string;
}

const EventStatusModal: React.FC<EventStatusModalProps> = ({
  isOpen,
  onClose,
  category,
  category_id,
  currentStatus = "pending",
  eventTitle = "",
}) => {
  const { t, language } = useLanguage();
  const isRTL = language === "ar";
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);
  const [paidDate, setPaidDate] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const statusOptions = [
    {
      value: "pending",
      label: t("pending"),
      color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    },
    {
      value: "completed",
      label: t("completed"),
      color: "bg-green-100 text-green-800 border-green-200",
    },
    {
      value: "overdue",
      label: t("overdue"),
      color: "bg-red-100 text-red-800 border-red-200",
    },
    {
      value: "cancelled",
      label: t("cancelled"),
      color: "bg-gray-100 text-gray-800 border-gray-200",
    },
    {
      value: "upcoming",
      label: t("upcoming"),
      color: "bg-blue-100 text-blue-800 border-blue-200",
    },
  ];

  const handleUpdateStatus = async () => {
    if (selectedStatus === currentStatus) {
      onClose();
      return;
    }

    // Validate paid_date if status is completed
    if (selectedStatus === "completed" && !paidDate) {
      toast.error(t("pleaseSelectPaidDate"));
      return;
    }

    setIsLoading(true);
    try {
      const requestBody: any = {
        status: selectedStatus,
      };

      // Add paid_date to request body if status is completed
      if (selectedStatus === "completed" && paidDate) {
        requestBody.paid_date = paidDate;
      }

      if (category === "income_due_date") {
        await IncomeServices().updateIncomeStatus(category_id, requestBody);
        toast.success(t("incomeStatusUpdated"));
      } else if (category === "expense_due_date") {
        await ExpenseServices().updateExpenseStatus(category_id, requestBody);
        toast.success(t("expenseStatusUpdated"));
      }
      onClose();
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error(t("errorUpdatingStatus"));
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div
        className={`relative mx-4 w-full max-w-md rounded-lg bg-white shadow-xl dark:bg-boxdark ${
          isRTL ? "rtl" : "ltr"
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 p-6 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {category === "income_due_date"
              ? t("updateIncomeStatus")
              : t("updateExpenseStatus")}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg
              className="h-6 w-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {eventTitle && (
            <div className="mb-4">
              <p className="mb-2 text-sm text-gray-600 dark:text-gray-400">
                {t("event")}:
              </p>
              <p className="font-medium text-gray-900 dark:text-white">
                {eventTitle}
              </p>
            </div>
          )}

          <div className="mb-6">
            <label className="mb-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("selectStatus")}
            </label>
            <div className="space-y-2">
              {statusOptions.map((status) => (
                <label
                  key={status.value}
                  className={`flex cursor-pointer items-center rounded-lg border p-3 transition-all hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    selectedStatus === status.value
                      ? "border-primary bg-primary/5"
                      : "border-gray-200 dark:border-gray-600"
                  } ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <input
                    type="radio"
                    name="status"
                    value={status.value}
                    checked={selectedStatus === status.value}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className={`h-4 w-4 text-primary focus:ring-primary ${
                      isRTL ? "ml-3" : "mr-3"
                    }`}
                  />
                  <div className="flex items-center">
                    <span
                      className={`inline-block rounded-full border px-2 py-1 text-xs font-medium ${status.color} ${
                        isRTL ? "ml-2" : "mr-2"
                      }`}
                    >
                      {status.label}
                    </span>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {status.label}
                    </span>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Paid Date Field - Only show when completed is selected */}
          {selectedStatus === "completed" && (
            <div className="mb-6">
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("paidDate")} <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                value={paidDate}
                onChange={(e) => setPaidDate(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-primary"
                required
              />
            </div>
          )}
        </div>

        {/* Footer */}
        <div
          className={`flex gap-3 border-t border-gray-200 p-6 dark:border-gray-700 ${
            isRTL ? "flex-row-reverse" : ""
          }`}
        >
          <button
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 rounded-md border border-gray-300 bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            {t("cancel")}
          </button>
          <button
            onClick={handleUpdateStatus}
            disabled={isLoading || selectedStatus === currentStatus}
            className="flex-1 rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                {t("updating")}
              </div>
            ) : (
              t("updateStatus")
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EventStatusModal;

import React, { useState, useMemo } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";
import AnalyticsSection from "./Sections/AnalyticsSection";
import IncomeVsExpenseSection from "./Sections/IncomeVsExpenseSection";
import AdvancedAnalyticsSection from "./Sections/AdvancedAnalyticsSection";
import IncomeSummary from "../Financials/Income/IncomeSummary";
import ExpenseSummary from "../Financials/Expenses/ExpenseSummary";
import {
  SafeAnalyticsWrapper,
  AnalyticsEmptyState,
  AnalyticsLoading,
  useAnalyticsErrorHandler,
} from "../common/AnalyticsErrorBoundary";
import { filterValidEvents } from "@/utils/analyticsUtils";
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Target,
  Settings,
} from "lucide-react";

interface ImprovedDashboardProps {
  events: EventDetails[];
  isLoading?: boolean;
}

const ImprovedDashboard: React.FC<ImprovedDashboardProps> = ({
  events,
  isLoading = false,
}) => {
  const { t, language } = useLanguage();
  const [activeTab, setActiveTab] = useState<
    "overview" | "income" | "expenses" | "advanced"
  >("overview");
  const [dateRange, setDateRange] = useState<
    "1m" | "3m" | "6m" | "12m" | "all"
  >("12m");
  const { error, clearError } = useAnalyticsErrorHandler();

  // Process and filter events
  const { validEvents, incomeEvents, expenseEvents, filteredEvents } =
    useMemo(() => {
      const valid = filterValidEvents(events);
      const income = valid.filter((event) => event.category === "income");
      const expense = valid.filter((event) => event.category === "expense");

      // Apply date filtering
      let filtered = valid;
      if (dateRange !== "all") {
        const months = parseInt(dateRange.replace("m", ""));
        const cutoffDate = new Date();
        cutoffDate.setMonth(cutoffDate.getMonth() - months);
        filtered = valid.filter(
          (event) => new Date(event.dueDate) >= cutoffDate,
        );
      }

      return {
        validEvents: valid,
        incomeEvents: income,
        expenseEvents: expense,
        filteredEvents: filtered,
      };
    }, [events, dateRange]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const totalIncome = incomeEvents.reduce(
      (sum, event) => sum + Number(event.amount),
      0,
    );
    const totalExpense = expenseEvents.reduce(
      (sum, event) => sum + Number(event.amount),
      0,
    );
    const netProfit = totalIncome - totalExpense;
    const transactionCount = validEvents.length;

    return {
      totalIncome,
      totalExpense,
      netProfit,
      transactionCount,
      profitMargin: totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0,
    };
  }, [incomeEvents, expenseEvents, validEvents]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <AnalyticsLoading />
        <AnalyticsLoading />
      </div>
    );
  }

  if (error) {
    return (
      <AnalyticsEmptyState
        title={t("Analytics Error")}
        description={t(
          "There was an error loading the analytics. Please try refreshing the page.",
        )}
        icon={<BarChart3 className="mx-auto mb-4 h-16 w-16 text-red-400" />}
        action={
          <button
            onClick={clearError}
            className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
          >
            {t("Try Again")}
          </button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Summary Cards */}
      <div className="rounded-lg border bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="p-6">
          <div className="mb-6 flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <h1 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white lg:mb-0">
              {t("Financial Dashboard")}
            </h1>

            {/* Date Range Filter */}
            <div className="flex rounded-lg bg-gray-100 p-1 dark:bg-gray-700">
              {[
                { key: "1m", label: t("1M") },
                { key: "3m", label: t("3M") },
                { key: "6m", label: t("6M") },
                { key: "12m", label: t("12M") },
                { key: "all", label: t("All") },
              ].map((option) => (
                <button
                  key={option.key}
                  onClick={() => setDateRange(option.key as any)}
                  className={`rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                    dateRange === option.key
                      ? "bg-white text-gray-900 shadow-sm dark:bg-gray-600 dark:text-white"
                      : "text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="overflow-hidden rounded-lg bg-gradient-to-r from-green-50 to-green-100 p-6 dark:from-green-900/20 dark:to-green-800/20">
              <div className="flex min-h-[100px] items-start justify-between">
                <div className="min-w-0 flex-1 pr-4">
                  <p className="mb-2 text-sm text-green-600 dark:text-green-400">
                    {t("Total Income")}
                  </p>
                  <p className="truncate text-2xl font-bold text-green-700 dark:text-green-300">
                    {summaryStats.totalIncome.toLocaleString(
                      language === "ar" ? "ar-EG" : "en-US",
                      {
                        style: "currency",
                        currency: "EGP",
                        minimumFractionDigits: 0,
                      },
                    )}
                  </p>
                  <p className="mt-1 text-xs text-green-600 dark:text-green-400">
                    {incomeEvents.length} {t("transactions")}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </div>
            </div>

            <div className="overflow-hidden rounded-lg bg-gradient-to-r from-red-50 to-red-100 p-6 dark:from-red-900/20 dark:to-red-800/20">
              <div className="flex min-h-[100px] items-start justify-between">
                <div className="min-w-0 flex-1 pr-4">
                  <p className="mb-2 text-sm text-red-600 dark:text-red-400">
                    {t("Total Expense")}
                  </p>
                  <p className="truncate text-2xl font-bold text-red-700 dark:text-red-300">
                    {summaryStats.totalExpense.toLocaleString(
                      language === "ar" ? "ar-EG" : "en-US",
                      {
                        style: "currency",
                        currency: "EGP",
                        minimumFractionDigits: 0,
                      },
                    )}
                  </p>
                  <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                    {expenseEvents.length} {t("transactions")}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-red-500" />
                </div>
              </div>
            </div>

            <div
              className={`overflow-hidden rounded-lg bg-gradient-to-r p-6 ${
                summaryStats.netProfit >= 0
                  ? "from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20"
                  : "from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20"
              }`}
            >
              <div className="flex min-h-[100px] items-start justify-between">
                <div className="min-w-0 flex-1 pr-4">
                  <p
                    className={`mb-2 text-sm ${
                      summaryStats.netProfit >= 0
                        ? "text-blue-600 dark:text-blue-400"
                        : "text-orange-600 dark:text-orange-400"
                    }`}
                  >
                    {t("Net Profit")}
                  </p>
                  <p
                    className={`truncate text-2xl font-bold ${
                      summaryStats.netProfit >= 0
                        ? "text-blue-700 dark:text-blue-300"
                        : "text-orange-700 dark:text-orange-300"
                    }`}
                  >
                    {summaryStats.netProfit.toLocaleString(
                      language === "ar" ? "ar-EG" : "en-US",
                      {
                        style: "currency",
                        currency: "EGP",
                        minimumFractionDigits: 0,
                      },
                    )}
                  </p>
                  <p className="mt-1 whitespace-nowrap text-xs text-gray-600 dark:text-gray-400">
                    {summaryStats.profitMargin.toFixed(1)}% {t("margin")}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <Target className="h-8 w-8 text-blue-500" />
                </div>
              </div>
            </div>

            <div className="overflow-hidden rounded-lg bg-gradient-to-r from-purple-50 to-purple-100 p-6 dark:from-purple-900/20 dark:to-purple-800/20">
              <div className="flex min-h-[100px] items-start justify-between">
                <div className="min-w-0 flex-1 pr-4">
                  <p className="mb-2 text-sm text-purple-600 dark:text-purple-400">
                    {t("Total Transactions")}
                  </p>
                  <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                    {summaryStats.transactionCount}
                  </p>
                  <p className="mt-1 text-xs text-purple-600 dark:text-purple-400">
                    {t("in selected period")}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <BarChart3 className="h-8 w-8 text-purple-500" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-t dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {[
              { key: "overview", label: t("Overview"), icon: BarChart3 },
              { key: "income", label: t("Income"), icon: DollarSign },
              { key: "expenses", label: t("Expenses"), icon: TrendingUp },
              { key: "advanced", label: t("Advanced"), icon: Settings },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex items-center space-x-2 border-b-2 px-1 py-4 text-sm font-medium ${
                  activeTab === tab.key
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === "overview" && (
          <>
            <SafeAnalyticsWrapper data={filteredEvents}>
              <AnalyticsSection events={filteredEvents} />
            </SafeAnalyticsWrapper>

            <SafeAnalyticsWrapper data={filteredEvents}>
              <IncomeVsExpenseSection events={filteredEvents} />
            </SafeAnalyticsWrapper>
          </>
        )}

        {activeTab === "income" && (
          <SafeAnalyticsWrapper data={incomeEvents}>
            <IncomeSummary incomes={incomeEvents} loading={isLoading} />
          </SafeAnalyticsWrapper>
        )}

        {activeTab === "expenses" && (
          <SafeAnalyticsWrapper data={expenseEvents}>
            <ExpenseSummary expenses={expenseEvents} loading={isLoading} />
          </SafeAnalyticsWrapper>
        )}

        {activeTab === "advanced" && (
          <SafeAnalyticsWrapper data={filteredEvents}>
            <AdvancedAnalyticsSection events={filteredEvents} />
          </SafeAnalyticsWrapper>
        )}
      </div>
    </div>
  );
};

export default ImprovedDashboard;

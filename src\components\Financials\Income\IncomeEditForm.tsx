import React, { useState, useEffect } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import { UpdateIncomePayload, IncomeEditResponse, IncomeEditError, canEditIncome, formatAmountForAPI, toISOString } from "@/types/income";
import IncomeServices from "@/lib/income";
import { toast } from "react-hot-toast";
import useLanguage from "@/hooks/useLanguage";
import { FaSave, FaTimes, FaExclamationTriangle } from "react-icons/fa";

interface IncomeEditFormProps {
  income: EventDetails;
  onClose: () => void;
  onSave: (updatedIncome: IncomeEditResponse) => void;
  onError?: (error: IncomeEditError) => void;
}

const IncomeEditForm: React.FC<IncomeEditFormProps> = ({
  income,
  onClose,
  onSave,
  onError,
}) => {
  const { t } = useLanguage();
  const incomeServices = IncomeServices();
  
  const [formData, setFormData] = useState<UpdateIncomePayload>({
    title: income.title || "",
    amount: income.amount || 0,
    due_date: income.dueDate ? new Date(income.dueDate).toISOString() : new Date().toISOString(),
    received_date: income.received_date ? new Date(income.received_date).toISOString() : null,
    description: income.description || "",
    status: income.status || "pending",
    priority: income.priority || "medium",
    type_id: income.type || undefined,
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Check if income can be edited
  const canEdit = canEditIncome(income);

  useEffect(() => {
    if (!canEdit) {
      const errorMessage = income.reservation_id 
        ? t("Cannot edit income linked to reservation")
        : t("Cannot edit income linked to contract");
      toast.error(errorMessage);
    }
  }, [canEdit, income, t]);

  const handleInputChange = (field: keyof UpdateIncomePayload, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = t("Title is required");
    }

    if (formData.amount <= 0) {
      newErrors.amount = t("Amount must be greater than 0");
    }

    if (formData.amount > 9999999999.99) {
      newErrors.amount = t("Amount exceeds maximum allowed value");
    }

    if (!formData.due_date) {
      newErrors.due_date = t("Due date is required");
    }

    // Validate received_date if status is completed
    if (formData.status === "completed" && !formData.received_date) {
      newErrors.received_date = t("Received date is required for completed income");
    }

    // Cannot set received_date for cancelled income
    if (formData.status === "cancelled" && formData.received_date) {
      newErrors.received_date = t("Cannot set received date for cancelled income");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!canEdit) {
      toast.error(t("This income cannot be edited"));
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Format the payload
      const payload: UpdateIncomePayload = {
        ...formData,
        amount: formatAmountForAPI(formData.amount),
        due_date: toISOString(formData.due_date),
        received_date: formData.received_date ? toISOString(formData.received_date) : null,
      };

      const updatedIncome = await incomeServices.editIncome(income.id, payload);
      
      toast.success(t("Income updated successfully"));
      onSave(updatedIncome);
      onClose();
    } catch (error: any) {
      console.error("Error updating income:", error);
      
      if (error.can_update === false) {
        // Handle 409 conflict error
        if (error.reservation) {
          toast.error(t("Income is linked to reservation: ") + error.reservation.title);
        } else if (error.contract) {
          toast.error(t("Income is linked to contract: ") + error.contract.title);
        } else {
          toast.error(error.error || t("Cannot update this income"));
        }
        
        if (onError) {
          onError(error as IncomeEditError);
        }
      } else if (error.details) {
        // Handle validation errors
        const fieldErrors: Record<string, string> = {};
        Object.keys(error.details).forEach(field => {
          fieldErrors[field] = error.details[field][0] || t("Invalid value");
        });
        setErrors(fieldErrors);
        toast.error(t("Please fix the validation errors"));
      } else {
        toast.error(error.message || t("Failed to update income"));
      }
    } finally {
      setLoading(false);
    }
  };

  if (!canEdit) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
          <div className="flex items-center gap-3 text-amber-600">
            <FaExclamationTriangle className="text-xl" />
            <h3 className="text-lg font-semibold">{t("Cannot Edit Income")}</h3>
          </div>
          <p className="mt-3 text-gray-600 dark:text-gray-300">
            {income.reservation_id 
              ? t("This income is linked to a reservation and cannot be edited.")
              : t("This income is linked to a contract and cannot be edited.")
            }
          </p>
          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="rounded-lg bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
            >
              {t("Close")}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-2xl rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
        <h2 className="mb-6 text-xl font-semibold text-gray-900 dark:text-white">
          {t("Edit Income")}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Title")} *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              className={`w-full rounded-lg border p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.title ? "border-red-500" : "border-gray-300 dark:border-gray-600"
              }`}
              placeholder={t("Enter income title")}
            />
            {errors.title && <p className="mt-1 text-sm text-red-500">{errors.title}</p>}
          </div>

          {/* Amount */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Amount")} *
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="9999999999.99"
              value={formData.amount}
              onChange={(e) => handleInputChange("amount", parseFloat(e.target.value) || 0)}
              className={`w-full rounded-lg border p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.amount ? "border-red-500" : "border-gray-300 dark:border-gray-600"
              }`}
              placeholder={t("Enter amount")}
            />
            {errors.amount && <p className="mt-1 text-sm text-red-500">{errors.amount}</p>}
          </div>

          {/* Due Date */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Due Date")} *
            </label>
            <input
              type="datetime-local"
              value={formData.due_date ? new Date(formData.due_date).toISOString().slice(0, 16) : ""}
              onChange={(e) => handleInputChange("due_date", new Date(e.target.value).toISOString())}
              className={`w-full rounded-lg border p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.due_date ? "border-red-500" : "border-gray-300 dark:border-gray-600"
              }`}
            />
            {errors.due_date && <p className="mt-1 text-sm text-red-500">{errors.due_date}</p>}
          </div>

          {/* Received Date */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Received Date")}
            </label>
            <input
              type="datetime-local"
              value={formData.received_date ? new Date(formData.received_date).toISOString().slice(0, 16) : ""}
              onChange={(e) => handleInputChange("received_date", e.target.value ? new Date(e.target.value).toISOString() : null)}
              className={`w-full rounded-lg border p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.received_date ? "border-red-500" : "border-gray-300 dark:border-gray-600"
              }`}
            />
            {errors.received_date && <p className="mt-1 text-sm text-red-500">{errors.received_date}</p>}
          </div>

          {/* Status */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Status")}
            </label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange("status", e.target.value as any)}
              className="w-full rounded-lg border border-gray-300 p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="pending">{t("Pending")}</option>
              <option value="completed">{t("Completed")}</option>
              <option value="cancelled">{t("Cancelled")}</option>
              <option value="upcoming">{t("Upcoming")}</option>
              <option value="overdue">{t("Overdue")}</option>
            </select>
          </div>

          {/* Priority */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Priority")}
            </label>
            <select
              value={formData.priority}
              onChange={(e) => handleInputChange("priority", e.target.value as any)}
              className="w-full rounded-lg border border-gray-300 p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="low">{t("Low")}</option>
              <option value="medium">{t("Medium")}</option>
              <option value="high">{t("High")}</option>
            </select>
          </div>

          {/* Description */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Description")}
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={3}
              className="w-full rounded-lg border border-gray-300 p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
              placeholder={t("Enter description")}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex items-center gap-2 rounded-lg bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
              disabled={loading}
            >
              <FaTimes className="text-sm" />
              {t("Cancel")}
            </button>
            <button
              type="submit"
              className="flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50"
              disabled={loading}
            >
              <FaSave className="text-sm" />
              {loading ? t("Saving...") : t("Save")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default IncomeEditForm;

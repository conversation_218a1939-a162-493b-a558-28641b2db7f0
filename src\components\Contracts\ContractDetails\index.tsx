import React, { useState, useEffect, useMemo } from "react";
import { Contract, ContractFormData } from "@/lib/interfaces/contract";
import { EventDetails } from "@/lib/interfaces/finaces";
import ContractForm from "../ContractForm";
import useLanguage from "@/hooks/useLanguage";
import ContractHeader from "./ContractHeader";
import DetailsTabs from "./DetailsTabs";
import FinancialSummary from "./FinancialSummary";
import DetailsTab from "./tabs/DetailsTab";
import PaymentsTab from "./tabs/PaymentsTab";
import RenewalTab from "./tabs/RenewalTab";
import DocumentsTab from "./tabs/DocumentsTab";
import ContractServices from "@/lib/contracts";
import { generatePDF } from "@/components/Dashboard/UpcommingEventsPage/generatePDF";
import { toast } from "react-hot-toast";
import PDFGeneratorModal from "@/components/Dashboard/UpcommingEventsPage/PDFGeneratorModal";
import { usePDFGenerator } from "@/hooks/usePDFGenerator";

interface ContractDetailsProps {
  contract: Contract;
  onClose: () => void;
  onUpdate: (contract: Contract) => void;
  onDelete: () => void;
  canEdit?: boolean;
  canDelete?: boolean;
  canExport?: boolean;
}

const ContractDetails: React.FC<ContractDetailsProps> = ({
  contract,
  onClose,
  onUpdate,
  onDelete,
  canEdit = true,
  canDelete = true,
  canExport = true,
}) => {
  const { t, language } = useLanguage();
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "details" | "payments" | "renewal" | "documents"
  >("details");
  const [expenseData, setExpenseData] = useState<EventDetails[]>([]);
  const [incomeData, setIncomeData] = useState<EventDetails[]>([]);
  const [isLoadingExpenses, setIsLoadingExpenses] = useState(false);
  const [expenseError, setExpenseError] = useState<string | null>(null);
  const [hasLoadedExpenseData, setHasLoadedExpenseData] = useState(false);
  const { modalProps, openPDFModal, closePDFModal } = usePDFGenerator();
  const [isFinancialDataReady, setIsFinancialDataReady] = useState(false);
  const ContractService = ContractServices();

  useEffect(() => {
    const fetchExpenseData = async () => {
      setIsLoadingExpenses(true);
      try {
        const response = await ContractService.getContractEventsById(
          contract.id,
        );
        if (response) {
          setExpenseData(response.expenses);
          setIncomeData(response.incomes);
          setIsFinancialDataReady(true);
        }
      } catch (error) {
        setExpenseError(t("Failed to load expense data"));
      } finally {
        setIsLoadingExpenses(false);
        setHasLoadedExpenseData(true);
      }
    };

    if (!hasLoadedExpenseData) {
      fetchExpenseData();
    }
  }, [contract.id, hasLoadedExpenseData, t, ContractService]);
  // Reset the loaded flag when contract ID changes
  useEffect(() => {
    setHasLoadedExpenseData(false);
    setExpenseData([]);
    setIsFinancialDataReady(false);
  }, [contract.id]);

  // Calculate total paid and remaining using useMemo
  const { totalPaid, totalRemaining } = useMemo(() => {
    console.log("Calculating totals with expenseData:", expenseData);
    console.log("Contract total amount:", contract.total_amount);

    // For contracts, we calculate based on expense events (payments we've made)
    // Sum from expense events with status completed
    const expensesPaid = expenseData
      .filter((event) => event.status === "completed")
      .reduce((sum, event) => {
        const amount = Number(event.amount) || 0;
        console.log(
          `Adding expense amount: ${amount} (status: ${event.status})`,
        );
        return sum + amount;
      }, 0);

    console.log("Total expenses paid:", expensesPaid);

    // For contracts, we track expenses (payments we've made)
    const paid = expensesPaid;
    const contractTotal = Number(contract.total_amount) || 0;
    const remaining = contractTotal - paid;

    console.log("Final calculation - paid:", paid, "remaining:", remaining);

    return {
      totalPaid: paid,
      totalRemaining: remaining,
    };
  }, [expenseData, contract.total_amount]);

  // Handle printing the contract
  const handlePrint = () => {
    const printContent = document.getElementById("printable-contract");
    if (printContent) {
      const originalBody = document.body.innerHTML;
      document.body.innerHTML = printContent.innerHTML;
      window.print();
      document.body.innerHTML = originalBody;
    }
  };

  // Handle downloading the contract as PDF - UPDATED TO USE NEW PDF MODAL
  const handleDownload = () => {
    // Open the new PDF generator modal for contracts with associated incomes/expenses
    openPDFModal(
      {
        contracts: [contract],
        events: [...expenseData, ...incomeData] // Include all associated events
      },
      `${t("contractReport")} - ${contract.title}`,
      'contracts'
    );
  }; // Function to transform Contract to ContractFormData
  const transformContractToFormData = (
    contract: Contract,
  ): ContractFormData => {
    // Transform both expense and income data to installments format
    const installments: any[] = [];
    // Add expenses as installments
    expenseData.forEach((expense) => {
      installments.push({
        id: expense.id,
        amount: expense.amount,
        due_date: expense.dueDate ? expense.dueDate.split("T")[0] : "",
        notification_view_date: expense.dueDate
          ? expense.dueDate.split("T")[0]
          : "",
        status: expense.status,
        priority: expense.priority || "medium",
        paid_date: expense.paid_date ? expense.paid_date.split("T")[0] : "",
        type: "expense" as const,
        title: expense.title || "",
        description: expense.description || "",
        contact_id: expense.contact?.id || contract.client?.id || "",
        location_id: expense.location?.id || contract.location?.id || "",
        type_id: (expense as any).type_id || "",
        is_deleted: false,
      });
    });

    // Add incomes as installments
    incomeData.forEach((income) => {
      installments.push({
        id: income.id,
        amount: income.amount,
        due_date: income.dueDate ? income.dueDate.split("T")[0] : "",
        notification_view_date: income.dueDate
          ? income.dueDate.split("T")[0]
          : "",
        status: income.status,
        priority: income.priority || "medium",
        paid_date: income.paid_date ? income.paid_date.split("T")[0] : "",
        type: "income" as const,
        title: income.title || "",
        description: income.description || "",
        contact_id: income.contact?.id || contract.client?.id || "",
        location_id: income.location?.id || contract.location?.id || "",
        expense_id: (income as any).expense_id, // Link to parent expense if exists
        is_deleted: false,
      });
    });

    // Sort installments by due date and group incomes under their expenses
    const sortedInstallments = installments.sort((a, b) => {
      // First, sort by expense relationship
      if (a.expense_id === b.id) return 1; // a is income of b (expense)
      if (b.expense_id === a.id) return -1; // b is income of a (expense)

      // Then by due date
      const dateA = new Date(a.due_date).getTime();
      const dateB = new Date(b.due_date).getTime();
      return dateA - dateB;
    });

    // Transform document uploads to proper format
    const documentUpload = (contract.document_uploaded || []).map(
      (doc, index) => ({
        id: doc.id || `doc-${index}`,
        name: doc.name || `Document ${index + 1}`,
        url: doc.url || "",
        uploadDate: doc.uploadDate || new Date().toISOString(),
        type: doc.type || "uploaded",
        fileType: doc.fileType || "document",
      }),
    );

    return {
      priority: "medium", // Default priority since not in Contract interface
      contact_id: contract.client?.id || "",
      location_id: contract.location?.id || "",
      type_id: "", // Default empty since not in Contract interface
      contract: {
        id: contract.id,
        title: contract.title,
        description: contract.description,
        start_date: contract.start_date ? new Date(contract.start_date).toISOString().split('T')[0] : "",
        end_date: contract.end_date ? new Date(contract.end_date).toISOString().split('T')[0] : "",
        total_amount: contract.total_amount,
        status: contract.status,
        notes: contract.notes || "",
        // Pass the financial events data for the installments component
        expense_events: expenseData,
        income_events: incomeData,
      } as any,
      installments: sortedInstallments as any,
      documentUpload: documentUpload,
      installmentCount: sortedInstallments.length,
      renewal_terms: contract.renewal_terms || {
        auto_renew: false,
        increase_percentage: 0,
        notice_period_days: 0,
      },
    };
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  // Enhanced contract editing with proper data transformation
  const handleEdit = () => {
    const contractFormData = transformContractToFormData(contract);

    // Add expense data as contractFinancials for the installments popup
    const enhancedFormData = {
      ...contractFormData,
      contractFinancials: expenseData, // Pass expense data for installments
    };

    setIsEditing(true);
    // You would typically navigate to the contract form with this data
    // Example: router.push({ pathname: '/contracts/edit', state: enhancedFormData });
  };

  // Enhanced contract update handler
  const handleContractUpdate = async (updatedContract: Contract) => {
    setIsUpdating(true);
    const loadingToast = toast.loading(t("Updating contract..."));

    try {
      // Update parent component optimistically
      onUpdate(updatedContract);

      // Refresh financial data for updated contract
      setHasLoadedExpenseData(false);
      setExpenseData([]);
      setIncomeData([]);
      setIsFinancialDataReady(false);

      // Success feedback
      toast.dismiss(loadingToast);
      toast.success(t("Contract updated successfully"));

      // Return to details view
      setIsEditing(false);
    } catch (error: unknown) {
      toast.dismiss(loadingToast);
      toast.error(t("Failed to update contract"));
      console.error("Error updating contract:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Function to update event status in local state
  const handleEventStatusUpdate = (
    eventId: string,
    newStatus: "completed" | "pending" | "cancelled" | "upcoming" | "overdue",
    type: "income" | "expense",
  ) => {
    if (type === "income") {
      setIncomeData((prevData) =>
        prevData.map((event) =>
          event.id === eventId ? { ...event, status: newStatus } : event,
        ),
      );
    } else {
      setExpenseData((prevData) =>
        prevData.map((event) =>
          event.id === eventId ? { ...event, status: newStatus } : event,
        ),
      );
    }
  };

  // If editing, show the form instead of details
  if (isEditing) {
    const formData = transformContractToFormData(contract);
    return (
      <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800 sm:p-6">
        <ContractForm
          contract={formData}
          onCancel={handleCancelEdit}
          onUpdate={handleContractUpdate}
          isUpdating={isUpdating}
        />
      </div>
    );
  }

  // Render the contract details
  return (
    <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800 sm:p-6">
      <div id="printable-contract">
        {/* Header */}
        <ContractHeader
          contract={contract}
          onEdit={handleEdit}
          onDelete={onDelete}
          onClose={onClose}
          onDownload={handleDownload}
          totalPaid={totalPaid}
          totalRemaining={totalRemaining}
          isFinancialDataReady={isFinancialDataReady}
          canEdit={canEdit}
          canDelete={canDelete}
          canExport={canExport}
        />

        {/* Navigation Tabs */}
        <DetailsTabs activeTab={activeTab} onTabChange={setActiveTab} />

        {/* Financial Summary */}
        <FinancialSummary
          contract={contract}
          totalPaid={totalPaid}
          totalRemaining={totalRemaining}
          isFinancialDataReady={isFinancialDataReady}
        />

        {/* Tab Content */}
        {activeTab === "details" && <DetailsTab contract={contract} />}
        {activeTab === "payments" && (
          <PaymentsTab
            contract={contract}
            expenseData={expenseData}
            totalPaid={totalPaid}
            totalRemaining={totalRemaining}
            isFinancialDataReady={isFinancialDataReady}
            incomeData={incomeData}
            isLoading={isLoadingExpenses}
            error={expenseError}
            onEventStatusUpdate={handleEventStatusUpdate}
          />
        )}
        {activeTab === "renewal" && <RenewalTab contract={contract} />}
        {activeTab === "documents" && <DocumentsTab contract={contract} />}
      </div>

      {/* PDF Generator Modal - NEW ADVANCED PDF GENERATOR */}
      <PDFGeneratorModal
        isOpen={modalProps.isOpen}
        onClose={modalProps.onClose}
        data={modalProps.data}
        title={modalProps.title}
        mode={modalProps.mode}
      />
    </div>
  );
};

export default ContractDetails;

// Define interfaces for financial history components specific to contacts

export interface Reservation {
  id: string;
  locationName: string;
  locationId: string;
  startDate: Date | string;
  endDate: Date | string;
  status: 'ongoing' | 'completed' | 'cancelled';
  totalAmount: number;
  ourPercentage: number;
  agencyPercentage?: number;
  payments: {
    id: string;
    date: Date | string;
    amount: number;
    type: 'deposit' | 'installment' | 'final';
    status: 'paid' | 'pending' | 'overdue';
  }[];
  capacity: number;
}

export interface Income {
  id: string;
  date: Date | string;
  amount: number;
  source: 'reservation' | 'event';
  locationName: string;
  description: string;
  status: 'completed' | 'pending' | 'cancelled';
  percentage?: number;
}

export interface Expense {
  id: string;
  date: Date | string;
  amount: number;
  type: 'contract' | 'event';
  locationName: string;
  description: string;
  status: 'paid' | 'pending' | 'overdue';
  recurring?: boolean;
}

export interface Contract {
  id: string;
  locationName: string;
  locationId: string;
  startDate: Date | string;
  endDate: Date | string;
  status: 'active' | 'completed' | 'terminated';
  totalAmount: number;
  payments: {
    id: string;
    date: Date | string;
    amount: number;
    type: 'initial' | 'installment' | 'final';
    status: 'paid' | 'pending' | 'overdue';
  }[];
  contractType: string;
  description: string;
  recurring: boolean;
}

export interface FinancialHistoryProps {
  name: string | undefined;
  reservations?: Reservation[];
  incomes?: Income[];
  expenses?: Expense[];
  contracts?: Contract[];
  isLoading?: boolean;
}

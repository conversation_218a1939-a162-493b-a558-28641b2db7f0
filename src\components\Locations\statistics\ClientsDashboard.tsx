import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import useLanguage from "@/hooks/useLanguage";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON> as Re<PERSON>hartsBar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as <PERSON>C<PERSON>sTooltip,
  Legend as <PERSON><PERSON><PERSON>sLegend,
  ResponsiveContainer,
  LineChart as ReChartsLine<PERSON>hart,
  Line,
  Pie<PERSON>hart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';

interface ClientsDashboardProps {
  clientData: Array<{
    name: string;
    revenue: number;
    reservations: number;
    adTypes: string[];
  }>;
  renderActiveShape: (props: any) => JSX.Element;
  COLORS: string[];
  clientRevenuePieData: Array<{ name: string; value: number; }>;
  totalClients: number;
}

const ClientsDashboard: React.FC<ClientsDashboardProps> = ({
  clientData,
  renderActiveShape,
  COLORS,
  clientRevenuePieData,
  totalClients
}) => {
  const { t } = useLanguage();
  const [activePieIndex, setActivePieIndex] = useState(0);
  
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-6">
          <h3 className="text-lg font-medium">{t("clientAnalysis")}</h3>
          <Button variant="outline" size="sm" className="self-start sm:self-auto h-8">
            {t("exportData")}
          </Button>
        </div>
        
        <div className="h-80 w-full">
          <ResponsiveContainer width="99%" height="100%">
            <ReChartsPieChart>
              <Pie
                activeIndex={activePieIndex}
                activeShape={renderActiveShape}
                data={clientRevenuePieData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                dataKey="value"
                onMouseEnter={(_, index) => setActivePieIndex(index)}
              >
                {clientRevenuePieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <ReChartsTooltip />
            </ReChartsPieChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
            {t("topClients")}
          </h4>
          
          <div className="w-full overflow-x-auto">
            <div className="min-w-[640px]">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b dark:border-gray-700">
                    <th className="text-left py-3 px-4">{t("client")}</th>
                    <th className="text-right py-3 px-4">{t("revenue")}</th>
                    <th className="text-right py-3 px-4">{t("reservations")}</th>
                  </tr>
                </thead>
                <tbody>
                  {clientData.slice(0, 5).map((client, idx) => (
                    <tr 
                      key={client.name} 
                      className={idx < 4 ? "border-b dark:border-gray-700" : ""}
                    >
                      <td className="py-3 px-4 font-medium">{client.name}</td>
                      <td className="text-right py-3 px-4 font-medium">${client.revenue.toLocaleString()}</td>
                      <td className="text-right py-3 px-4">{client.reservations}</td>
                      <td className="py-3 px-4">
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      {/* Client Acquisition & Retention */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm">
        <h3 className="text-lg font-medium mb-6">{t("clientRetentionAnalysis")}</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="w-full">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
              {t("clientDistribution")}
            </h4>
            <div className="h-64 w-full">
              <ResponsiveContainer width="99%" height="100%">
                <ReChartsBarChart
                  data={[
                    { name: t("new"), value: 3 },
                    { name: t("returning"), value: 5 },
                    { name: t("loyal"), value: totalClients - 8 }
                  ]}
                  layout="vertical"
                  margin={{ top: 5, right: 10, left: 20, bottom: 5 }}
                >
                  <CartesianGrid horizontal={false} strokeDasharray="3 3" />
                  <XAxis type="number" axisLine={false} tickLine={false} />
                  <YAxis 
                    type="category" 
                    dataKey="name" 
                    axisLine={false} 
                    tickLine={false} 
                  />
                  <ReChartsTooltip />
                  <Bar 
                    dataKey="value" 
                    name={t("clients")} 
                    fill="#3b82f6"
                    radius={[0, 4, 4, 0]}
                  />
                </ReChartsBarChart>
              </ResponsiveContainer>
            </div>
          </div>
          
          <div className="w-full">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
              {t("clientRetentionRate")}
            </h4>
            <div className="h-64 w-full">
              <ResponsiveContainer width="99%" height="100%">
                <ReChartsLineChart
                  data={[
                    { period: 'Q1', rate: 82 },
                    { period: 'Q2', rate: 85 },
                    { period: 'Q3', rate: 83 },
                    { period: 'Q4', rate: 87 }
                  ]}
                  margin={{ top: 5, right: 10, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="period" axisLine={false} tickLine={false} />
                  <YAxis 
                    axisLine={false} 
                    tickLine={false} 
                    domain={[75, 90]}
                    tickFormatter={(value) => `${value}%`}
                  />
                  <ReChartsTooltip formatter={(value: any) => [`${value}%`, t("retentionRate")]} />
                  <ReChartsLegend />
                  <Line 
                    type="monotone" 
                    dataKey="rate" 
                    name={t("retentionRate")}
                    stroke="#10b981" 
                    strokeWidth={2}
                    dot={{ r: 4 }} 
                  />
                </ReChartsLineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientsDashboard;

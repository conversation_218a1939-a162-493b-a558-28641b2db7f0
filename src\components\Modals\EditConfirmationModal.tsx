import { useState, useEffect } from 'react';
import { X, Edit2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/cards/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import useLanguage from "@/hooks/useLanguage";

interface EditConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  itemName: string;
}

const EditConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  itemName,
}: EditConfirmationModalProps) => {
  const [confirmText, setConfirmText] = useState('');
  const [mounted, setMounted] = useState(false);
  const { t } = useLanguage();

  useEffect(() => {
    setMounted(true);
    if (isOpen) {
      setConfirmText('');
    }
  }, [isOpen]);

  if (!mounted || !isOpen) return null;

  const isEditEnabled = confirmText.toLowerCase() === 'edit';

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-200" 
        onClick={onClose} 
      />
      <Card className="relative w-full max-w-md mx-4 z-50 shadow-xl transform transition-all duration-200 scale-100 bg-white dark:bg-gray-900">
        <CardHeader className="space-y-1 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-full dark:bg-blue-900/20">
                <Edit2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">
                {title}
              </CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6 p-6 pt-0">
          <div className="space-y-4">
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {t("editConfirmationMessage")} <span className="font-medium">{itemName}</span>
            </p>
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("typeEditToConfirm")}
              </label>
              <Input
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder={t("typeLiteralEdit")}
                className={`w-full transition-colors duration-200 ${
                  isEditEnabled 
                    ? 'border-blue-500 focus:ring-blue-500' 
                    : 'border-gray-200 dark:border-gray-700'
                }`}
                autoComplete="off"
                autoCapitalize="off"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {t("editWarning")}
              </p>
            </div>
          </div>
          <div className="flex justify-end gap-3 pt-2">
            <Button
              variant="outline"
              onClick={onClose}
              className="border-gray-200 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
            >
              {t("cancel")}
            </Button>
            <Button
              variant="default"
              onClick={onConfirm}
              disabled={!isEditEnabled}
              className={`${
                isEditEnabled
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-500'
              }`}
            >
              {t("edit")}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditConfirmationModal;
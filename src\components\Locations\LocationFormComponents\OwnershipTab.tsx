import React, { useState } from "react";
import { Plus, Trash, AlertCircle, Percent } from "lucide-react";
import { OwnershipTabProps, OwnershipShare } from "./types";
import ContactSelectDropdown from "./ContactSelectDropdown";

const OwnershipTab: React.FC<OwnershipTabProps> = ({
  formData,
  handleChange,
  ownershipShares,
  setOwnershipShares,
  isEgyCommPrimaryOwner,
  setIsEgyCommPrimaryOwner,
  isOwnedByEgyComm,
  setIsOwnedByEgyComm,
  handleOwnedBySelect,
  contactOptions,
  sharedWith,
  t,
  totalPercentage,
  percentageError,
  validatePercentages,
}) => {
  const [newPartnerShare, setNewPartnerShare] = useState<OwnershipShare>({
    name: "",
    percentage: 0,
  });

  const updatePartnerPercentage = (index: number, percentage: number) => {
    const updatedShares = [...ownershipShares];
    updatedShares[index].percentage = percentage;
    setOwnershipShares(updatedShares);

    // Only auto-adjust EgyComm's percentage if EgyComm is the primary owner
    if (isEgyCommPrimaryOwner) {
      // Calculate total partners percentage
      const partnersTotal = updatedShares.reduce(
        (sum, share) => sum + share.percentage,
        0,
      );

      // Adjust EgyComm's percentage to maintain total of 100%
      const egyCommAdjustedPercentage = Math.max(0, 100 - partnersTotal);
      handleChange("ourPercentage", parseFloat(egyCommAdjustedPercentage.toFixed(2)));
    }

    validatePercentages();
  };

  // Calculate combined total percentage
  const combinedTotalPercentage =
    totalPercentage + (formData.ourPercentage || 0);

  return (
    <div className="space-y-6">
      {/* EgyComm Primary Owner Checkbox */}
      <div className="flex items-center space-x-2 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
        <input
          type="checkbox"
          id="egycomm-primary-owner"
          checked={isEgyCommPrimaryOwner}
          onChange={(e) => {
            const isChecked = e.target.checked;
            setIsEgyCommPrimaryOwner(isChecked);

            if (isChecked) {
              // EgyComm is primary owner - clear other owners and set to 100% if no partners
              setIsOwnedByEgyComm(true);
              handleChange("ownedBy", "EgyComm");
              
              // If there are no ownership shares, set EgyComm to 100%
              if (ownershipShares.length === 0) {
                handleChange("ourPercentage", 100);
              }
            } else {
              // No primary owner set yet
              setIsOwnedByEgyComm(false);
              handleChange("ownedBy", "");
              
              // Reset EgyComm percentage to allow for other primary owner
              if (formData.ourPercentage === 100) {
                handleChange("ourPercentage", 50);
              }
            }
          }}
          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label
          htmlFor="egycomm-primary-owner"
          className="text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          {t("egyCommIsPrimaryOwner")}
        </label>
        <div className="ml-2 text-xs text-gray-500">
          {isEgyCommPrimaryOwner
            ? t("allPartnersSecondary")
            : t("selectPrimaryOwner")}
        </div>
      </div>

      {/* Only show this section if EgyComm is NOT the primary owner */}
      {!isEgyCommPrimaryOwner && (
        <div className="grid grid-cols-1 items-end gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("primaryOwner")}
            </label>
            <ContactSelectDropdown
              options={contactOptions}
              value={formData.ownedBy || ""}
              onSelect={(owner) => {
                handleOwnedBySelect(owner);
              }}
              placeholder={t("selectPrimaryOwner")}
            />
            <p className="mt-1 text-xs text-gray-500">
              {t("primaryOwnerDescription")}
            </p>
          </div>
        </div>
      )}

      {/* Always show EgyComm's percentage */}
      <div className="grid grid-cols-1 gap-4">
        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("egyCommPercentage")}
          </label>
          <div className="relative">
            <input
              type="number"
              min="0"
              max="100"
              step="0.01"
              value={formData.ourPercentage || 0}
              onChange={(e) => {
                const newPercentage = parseFloat(parseFloat(e.target.value).toFixed(2)) || 0;
                handleChange("ourPercentage", newPercentage);

                // If we're setting EgyComm to 100%, make it the primary owner
                if (newPercentage === 100 && !isEgyCommPrimaryOwner) {
                  setIsEgyCommPrimaryOwner(true);
                  setIsOwnedByEgyComm(true);
                  handleChange("ownedBy", "EgyComm");
                  setOwnershipShares([]);
                }

                // Only auto-adjust partners if EgyComm is primary owner
                if (isEgyCommPrimaryOwner) {
                  // Update the remaining percentages
                  const remainingPercentage = 100 - newPercentage;
                  if (ownershipShares.length > 0) {
                    const currentTotal = ownershipShares.reduce(
                      (sum, share) => sum + share.percentage,
                      0,
                    );
                    const updatedShares = [...ownershipShares];

                    if (currentTotal > 0) {
                      // Proportionally adjust each partner's percentage with decimal precision
                      updatedShares.forEach((share) => {
                        share.percentage = parseFloat(
                          ((share.percentage / currentTotal) * remainingPercentage).toFixed(2)
                        );
                      });

                      // Adjust for rounding errors by adding/subtracting from the first partner
                      const newTotal = updatedShares.reduce(
                        (sum, share) => sum + share.percentage,
                        0,
                      );
                      if (
                        Math.abs(newTotal - remainingPercentage) > 0.01 &&
                        updatedShares.length > 0
                      ) {
                        updatedShares[0].percentage = parseFloat(
                          (updatedShares[0].percentage + (remainingPercentage - newTotal)).toFixed(2)
                        );
                      }

                      setOwnershipShares(updatedShares);
                    }
                  }
                }
              }}
              className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
            />
            <Percent className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
          </div>
          <p className="mt-1 text-sm text-gray-500">
            {isEgyCommPrimaryOwner
              ? t("egyCommIsMainOwner")
              : t("egyCommPercentageDescription")}
          </p>
        </div>
      </div>

      {/* Percentage Allocation Visualization */}
      <div>
        <div className="mb-1 flex justify-between text-sm">
          <span className="font-medium">{t("percentageAllocation")}</span>
          <span
            className={`font-medium ${
              combinedTotalPercentage !== 100
                ? "text-red-500"
                : "text-green-500"
            }`}
          >
            {combinedTotalPercentage}%{" "}
            {combinedTotalPercentage === 100 ? "✓" : ""}
          </span>
        </div>
        <div className="flex h-6 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
          {/* EgyComm's percentage first - always shown */}
          <div
            className="flex h-full items-center justify-center bg-blue-600 text-xs font-bold text-white"
            style={{ width: `${formData.ourPercentage || 0}%` }}
          >
            {(formData.ourPercentage || 0) > 10
              ? `${formData.ourPercentage || 0}%`
              : ""}
          </div>

          {/* Partners percentages */}
          {ownershipShares.map((share, idx) => {
            const colors = [
              "bg-green-500 dark:bg-green-600",
              "bg-purple-500 dark:bg-purple-600",
              "bg-yellow-500 dark:bg-yellow-600",
              "bg-pink-500 dark:bg-pink-600",
              "bg-red-500 dark:bg-red-600",
            ];
            const color = colors[idx % colors.length];

            // Highlight primary owner if not EgyComm
            const isPrimaryOwner =
              !isEgyCommPrimaryOwner && share.name === formData.ownedBy;
            const borderClass = isPrimaryOwner ? "ring-2 ring-white" : "";

            return (
              <div
                key={idx}
                className={`h-full ${color} ${borderClass} flex items-center justify-center text-xs font-bold text-white`}
                style={{
                  width: `${share.percentage}%`,
                }}
              >
                {share.percentage > 10 ? `${share.percentage}%` : ""}
              </div>
            );
          })}

          {/* Remaining space if less than 100% */}
          {combinedTotalPercentage < 100 && (
            <div
              className="flex h-full items-center justify-center bg-gray-300 text-xs font-bold text-white dark:bg-gray-600"
              style={{ width: `${100 - combinedTotalPercentage}%` }}
            >
              {100 - combinedTotalPercentage > 10
                ? `${100 - combinedTotalPercentage}%`
                : ""}
            </div>
          )}
        </div>

        {/* Display the remaining percentage info below the visualization */}
        {(formData.ourPercentage || 0) < 100 && (
          <p className="mt-1 text-right text-xs text-gray-500">
            {t("remainingForPartners")}: {100 - (formData.ourPercentage || 0)}%
          </p>
        )}

        {percentageError && (
          <div className="mt-2 flex items-center text-sm text-red-500">
            <AlertCircle className="mr-1 h-4 w-4" />
            {percentageError}
          </div>
        )}
      </div>

      {/* Partners Section */}
      <div>
        <div className="mb-3 flex items-center justify-between">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {isEgyCommPrimaryOwner
              ? t("ownershipPartners")
              : t("additionalPartners")}
          </label>
          {combinedTotalPercentage !== 100 && (
            <span className="text-xs text-red-500">
              {t("percentageMustEqual100")}
            </span>
          )}
        </div>

        {/* Existing partners list */}
        {ownershipShares.length > 0 ? (
          <div className="mb-4 space-y-3">
            {ownershipShares.map((share, idx) => {
              const isPrimaryOwner =
                !isEgyCommPrimaryOwner && share.name === formData.ownedBy;

              return (
                <div
                  key={idx}
                  className={`flex items-center gap-3 rounded-lg p-3 ${
                    isPrimaryOwner
                      ? "border border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20"
                      : "bg-gray-50 dark:bg-gray-800/50"
                  }`}
                >
                  <div className="flex-1">
                    <p className="font-medium">{share.name}</p>
                    {isPrimaryOwner && (
                      <p className="text-xs text-blue-600 dark:text-blue-400">
                        {t("primaryOwner")}
                      </p>
                    )}
                  </div>
                  <div className="w-24">
                    <div className="relative">
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={share.percentage}
                        onChange={(e) =>
                          updatePartnerPercentage(
                            idx,
                            parseFloat(parseFloat(e.target.value).toFixed(2)) || 0,
                          )
                        }
                        className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                      />
                      <Percent className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      const updatedShares = ownershipShares.filter(
                        (_, i) => i !== idx,
                      );
                      setOwnershipShares(updatedShares);
                    }}
                    className={`rounded-full p-1 ${
                      isPrimaryOwner && !isEgyCommPrimaryOwner
                        ? "cursor-not-allowed opacity-50"
                        : "text-red-500 hover:bg-red-50 hover:text-red-700"
                    }`}
                    disabled={isPrimaryOwner && !isEgyCommPrimaryOwner}
                    title={
                      isPrimaryOwner
                        ? t("cannotRemovePrimaryOwner")
                        : t("removePartner")
                    }
                  >
                    <Trash className="h-4 w-4" />
                  </button>
                </div>
              );
            })}
          </div>
        ) : (
          <p className="mb-4 text-sm text-gray-500">
            {isEgyCommPrimaryOwner
              ? t("noPartnersAdded")
              : t("selectPrimaryOwnerFirst")}
          </p>
        )}

        {/* Add new partner form - Modified to adjust percentages automatically */}
        <div className="flex flex-col gap-3 md:flex-row">
          <div className="flex-1">
            <ContactSelectDropdown
              options={contactOptions.filter(
                (contact) =>
                  !ownershipShares.some((share) => share.name === contact),
              )}
              value={newPartnerShare.name}
              onSelect={(name: string) =>
                setNewPartnerShare({ ...newPartnerShare, name })
              }
              placeholder={t("searchOrSelectPartner")}
              disabled={!isEgyCommPrimaryOwner && !formData.ownedBy}
            />
          </div>
          <div className="w-24">
            <div className="relative">
              <input
                type="number"
                min="0"
                max={100 - (formData.ourPercentage || 0)}
                step="0.01"
                value={newPartnerShare.percentage}
                onChange={(e) =>
                  setNewPartnerShare({
                    ...newPartnerShare,
                    percentage: parseFloat(parseFloat(e.target.value).toFixed(2)) || 0,
                  })
                }
                placeholder="%"
                className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                disabled={!isEgyCommPrimaryOwner && !formData.ownedBy}
              />
              <Percent className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            </div>
          </div>
          <button
            type="button"
            onClick={() => {
              if (newPartnerShare.name.trim() === "") return;

              // Create new partner with specified percentage
              const newPartner = {
                name: newPartnerShare.name.trim(),
                percentage: newPartnerShare.percentage || 0,
              };

              // Add to ownership shares
              const updatedShares = [...ownershipShares, newPartner];
              setOwnershipShares(updatedShares);

              // Update sharedWith in formData (this would call to parent)
              handleChange("sharedWith", [...sharedWith, newPartner.name]);

              // Reset the input fields
              setNewPartnerShare({ name: "", percentage: 0 });
            }}
            className={`rounded-lg px-4 py-2 transition-colors ${
              newPartnerShare.name.trim() === "" ||
              (!isEgyCommPrimaryOwner && !formData.ownedBy)
                ? "cursor-not-allowed bg-gray-300 text-gray-600 dark:bg-gray-700 dark:text-gray-400"
                : "bg-blue-500 text-white hover:bg-blue-600"
            }`}
            disabled={
              newPartnerShare.name.trim() === "" ||
              (!isEgyCommPrimaryOwner && !formData.ownedBy)
            }
          >
            <Plus className="hidden h-4 w-4 md:mr-1.5 md:inline-block" />
            <span>{t("addPartner")}</span>
          </button>
        </div>

        {/* Helper text showing available percentage */}
        <div className="mt-2 text-sm text-gray-500">
          {t("availablePercentage")}:{" "}
          {Math.max(0, 100 - combinedTotalPercentage)}%
        </div>
      </div>

      <div className="mt-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("summary")}
        </h4>
        <ul className="mt-2 space-y-1 text-sm">
          <li className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">
              {t("primaryOwner")}:
            </span>
            <span className="font-medium">
              {isEgyCommPrimaryOwner
                ? "EgyComm"
                : formData.ownedBy || t("notSet")}
            </span>
          </li>
          <li className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">
              {t("egyCommPercentage")}:
            </span>
            <span className="font-medium">{formData.ourPercentage || 0}%</span>
          </li>
          <li className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">
              {t("totalPartnersPercentage")}:
            </span>
            <span className="font-medium">{totalPercentage}%</span>
          </li>
          <li className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">
              {t("combinedTotal")}:
            </span>
            <span
              className={`font-medium ${combinedTotalPercentage !== 100 ? "text-red-500" : "text-green-500"}`}
            >
              {combinedTotalPercentage}%
              {combinedTotalPercentage === 100 ? " ✓" : ""}
            </span>
          </li>
          <li className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">
              {t("partners")}:
            </span>
            <span className="font-medium">{ownershipShares.length}</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default OwnershipTab;

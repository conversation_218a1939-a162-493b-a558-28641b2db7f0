import { ReservationHistory } from '@/lib/types/ReservationHistory';

export const mockReservationHistory: ReservationHistory[] = [
  {
    id: '1',
    locationId: 'loc_1',
    locationName: 'Downtown Cairo Office',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    totalAmount: 150000,
    ourPercentage: 75,
    agencyPercentage: 15,
    status: 'ongoing',
    payments: [
      {
        date: new Date('2024-01-01'),
        amount: 37500,
        type: 'instalment',
        status: 'paid'
      },
      {
        date: new Date('2024-04-01'),
        amount: 37500,
        type: 'instalment',
        status: 'paid'
      },
      {
        date: new Date('2024-07-01'),
        amount: 37500,
        type: 'instalment',
        status: 'pending'
      },
      {
        date: new Date('2024-10-01'),
        amount: 37500,
        type: 'final',
        status: 'pending'
      }
    ]
  },
  {
    id: '2',
    locationId: 'loc_2',
    locationName: 'Alexandria Warehouse',
    startDate: new Date('2023-06-01'),
    endDate: new Date('2023-12-31'),
    totalAmount: 80000,
    ourPercentage: 60,
    agencyPercentage: 10,
    status: 'completed',
    payments: [
      {
        date: new Date('2023-06-01'),
        amount: 40000,
        type: 'instalment',
        status: 'paid'
      },
      {
        date: new Date('2023-09-01'),
        amount: 40000,
        type: 'final',
        status: 'paid'
      }
    ]
  },
  {
    id: '3',
    locationId: 'loc_3',
    locationName: 'Giza Retail Space',
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-03-31'),
    totalAmount: 45000,
    ourPercentage: 80,
    agencyPercentage: 12,
    status: 'cancelled',
    payments: [
      {
        date: new Date('2023-01-01'),
        amount: 15000,
        type: 'instalment',
        status: 'paid'
      },
      {
        date: new Date('2023-02-01'),
        amount: 15000,
        type: 'instalment',
        status: 'overdue'
      }
    ]
  }
];
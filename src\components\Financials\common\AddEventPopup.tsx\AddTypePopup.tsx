import React, { useState } from "react";
import { Button } from '@/components/ui/button';
import useLanguage from "@/hooks/useLanguage";

interface AddTypePopupProps {
    onClose: () => void;
    onSave: (type: string) => void;
}

const AddTypePopup: React.FC<AddTypePopupProps> = ({ onClose, onSave }) => {
    const { t } = useLanguage();
    const [newType, setNewType] = useState("");

    const handleSave = () => {
        if (newType) {
            onSave(newType);
        }
    };

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
            <div className="bg-white dark:bg-boxdark p-8 rounded-lg shadow-lg relative w-full max-w-md mx-4">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-6">{t("Add New Type")}</h2>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t("Type")}</label>
                    <input
                        type="text"
                        value={newType}
                        onChange={(e) => setNewType(e.target.value)}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
                    />
                </div>
                <div className="flex justify-end gap-4">
                    <Button variant="outline" className="px-4 py-2 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700" onClick={onClose}>{t("Cancel")}</Button>
                    <Button variant="default" className="px-4 py-2 bg-blue-500 text-white rounded-lg shadow-sm hover:bg-blue-600" onClick={handleSave}>{t("Save")}</Button>
                </div>
            </div>
        </div>
    );
};

export default AddTypePopup;